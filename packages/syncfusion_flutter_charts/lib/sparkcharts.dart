/// Syncfusion Flutter Spark/Micro charts are light weight chart, typically drawn without axes or coordinates.
/// It presents the general shape of data’s in a simple and highly condensed way.
///
/// To use, import `package:syncfusion_flutter_charts/sparkcharts.dart`.
///
/// See also:
/// * [Syncfusion Flutter Charts product page](https://www.syncfusion.com/flutter-widgets/flutter-spark-charts)
/// * [User guide documentation](https://help.syncfusion.com/flutter/sparkcharts/overview)
/// * [Knowledge base](https://www.syncfusion.com/kb/flutter/sfsparklinechart)

library sparkcharts;

// export spark chart library

export 'src/sparkline/marker.dart';
export 'src/sparkline/plot_band.dart';
export 'src/sparkline/series/spark_area_base.dart';
export 'src/sparkline/series/spark_bar_base.dart';
export 'src/sparkline/series/spark_line_base.dart';
export 'src/sparkline/series/spark_win_loss_base.dart';
export 'src/sparkline/trackball/spark_chart_trackball.dart';
export 'src/sparkline/utils/enum.dart';
