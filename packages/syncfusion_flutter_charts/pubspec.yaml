name: syncfusion_flutter_charts
description: A Flutter Charts library which includes data visualization widgets such as cartesian and circular charts, to create real-time, interactive, high-performance, animated charts.
version: 22.2.12
homepage: https://github.com/syncfusion/flutter-widgets/tree/master/packages/syncfusion_flutter_charts

environment:
  sdk: '>=2.17.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  intl: ^0.19.0
  vector_math: ">=2.1.0 <=3.0.0"
  syncfusion_flutter_core: ^22.2.12

flutter:
