library xr_helper;

//! ================================ Imports ================================
import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:another_flushbar/flushbar.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gap/gap.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/src/data/remote/response/api_messages.dart';
import 'package:xr_helper/src/shared_widgets/text_fields/base_search_sheet.dart';

//! ================================ Base View Model ================================
part 'src/base_functions/base_repo.dart';
part 'src/base_functions/base_view_model.dart';
//! ================================ Data ================================
//? Local ================================
part 'src/data/local/get_storage_service.dart';
part 'src/data/local/keys/local_keys.dart';
part 'src/data/remote/app_exception.dart';
part 'src/data/remote/helpers/network_mixin_helper.dart';
part 'src/data/remote/models/post_image_model.dart';
//? Remote ================================
part 'src/data/remote/network/base_api_service.dart';
part 'src/data/remote/network/network_api_service.dart';
//! ================================ Extensions ================================
part 'src/extensions/context_extensions.dart';
part 'src/extensions/date_time_extensions.dart';
part 'src/extensions/string_extensions.dart';
part 'src/extensions/widget_extensions.dart';
//! ================================ Resources ================================
part 'src/resources/app_radius.dart';
part 'src/resources/app_spaces.dart';
part 'src/resources/theme/color_manager.dart';
part 'src/resources/theme/font_styles.dart';
part 'src/resources/theme/theme_manager.dart';
//! ================================ Services ================================
part 'src/services/notifications_service.dart';
//! ================================ Shared Widgets ================================
part 'src/shared_widgets/animated/widget_animator.dart';
part 'src/shared_widgets/buttons/button.dart';
part 'src/shared_widgets/dialog/base_dialog.dart';
part 'src/shared_widgets/text_fields/date_picker.dart';
part 'src/shared_widgets/text_fields/drop_down_field.dart';
part 'src/shared_widgets/text_fields/search_drop_down.dart';
part 'src/shared_widgets/text_fields/text_field.dart';
//! ================================ Utils ================================
part 'src/utils/enum_values.dart';
part 'src/utils/flush_bar.dart';
part 'src/utils/logger.dart';
part 'src/utils/validations.dart';
