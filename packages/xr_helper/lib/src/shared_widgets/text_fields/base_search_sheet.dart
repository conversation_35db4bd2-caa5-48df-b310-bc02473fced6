import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

const _radius = Radius.circular(12);

class BaseSearchSheet extends HookWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final bool isRequired;
  final bool isMultiSelect;
  final bool ignoring;
  final String? ignoringMessage;
  final Function(dynamic)? itemModelAsName;
  final Function(dynamic)? multiItemsAsName;
  final bool isEng;

  const BaseSearchSheet({
    super.key,
    required this.onChanged,
    required this.data,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
    this.isMultiSelect = false,
    this.isEng = true,
    this.ignoring = false,
    this.itemModelAsName,
    this.multiItemsAsName,
    this.ignoringMessage,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    log('asfasfs ${selectedValue}');

    return Material(
      borderRadius: isDropdownOpen.value
          ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
          : BorderRadius.circular(12),
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showBottomSheet(context, isDropdownOpen),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  selectedValue != null &&
                          ((isMultiSelect &&
                                  selectedValue is List &&
                                  selectedValue.isNotEmpty) ||
                              (!isMultiSelect &&
                                  selectedValue.toString().isNotEmpty))
                      ? isMultiSelect
                          ? (multiItemsAsName != null
                              ? multiItemsAsName!(selectedValue)
                              : selectedValue.toString())
                          : (itemModelAsName != null
                              ? itemModelAsName!(selectedValue)
                              : selectedValue.toString())
                      : '$label',
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ),
              if (icon != null) icon!,
              const Icon(Icons.arrow_drop_down),
            ],
          ),
        ),
      ),
    );
  }

  void _showBottomSheet(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    isDropdownOpen.value = true;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: _radius),
      ),
      builder: (context) {
        return _buildBottomSheetContent(context, isDropdownOpen);
      },
    ).whenComplete(() => isDropdownOpen.value = false);
  }

  Widget _buildBottomSheetContent(
      BuildContext context, ValueNotifier<bool> isDropdownOpen) {
    return HookBuilder(builder: (context) {
      final searchController = useTextEditingController();
      final filteredData = useState(data);
      final selectedItems = useState<List>(isMultiSelect
          ? (selectedValue ?? [])
          : (selectedValue != null ? [selectedValue] : []));

      useEffect(() {
        searchController.addListener(() {
          final query = searchController.text.toLowerCase();
          filteredData.value = data.where((item) {
            final itemName = itemModelAsName != null
                ? itemModelAsName!(item)
                : item.toString();
            return itemName.toLowerCase().contains(query);
          }).toList();
        });
        return null;
      }, [searchController]);

      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: searchController,
              style: const TextStyle(color: Colors.black),
              decoration: InputDecoration(
                hintText: isEng ? "Search" : "بحث",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: filteredData.value.length,
              itemBuilder: (context, index) {
                final item = filteredData.value[index];
                final isSelected = selectedItems.value.contains(item);

                return ListTile(
                  leading: isMultiSelect
                      ? Icon(
                          isSelected ? Icons.check_circle : Icons.circle,
                          color: isSelected
                              ? ColorManager.primaryColor
                              : Colors.grey,
                          size: 20,
                        )
                      : null,
                  selected: isSelected,
                  title: Text(itemModelAsName != null
                      ? itemModelAsName!(item)
                      : item.toString()),
                  onTap: () {
                    if (isMultiSelect) {
                      if (isSelected) {
                        selectedItems.value = List.from(selectedItems.value)
                          ..remove(item);
                      } else {
                        selectedItems.value = List.from(selectedItems.value)
                          ..add(item);
                      }
                      onChanged?.call(selectedItems.value);
                    } else {
                      onChanged?.call(item);
                      Navigator.pop(context); // Close the bottom sheet
                    }
                  },
                );
              },
            ),
          ),
        ],
      );
    });
  }
}
// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// const _radius = Radius.circular(12);
//
// class BaseSearchSheet extends HookWidget {
//   final dynamic selectedValue;
//   final String? label;
//   final List<dynamic> data;
//   final void Function(dynamic)? onChanged;
//   final Widget? icon;
//   final bool isRequired;
//   final bool isMultiSelect;
//   final bool ignoring;
//   final String? ignoringMessage;
//   final Function(dynamic)? itemModelAsName;
//   final Function(dynamic)? multiItemsAsName;
//   final bool isEng;
//
//   const BaseSearchSheet({
//     super.key,
//     required this.onChanged,
//     required this.data,
//     required this.label,
//     required this.selectedValue,
//     this.isRequired = true,
//     this.isMultiSelect = false,
//     this.isEng = true,
//     this.ignoring = false,
//     this.itemModelAsName,
//     this.multiItemsAsName,
//     this.ignoringMessage,
//     this.icon,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final isDropdownOpen = useState(false);
//
//     return Material(
//       borderRadius: isDropdownOpen.value
//           ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
//           : BorderRadius.circular(12),
//       color: Colors.transparent,
//       child: InkWell(
//         onTap: () => _showBottomSheet(context, isDropdownOpen),
//         child: Container(
//           padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(12),
//             border: Border.all(color: Colors.grey),
//           ),
//           child: Row(
//             children: [
//               Expanded(
//                 child: Text(
//                   selectedValue != null
//                       ? multiItemsAsName != null
//                           ? multiItemsAsName!(selectedValue)
//                           : selectedValue.toString()
//                       : '$label',
//                   style: const TextStyle(
//                     fontSize: 16,
//                   ),
//                 ),
//               ),
//               if (icon != null) icon!,
//               const Icon(Icons.arrow_drop_down),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   void _showBottomSheet(
//       BuildContext context, ValueNotifier<bool> isDropdownOpen) {
//     isDropdownOpen.value = true;
//     showModalBottomSheet(
//       context: context,
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.vertical(top: _radius),
//       ),
//       builder: (context) {
//         return _buildBottomSheetContent(context, isDropdownOpen);
//       },
//     ).whenComplete(() => isDropdownOpen.value = false);
//   }
//
//   Widget _buildBottomSheetContent(
//       BuildContext context, ValueNotifier<bool> isDropdownOpen) {
//     return HookBuilder(builder: (context) {
//       final searchController = useTextEditingController();
//       log('asfsafsaf ${data}');
//
//       final filteredData = useState(data);
//       final selectedItems = useState<List>(selectedValue ?? []);
//
//       useEffect(() {
//         searchController.addListener(() {
//           final query = searchController.text.toLowerCase();
//           filteredData.value = data.where((item) {
//             final itemName = itemModelAsName != null
//                 ? itemModelAsName!(item)
//                 : item.toString();
//             return itemName.toLowerCase().contains(query);
//           }).toList();
//         });
//         return null;
//       }, [searchController]);
//
//       return Column(
//         children: [
//           Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: TextField(
//               controller: searchController,
//               style: const TextStyle(color: Colors.white),
//               decoration: InputDecoration(
//                 hintText: isEng ? "Search" : "بحث",
//                 border: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(12),
//                 ),
//               ),
//             ),
//           ),
//           Expanded(
//             child: ListView.builder(
//               itemCount: filteredData.value.length,
//               itemBuilder: (context, index) {
//                 final item = filteredData.value[index];
//                 final isSelected = selectedItems.value.contains(item);
//                 return ListTile(
//                   leading: isSelected
//                       ? const Icon(Icons.check_circle,
//                           color: ColorManager.primaryColor, size: 12)
//                       : const Icon(Icons.circle, color: Colors.grey, size: 12),
//                   selected: isSelected,
//                   title: Text(itemModelAsName != null
//                       ? itemModelAsName!(item)
//                       : item.toString()),
//                   onTap: () {
//                     if (isSelected) {
//                       selectedItems.value = List.from(selectedItems.value)
//                         ..remove(item);
//                     } else {
//                       selectedItems.value = List.from(selectedItems.value)
//                         ..add(item);
//                     }
//                     if (onChanged != null) {
//                       onChanged!(selectedItems.value);
//                     }
//                     // onChanged?.call(selectedItems.value);
//                   },
//                 );
//               },
//             ),
//           ),
//         ],
//       );
//     });
//   }
// }
