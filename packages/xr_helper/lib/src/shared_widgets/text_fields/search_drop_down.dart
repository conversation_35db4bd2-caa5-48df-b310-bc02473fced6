part of xr_helper;

const _radius = Radius.circular(12);

class BaseSearchDropDown extends HookWidget {
  final dynamic selectedValue;
  final String? label;
  final String? title;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final bool isRequired;
  final bool isMultiSelect;
  final bool ignoring;
  final String? ignoringMessage;
  final Function(dynamic)? itemModelAsName;
  final Function(dynamic)? multiItemsAsName;
  final bool isEng;

  const BaseSearchDropDown(
      {super.key,
      required this.onChanged,
      required this.data,
      required this.label,
      required this.selectedValue,
      this.isRequired = true,
      this.isMultiSelect = false,
      this.ignoring = false,
      this.title,
      this.itemModelAsName,
      this.multiItemsAsName,
      this.ignoringMessage,
      this.isEng = true,
      this.icon});

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return BaseSearchSheet(
      label: label,
      isEng: isEng,
      data: data,
      selectedValue: selectedValue,
      onChanged: onChanged,
      isRequired: isRequired,
      isMultiSelect: isMultiSelect,
      ignoring: ignoring,
      ignoringMessage: ignoringMessage,
      itemModelAsName: itemModelAsName,
      multiItemsAsName: multiItemsAsName,
      icon: icon,
    );

    return Material(
      borderRadius: isDropdownOpen.value
          ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
          : BorderRadius.circular(12),
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null) ...[
            Text(
              title!,
              style: context.subTitle,
            ),
            context.smallGap,
          ],
          isMultiSelect
              ? _multiSelect(context, isDropdownOpen: isDropdownOpen)
              : _singleSelect(context, isDropdownOpen: isDropdownOpen),
        ],
      ),
    );
  }

  Widget _singleSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    return DropdownSearch(
      onBeforePopupOpening: (controller) {
        isDropdownOpen.value = true;
        return Future.value(true);
      },
      onBeforeChange: (val, value) {
        isDropdownOpen.value = false;
        return Future.value(true);
      },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null && selectedValue == null && isRequired) {
          return '${isEng ? 'Please select' : 'يرجى تحديد'} $label';
        }
        return null;
      },
      selectedItem: selectedValue,
      popupProps: PopupProps.menu(
        onDismissed: () {
          isDropdownOpen.value = false;
        },
        menuProps: MenuProps(
          elevation: 2,
          backgroundColor: Colors.white,
          borderRadius: !isDropdownOpen.value
              ? const BorderRadius.only(
                  bottomLeft: _radius,
                  bottomRight: _radius,
                )
              : BorderRadius.circular(12),
        ),
        itemBuilder: (context, data, isSelected) {
          return Column(
            children: [
              ListTile(
                selected: isSelected,
                title: Text(
                  itemModelAsName != null
                      ? itemModelAsName!(data)
                      : data.toString(),
                ),
              ),
              const Divider(
                thickness: .4,
              ),
            ],
          );
        },
        isFilterOnline: false,
        showSelectedItems: false,
        searchFieldProps: TextFieldProps(
          decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.smallPadding),
              hintText: isEng ? "Search" : "بحث"),
        ),
        showSearchBox: true,
      ),
      dropdownBuilder: (context, value) {
        if (value == null && selectedValue == null) {
          return Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              isEng ? 'Select $label' : 'اختر $label',
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            itemModelAsName != null
                ? itemModelAsName!(value)
                // : '${value ?? selectedValue ?? 'Select $label'}',
                : '${value ?? selectedValue ?? (isEng ? 'Select $label' : 'اختر $label')}',
          ),
        );
      },
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(
            vertical: 12,
            horizontal: icon == null ? 16 : 0,
          ),
          icon: icon,
          border: InputBorder.none,
          labelText: label,
          labelStyle: context.labelMedium.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: ColorManager.fieldColor.withOpacity(0.3),
        ),
      ),
      items: data,
      onChanged: onChanged,
    );
  }

  Widget _multiSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    return Theme(
      // change ok button color
      data: ThemeData(
        primaryColor: context.appTheme.primaryColor,
        // border radius
      ),
      child: ClipRRect(
        borderRadius: isDropdownOpen.value
            ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
            : BorderRadius.circular(12),
        child: DropdownSearch.multiSelection(
          onBeforePopupOpening: (controller) {
            isDropdownOpen.value = true;
            return Future.value(true);
          },
          onBeforeChange: (val, value) {
            isDropdownOpen.value = false;
            return Future.value(true);
          },
          autoValidateMode: AutovalidateMode.onUserInteraction,
          validator: (value) {
            if (value == null && selectedValue == null && isRequired) {
              return '${isEng ? 'Please select' : 'يرجى تحديد'} $label';
            }
            return null;
          },
          popupProps: PopupPropsMultiSelection.menu(
            onDismissed: () {
              isDropdownOpen.value = false;
            },
            menuProps: MenuProps(
              elevation: 2,
              backgroundColor: context.appTheme.cardColor,
              borderRadius: !isDropdownOpen.value
                  ? const BorderRadius.only(
                      bottomLeft: _radius,
                      bottomRight: _radius,
                    )
                  : BorderRadius.circular(12),
            ),
            itemBuilder: (context, data, isSelected) {
              return Column(
                children: [
                  ListTile(
                    selected: isSelected,
                    title: Text(
                      itemModelAsName != null
                          ? itemModelAsName!(data)
                          : data.toString(),
                    ),
                  ),
                  const Divider(
                    thickness: .4,
                  ),
                ],
              );
            },
            isFilterOnline: false,
            showSelectedItems: false,
            searchFieldProps: TextFieldProps(
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: isEng ? "Search" : "بحث",
                contentPadding:
                    EdgeInsets.symmetric(horizontal: AppSpaces.smallPadding),
              ),
            ),
            showSearchBox: true,
          ),
          dropdownBuilder: (context, value) {
            if (selectedValue == null || selectedValue.isEmpty) {
              return Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  isEng ? 'Select $label' : 'اختر $label',
                ),
              );
            } else if (selectedValue.isNotEmpty) {
              return Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  multiItemsAsName != null
                      ? multiItemsAsName!(selectedValue)
                      : selectedValue.toString(),
                ),
              );
            }

            return Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                multiItemsAsName != null ? multiItemsAsName!(value) : '$value',
              ),
            );
          },
          dropdownDecoratorProps: DropDownDecoratorProps(
            dropdownSearchDecoration: InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                vertical: 12,
                horizontal: icon == null ? 16 : 0,
              ),
              icon: icon,
              border: InputBorder.none,
              labelText: label,
              labelStyle: context.labelMedium.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              filled: true,
              fillColor: ColorManager.fieldColor.withOpacity(0.3),
            ),
          ),
          items: data,
          onChanged: onChanged,
        ),
      ),
    );
  }
}
