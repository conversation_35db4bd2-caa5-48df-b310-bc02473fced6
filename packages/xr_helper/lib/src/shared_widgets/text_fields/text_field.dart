part of xr_helper;

class BaseTextField extends StatelessWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextInputType textInputType;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final TextAlign textAlign;
  final Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? icon;
  final Widget? suffixIcon;
  final String? label;
  final String? hint;
  final int maxLines;
  final bool isWhiteText;
  final String? ignoringMessage;
  final String? Function(String?)? validator;
  final bool isObscure;
  final bool isRequired;
  final String? initialValue;
  final bool enabled;
  final bool readOnly;
  final bool unFocus;
  final bool enableToCopy;
  final String? title;

  const BaseTextField(
      {super.key,
      this.ignoringMessage,
      this.focusNode,
      this.controller,
      this.isObscure = false,
      this.unFocus = true,
      this.enableToCopy = true,
      this.onTap,
      this.hint,
      this.icon,
      this.suffixIcon,
      this.label,
      this.onChanged,
      this.onSubmitted,
      this.initialValue,
      this.textAlign = TextAlign.start,
      this.contentPadding = const EdgeInsets.all(AppSpaces.mediumPadding),
      this.textInputType = TextInputType.text,
      this.maxLines = 1,
      this.isWhiteText = false,
      this.isRequired = true,
      this.enabled = true,
      this.readOnly = false,
      this.validator,
      this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Row(
            children: [
              Text(
                title!,
                style: context.subTitle,
              ),
              if (isRequired)
                Text(
                  ' *',
                  style: context.labelLarge.copyWith(
                      color: Colors.red,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
            ],
          ),
          context.smallGap,
        ],

        //! Text Field
        _textField(context),
      ],
    );
  }

  Widget _textField(BuildContext context) {
    String? validations(value) {
      if (textInputType == TextInputType.number) {
        return Validations.numbersOnly(value, isEng: context.isEng);
      } else if (textInputType == TextInputType.emailAddress) {
        return Validations.email(value, isEng: context.isEng);
      } else if (textInputType == TextInputType.phone) {
        return Validations.phoneNumber(value, isEng: context.isEng);
      }

      return Validations.mustBeNotEmpty(value, isEng: context.isEng);
    }

    return TextFormField(
      scrollPadding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom +
              AppSpaces.mediumPadding),
      style: isWhiteText ? context.whiteLabelLarge : null,
      onTapOutside: (e) => unFocus ? FocusScope.of(context).unfocus() : null,
      focusNode: focusNode,
      obscureText: isObscure,
      enableInteractiveSelection: enableToCopy,
      enabled: enabled,
      readOnly: readOnly,
      controller: controller,
      keyboardType: textInputType,
      inputFormatters: [
        if (textInputType == TextInputType.number)
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]'))
      ],
      textAlign: textAlign,
      onTap: onTap,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      initialValue: initialValue,
      maxLines: maxLines,
      maxLength: textInputType == TextInputType.phone ? 11 : null,
      validator: isRequired ? (validator ?? validations) : null,
      decoration: InputDecoration(
        hintText: hint ?? 'Enter $title',
        hintStyle: context.greyLabelMedium,
        labelStyle: isWhiteText ? context.whiteLabelLarge : context.labelLarge,
        contentPadding: contentPadding,
        labelText: label,
        suffixIcon: suffixIcon,
        prefixIcon: icon != null
            ? Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.mediumPadding,
                    vertical: AppSpaces.smallPadding),
                child: icon,
              )
            : null,
      ),
    );
  }
}
