part of xr_helper;

class UploadImageModel {
  String? ref;
  String? refId;
  String? field;

  UploadImageModel({
    this.ref,
    this.refId,
    this.field,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (ref != null) data['ref'] = ref;
    if (refId != null) data['refId'] = refId;
    if (field != null) data['field'] = field;
    return data;
  }
}
