part of xr_helper;

abstract class BaseApiServices with XNetworkHelper {
  Future<dynamic> getResponse(String url);

  Future<dynamic> postResponse(String url,
      {required Map<String, dynamic> body,
      bool fromAuth = false,
      bool isNormalMethod = false,
      List<String> filePaths = const [],
      String? fieldName});

  Future<dynamic> putResponse(
    String url, {
    required Map<String, dynamic> data,
    bool fromAuth = false,
    bool isNormalMethod = false,
    List<String> filePaths = const [],
    String? fieldName,
  });

  Future<dynamic> deleteResponse(String url);

  Future<dynamic> uploadFile(
      {required String filePath, UploadImageModel? uploadImageModel});

  Future<List<dynamic>?> uploadFiles({
    required List<String?> filePaths,
    UploadImageModel? uploadImageModel,
  });

  Future<dynamic> deleteImage(String imageId);
}
