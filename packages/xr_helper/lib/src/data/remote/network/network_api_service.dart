part of xr_helper;

class NetworkApiServices extends BaseApiServices {
  static const _timeOutDuration = Duration(seconds: 120);

  // * Get request ================================
  @override
  Future<dynamic> getResponse(String url) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.i('GetApiUrl => $apiUrl');

      final response =
          await http.get(apiUrl, headers: headers).timeout(_timeOutDuration);

      Log.f(
          'GetRes => ${response.body}\nStatus Code => ${response.statusCode}');

      // final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

      // Log.i('ccccccccccccccccc ${nursery['data']['id']}');

      // final nurseryId = nursery['data']['id'];

      responseJson = returnResponse(response);
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
    return responseJson;
  }

  // * Post request ================================
  @override
  Future postResponse(String url,
      {required Map<String, dynamic> body,
      List<String> filePaths = const [],
      bool fromAuth = false,
      bool isNormalMethod = false,
      String? fieldName}) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.w(
          'PostApiUrl => $apiUrl\n 💾💾💾 PostData -> $body 💾💾💾 & filePaths => $filePaths');

      late http.Response response;

      if (isNormalMethod) {
        final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

        int? nurseryId;

        if (nursery.containsKey('data')) {
          nurseryId = nursery['data']['id'];
        } else {
          nurseryId = nursery['id'];
        }

        body['nursery'] = nurseryId;

        response = await http
            .post(
              apiUrl,
              headers: headers,
              body: jsonEncode(body),
            )
            .timeout(_timeOutDuration);
      } else {
        response = await _postOrPutAndUploadFiles(
          apiUrl,
          body: body,
          filePaths: filePaths,
          fieldName: fieldName,
          fromAuth: fromAuth,
          method: 'POST',
        );
      }

      Log.f(
          'PostBodyData => $body, PostRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(response.body);

        return responseJson;
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }

  // * Put request ================================
  @override
  Future putResponse(
    String url, {
    required Map<String, dynamic> data,
    List<String> filePaths = const [],
    String? fieldName,
    bool fromAuth = false,
    bool isNormalMethod = false,
  }) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.w(
          'PutApiUrl => $apiUrl\n 💾💾💾 PutData -> $data 💾💾💾 & filePaths => $filePaths');

      late http.Response response;

      if (isNormalMethod) {
        response = await http
            .put(
              apiUrl,
              headers: headers,
              body: jsonEncode(data),
            )
            .timeout(_timeOutDuration);
      } else {
        response = await _postOrPutAndUploadFiles(
          apiUrl,
          body: data,
          filePaths: filePaths,
          method: 'PUT',
          fieldName: fieldName,
          fromAuth: fromAuth,
        );
      }

      Log.f(
          'PutRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(response.body);
        return responseJson;
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }

  // * Post Or Put & Upload File request ================================
  Future _postOrPutAndUploadFiles(Uri url,
      {required Map<String, dynamic> body,
      required List<String> filePaths,
      required String method,
      required String? fieldName,
      required bool fromAuth}) async {
    late http.Response response;

    http.MultipartRequest request = http.MultipartRequest(method, url);
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    int? nurseryId;

    if (nursery != null) {
      // final firstNursery =
      // nursery['data'] is List ? nursery['data'][0] : nursery['data'];

      // if (nursery != null) {
      // } else {
      //   nurseryId = nursery['id'];
      // }

      if (nursery.containsKey('data')) {
        nurseryId = nursery['data']['id'];
      } else {
        nurseryId = nursery['id'];
      }

      body['nursery'] = nurseryId;
    }

    //? Add headers
    request.headers.addAll(headers);

    if (fromAuth) {
      request.fields.addAll(
        body.map((key, value) => MapEntry(key, value.toString())),
      );
    } else {
      final encodedData = jsonEncode(body);

      request.fields.addAll({
        "data": encodedData,
      });
    }

    final filesNotEmpty =
        filePaths.where((element) => element.isNotEmpty).isNotEmpty;

    //? Add files
    if (filePaths.isNotEmpty && filesNotEmpty) {
      for (var image in filePaths) {
        request.files.add(await http.MultipartFile.fromPath(
            'files.${fieldName ?? 'image'}', image));
      }
    }

    response = await http.Response.fromStream(await request.send())
        .timeout(_timeOutDuration);

    Log.f('PostRes => ${response.body}\nStatus Code => ${response.statusCode}');

    return response;
  }

  // * Delete request ================================
  @override
  Future deleteResponse(String url) async {
    try {
      final apiUrl = Uri.parse(url);

      Log.i('DeleteApiUrl => $apiUrl');

      final response =
          await http.delete(apiUrl, headers: headers).timeout(_timeOutDuration);

      Log.f(
          'DeleteRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }

  //! Upload single file
  @override
  Future<dynamic> uploadFile(
      {required String filePath, UploadImageModel? uploadImageModel}) async {
    if (filePath.isEmpty) return;

    dynamic responseJson;
    try {
      const String uploadUrl =
          "https://connectify-rdlj3.ondigitalocean.app/api/upload";

      http.MultipartRequest request =
          http.MultipartRequest('POST', Uri.parse(uploadUrl));

      Log.i(
          'UploadFileFieldsModel -------------------> ${uploadImageModel?.toJson()}\n 💾💾💾 filePath -> $filePath');

      request.files.add(await http.MultipartFile.fromPath('files', filePath));

      if (uploadImageModel != null) {
        request.fields.addAll(
          uploadImageModel
              .toJson()
              .map((key, value) => MapEntry(key, value.toString())),
        );
      }

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      Log.i(
          'UploadFileFieldsModel -------------------> ${uploadImageModel?.toJson()}\n 💾💾💾 filePath -> $filePath 💾💾💾\n 💾💾💾 request -> ${await response.stream.bytesToString()} 💾💾💾');

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(await response.stream.bytesToString());
      } else {
        throw FetchDataException(
            'Error occurred while communication with server with status code : ${response.statusCode}');
      }
    } on SocketException {
      throw FetchDataException('No Internet Connection');
    }
    return responseJson;
  }

  //! Upload multiple files
  @override
  Future<List<dynamic>?> uploadFiles(
      {required List<String?> filePaths,
      UploadImageModel? uploadImageModel}) async {
    if (filePaths.isEmpty) return [];

    List<dynamic>? responseJson = [];
    try {
      const String uploadUrl =
          "https://connectify-rdlj3.ondigitalocean.app/api/upload";

      http.MultipartRequest request =
          http.MultipartRequest('POST', Uri.parse(uploadUrl));

      for (String? filePath in filePaths) {
        request.files
            .add(await http.MultipartFile.fromPath('files', filePath!));
      }

      Log.i(
          'UploadFilesFieldsModel -------------------> ${uploadImageModel?.toJson()}');

      if (uploadImageModel != null) {
        request.fields.addAll(
          uploadImageModel
              .toJson()
              .map((key, value) => MapEntry(key, value.toString())),
        );
      }

      request.headers.addAll(headers);

      http.StreamedResponse response = await request.send();

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(await response.stream.bytesToString());
      } else {
        throw FetchDataException(
            'Error occurred while communication with server with status code : ${response.statusCode}');
      }
    } on SocketException {
      throw FetchDataException('No Internet Connection');
    }
    return responseJson;
  }

  //! Delete Image From Media Library
  @override
  Future deleteImage(String imageId) async {
    dynamic responseJson;
    try {
      final String uploadUrl =
          "https://connectify-rdlj3.ondigitalocean.app/api/upload/files/$imageId";

      final response = await http.delete(
        Uri.parse(uploadUrl),
        headers: headers,
      );

      Log.e(
          'DeleteImageFromMediaLibrary -------------------> ${response.body}');

      responseJson = returnResponse(response);
    } on SocketException {
      throw FetchDataException('No Internet Connection');
    }
    return responseJson;
  }
}
