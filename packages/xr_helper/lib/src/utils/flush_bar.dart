part of xr_helper;

Flushbar showBarAlert(BuildContext context, String msg,
    {Color iconColor = Colors.white,
    IconData icon = Icons.check_circle_outline,
    bool isError = false,
    String? title,
    Color? color,
    int duration = 5,
    Function(Flushbar)? onTap}) {
  return Flushbar(
    backgroundColor: color ?? (isError ? Colors.red : ColorManager.buttonColor),
    title: title,
    message: msg,
    icon: Icon(isError ? Icons.error : icon, size: 28.0, color: iconColor),
    duration: Duration(seconds: duration),
    flushbarStyle: FlushbarStyle.FLOATING,
    flushbarPosition: FlushbarPosition.TOP,
    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    padding: const EdgeInsets.all(16),
    borderRadius: BorderRadius.circular(10),
    onTap: onTap,
  )..show(context);
}
