part of xr_helper;

class Validations {
  //! Password Validation
  static String? password(
    String? value, {
    String? emptyPasswordMessage = "Password can't be empty",
    String? passwordLengthMessage =
        "Password must be at least 8 characters long",
  }) {
    if (value == null || value.isEmpty) {
      return emptyPasswordMessage;
    } else if (value.length < 8) {
      return passwordLengthMessage;
    }
    return null;
  }

  //! Phone Number Validation
  static String? phoneNumber(value,
      {String? emptyPhoneMessage = "Phone number can't be empty",
      String? phoneLengthMessage = "Invalid phone number",
      bool isEng = false}) {
    String pattern = r'^\d{11}$';

    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return isEng ? "الحقل لا يمكن أن يكون فارغًا" : emptyPhoneMessage;
    } else if (!regExp.hasMatch(value)) {
      return isEng ? "رقم غير صالح" : phoneLengthMessage;
    }

    return null;
  }

//   static String? phoneNumber(value,
//       {String? emptyPhoneMessage = "Phone number can't be empty",
//       String? phoneLengthMessage = "Invalid phone number"}) {
//     String pattern = r'^\d{10,11}$';
//
//     RegExp regExp = RegExp(pattern);
//     if (value == null || value.isEmpty) {
//       return emptyPhoneMessage;
//     } else if (!regExp.hasMatch(value)) {
//       return phoneLengthMessage;
//     }
//
//     return null;
//   }

  //! Numbers Only Validation
  static String? numbersOnly(value,
      {String? emptyMessage = "Field can't be empty",
      String? invalidMessage = "Invalid number",
      bool isEng = false}) {
    String pattern = r'^\d+$';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return isEng ? "الحقل لا يمكن أن يكون فارغًا" : emptyMessage;
    } else if (!regExp.hasMatch(value)) {
      return isEng ? "رقم غير صالح" : invalidMessage;
    }
    return null;
  }

  //! Email Validation
  static String? email(
    String? value, {
    String? emptyEmailMessage = "Email can't be empty",
    String? invalidEmailMessage = "Invalid email",
    bool isEng = false,
  }) {
    final RegExp urlExp = RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    if (value == null || value.isEmpty) {
      return isEng ? "الحقل لا يمكن أن يكون فارغًا" : emptyEmailMessage;
    } else if (!urlExp.hasMatch(value)) {
      return isEng ? "البريد الإلكتروني غير صالح" : invalidEmailMessage;
    }
    return null;
  }

  //! Must Be Not Empty Validation
  static String? mustBeNotEmpty(String? value,
      {String? emptyMessage, bool isEng = false}) {
    if (value == null || value.isEmpty) {
      return isEng
          ? "الحقل لا يمكن أن يكون فارغًا"
          : (emptyMessage ?? "Field can't be empty");
    }
    return null;
  }
}
