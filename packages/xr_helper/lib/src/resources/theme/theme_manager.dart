part of xr_helper;

class ThemeManager {
  //? Out Line Border -------------------------------------
  OutlineInputBorder get _outLineBorder => OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppRadius.fieldRadius),
        borderSide: const BorderSide(color: ColorManager.grey),
      );

  //? None Input Border -------------------------------------
  InputDecorationTheme get noneInputBorder => const InputDecorationTheme(
      enabledBorder:
          OutlineInputBorder(borderSide: BorderSide(color: ColorManager.black)),
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none);

  //? App Bar Theme -------------------------------------
  get _appBarTheme => const AppBarTheme(
      backgroundColor: ColorManager.primaryColor,
      foregroundColor: Colors.white,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
          color: Colors.white, fontWeight: FontWeight.bold, fontSize: 22));

  get colorScheme => const ColorScheme.light().copyWith(
      primary: ColorManager.primaryColor,
      secondary: ColorManager.secondaryColor);

  //? Button Theme -------------------------------------
  get _buttonTheme => ElevatedButtonThemeData(
      style: ButtonStyle(
          shape: MaterialStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.baseRadius))),
          foregroundColor: const MaterialStatePropertyAll(Colors.white70),
          backgroundColor:
              const MaterialStatePropertyAll(ColorManager.secondaryColor)));

  //? Input Decoration Theme -------------------------------------
  get _inputDecorationTheme => InputDecorationTheme(
        prefixIconColor: ColorManager.iconColor,
        suffixIconColor: ColorManager.iconColor,
        iconColor: ColorManager.iconColor,
        border: _outLineBorder,
        enabledBorder: _outLineBorder,
        focusedBorder: _outLineBorder,
        errorBorder: _outLineBorder.copyWith(
            borderSide: const BorderSide(color: ColorManager.errorColor)),
        fillColor: ColorManager.fieldColor.withOpacity(0.3),
        filled: true,
      );

  //? Bottom Navigation Bar Theme -------------------------------------
  get _bottomNavigationBar => const BottomNavigationBarThemeData(
        elevation: 20,
        showSelectedLabels: true,
        backgroundColor: ColorManager.primaryColor,
        selectedIconTheme: IconThemeData(color: ColorManager.white),
        unselectedIconTheme: IconThemeData(color: ColorManager.white),
        selectedItemColor: ColorManager.white,
        unselectedItemColor: ColorManager.iconColor,
      );

  //? List Tile Theme
  final _listTile = const ListTileThemeData(
    iconColor: ColorManager.white,
  );

  //? Floating Action Button Theme
  final _floatingActionButtonThemeData = const FloatingActionButtonThemeData(
    backgroundColor: ColorManager.secondaryColor,
    foregroundColor: ColorManager.white,
  );

  //? Theme Data -------------------------------------
  ThemeData appTheme() => ThemeData(
        scaffoldBackgroundColor: ColorManager.secondaryColor,
        useMaterial3: true,
        brightness: Brightness.light,
        primaryColor: ColorManager.secondaryColor,
        textTheme: GoogleFonts.cairoTextTheme(),

        //! AppBar Theme
        appBarTheme: _appBarTheme,

        //! Color Scheme
        colorScheme: colorScheme,
        // iconButtonTheme: const IconButtonThemeData(
        //     style: ButtonStyle(
        //         iconColor: MaterialStatePropertyAll(ColorManager.black))
        // ),

        //! Button Theme
        elevatedButtonTheme: _buttonTheme,

        //! input decoration theme
        inputDecorationTheme: _inputDecorationTheme,

        //! Bottom Navigation Bar Theme
        bottomNavigationBarTheme: _bottomNavigationBar,

        //! Floating Action Button Theme
        floatingActionButtonTheme: _floatingActionButtonThemeData,

        //! List Tile Theme
        listTileTheme: _listTile,
      );
}
