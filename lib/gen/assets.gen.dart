/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsAnimatedGen {
  const $AssetsAnimatedGen();

  /// File path: assets/animated/edit.json
  String get edit => 'assets/animated/edit.json';

  /// File path: assets/animated/loading.json
  String get loading => 'assets/animated/loading.json';

  /// List of all assets
  List<String> get values => [edit, loading];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/add.png
  AssetGenImage get add => const AssetGenImage('assets/icons/add.png');

  /// File path: assets/icons/closeEye.png
  AssetGenImage get closeEye =>
      const AssetGenImage('assets/icons/closeEye.png');

  /// File path: assets/icons/dashboard.png
  AssetGenImage get dashboard =>
      const AssetGenImage('assets/icons/dashboard.png');

  /// File path: assets/icons/female.png
  AssetGenImage get female => const AssetGenImage('assets/icons/female.png');

  /// File path: assets/icons/home.png
  AssetGenImage get home => const AssetGenImage('assets/icons/home.png');

  /// File path: assets/icons/male.png
  AssetGenImage get male => const AssetGenImage('assets/icons/male.png');

  /// File path: assets/icons/mess.png
  AssetGenImage get mess => const AssetGenImage('assets/icons/mess.png');

  /// File path: assets/icons/message.png
  AssetGenImage get message => const AssetGenImage('assets/icons/message.png');

  /// File path: assets/icons/notification.png
  AssetGenImage get notification =>
      const AssetGenImage('assets/icons/notification.png');

  /// File path: assets/icons/phone.png
  AssetGenImage get phone => const AssetGenImage('assets/icons/phone.png');

  /// File path: assets/icons/search.png
  AssetGenImage get search => const AssetGenImage('assets/icons/search.png');

  /// File path: assets/icons/settings.png
  AssetGenImage get settings =>
      const AssetGenImage('assets/icons/settings.png');

  /// File path: assets/icons/upload.png
  AssetGenImage get upload => const AssetGenImage('assets/icons/upload.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        add,
        closeEye,
        dashboard,
        female,
        home,
        male,
        mess,
        message,
        notification,
        phone,
        search,
        settings,
        upload
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/absent.png
  AssetGenImage get absent => const AssetGenImage('assets/images/absent.png');

  /// File path: assets/images/activities.png
  AssetGenImage get activities =>
      const AssetGenImage('assets/images/activities.png');

  /// File path: assets/images/admin.png
  AssetGenImage get admin => const AssetGenImage('assets/images/admin.png');

  /// File path: assets/images/attendance.png
  AssetGenImage get attendance =>
      const AssetGenImage('assets/images/attendance.png');

  /// File path: assets/images/attended.png
  AssetGenImage get attended =>
      const AssetGenImage('assets/images/attended.png');

  /// File path: assets/images/baby.png
  AssetGenImage get baby => const AssetGenImage('assets/images/baby.png');

  /// File path: assets/images/bee.png
  AssetGenImage get bee => const AssetGenImage('assets/images/bee.png');

  /// File path: assets/images/bg.png
  AssetGenImage get bg => const AssetGenImage('assets/images/bg.png');

  /// File path: assets/images/circleK.png
  AssetGenImage get circleK => const AssetGenImage('assets/images/circleK.png');

  /// File path: assets/images/classes.png
  AssetGenImage get classes => const AssetGenImage('assets/images/classes.png');

  /// File path: assets/images/congrats.png
  AssetGenImage get congrats =>
      const AssetGenImage('assets/images/congrats.png');

  /// File path: assets/images/emergency.png
  AssetGenImage get emergency =>
      const AssetGenImage('assets/images/emergency.png');

  /// File path: assets/images/events.png
  AssetGenImage get events => const AssetGenImage('assets/images/events.png');

  /// File path: assets/images/financial.png
  AssetGenImage get financial =>
      const AssetGenImage('assets/images/financial.png');

  /// File path: assets/images/food.png
  AssetGenImage get food => const AssetGenImage('assets/images/food.png');

  /// File path: assets/images/logo.jpeg
  AssetGenImage get logoJpeg => const AssetGenImage('assets/images/logo.jpeg');

  /// File path: assets/images/logo.png
  AssetGenImage get logoPng => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/logoPlace.png
  AssetGenImage get logoPlace =>
      const AssetGenImage('assets/images/logoPlace.png');

  /// File path: assets/images/member.png
  AssetGenImage get member => const AssetGenImage('assets/images/member.png');

  /// File path: assets/images/newMember.png
  AssetGenImage get newMember =>
      const AssetGenImage('assets/images/newMember.png');

  /// File path: assets/images/onBoarding1.png
  AssetGenImage get onBoarding1 =>
      const AssetGenImage('assets/images/onBoarding1.png');

  /// File path: assets/images/sleep.png
  AssetGenImage get sleep => const AssetGenImage('assets/images/sleep.png');

  /// File path: assets/images/splash.png
  AssetGenImage get splash => const AssetGenImage('assets/images/splash.png');

  /// File path: assets/images/staff.png
  AssetGenImage get staff => const AssetGenImage('assets/images/staff.png');

  /// File path: assets/images/students.png
  AssetGenImage get students =>
      const AssetGenImage('assets/images/students.png');

  /// File path: assets/images/supplies.png
  AssetGenImage get supplies =>
      const AssetGenImage('assets/images/supplies.png');

  /// File path: assets/images/teacher.png
  AssetGenImage get teacher => const AssetGenImage('assets/images/teacher.png');

  /// File path: assets/images/toilet.png
  AssetGenImage get toilet => const AssetGenImage('assets/images/toilet.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        absent,
        activities,
        admin,
        attendance,
        attended,
        baby,
        bee,
        bg,
        circleK,
        classes,
        congrats,
        emergency,
        events,
        financial,
        food,
        logoJpeg,
        logoPng,
        logoPlace,
        member,
        newMember,
        onBoarding1,
        sleep,
        splash,
        staff,
        students,
        supplies,
        teacher,
        toilet
      ];
}

class $AssetsSvgGen {
  const $AssetsSvgGen();

  /// File path: assets/svg/activities.svg
  String get activities => 'assets/svg/activities.svg';

  /// File path: assets/svg/all.svg
  String get all => 'assets/svg/all.svg';

  /// File path: assets/svg/announcement.svg
  String get announcement => 'assets/svg/announcement.svg';

  /// File path: assets/svg/attendance.svg
  String get attendance => 'assets/svg/attendance.svg';

  /// File path: assets/svg/car.svg
  String get car => 'assets/svg/car.svg';

  /// File path: assets/svg/classes.svg
  String get classes => 'assets/svg/classes.svg';

  /// File path: assets/svg/clothes.svg
  String get clothes => 'assets/svg/clothes.svg';

  /// File path: assets/svg/dashboard.svg
  String get dashboard => 'assets/svg/dashboard.svg';

  /// File path: assets/svg/diaper.svg
  String get diaper => 'assets/svg/diaper.svg';

  /// File path: assets/svg/emergency.svg
  String get emergency => 'assets/svg/emergency.svg';

  /// File path: assets/svg/events.svg
  String get events => 'assets/svg/events.svg';

  /// File path: assets/svg/exam.svg
  String get exam => 'assets/svg/exam.svg';

  /// File path: assets/svg/financial.svg
  String get financial => 'assets/svg/financial.svg';

  /// File path: assets/svg/food_new.svg
  String get foodNew => 'assets/svg/food_new.svg';

  /// File path: assets/svg/history.svg
  String get history => 'assets/svg/history.svg';

  /// File path: assets/svg/home.svg
  String get home => 'assets/svg/home.svg';

  /// File path: assets/svg/lanch_new.svg
  String get lanchNew => 'assets/svg/lanch_new.svg';

  /// File path: assets/svg/location.svg
  String get location => 'assets/svg/location.svg';

  /// File path: assets/svg/meals.svg
  String get meals => 'assets/svg/meals.svg';

  /// File path: assets/svg/messages.svg
  String get messages => 'assets/svg/messages.svg';

  /// File path: assets/svg/more_new.svg
  String get moreNew => 'assets/svg/more_new.svg';

  /// File path: assets/svg/none_new.svg
  String get noneNew => 'assets/svg/none_new.svg';

  /// File path: assets/svg/notification.svg
  String get notification => 'assets/svg/notification.svg';

  /// File path: assets/svg/phone_number.png.svg
  String get phoneNumberPng => 'assets/svg/phone_number.png.svg';

  /// File path: assets/svg/plans.svg
  String get plans => 'assets/svg/plans.svg';

  /// File path: assets/svg/settings.svg
  String get settings => 'assets/svg/settings.svg';

  /// File path: assets/svg/snack_new.svg
  String get snackNew => 'assets/svg/snack_new.svg';

  /// File path: assets/svg/some_new.svg
  String get someNew => 'assets/svg/some_new.svg';

  /// File path: assets/svg/staf.svg
  String get staf => 'assets/svg/staf.svg';

  /// File path: assets/svg/stool.svg
  String get stool => 'assets/svg/stool.svg';

  /// File path: assets/svg/students.svg
  String get students => 'assets/svg/students.svg';

  /// File path: assets/svg/toilet.svg
  String get toilet => 'assets/svg/toilet.svg';

  /// File path: assets/svg/urien.svg
  String get urien => 'assets/svg/urien.svg';

  /// List of all assets
  List<String> get values => [
        activities,
        all,
        announcement,
        attendance,
        car,
        classes,
        clothes,
        dashboard,
        diaper,
        emergency,
        events,
        exam,
        financial,
        foodNew,
        history,
        home,
        lanchNew,
        location,
        meals,
        messages,
        moreNew,
        noneNew,
        notification,
        phoneNumberPng,
        plans,
        settings,
        snackNew,
        someNew,
        staf,
        stool,
        students,
        toilet,
        urien
      ];
}

class Assets {
  Assets._();

  static const $AssetsAnimatedGen animated = $AssetsAnimatedGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsSvgGen svg = $AssetsSvgGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
