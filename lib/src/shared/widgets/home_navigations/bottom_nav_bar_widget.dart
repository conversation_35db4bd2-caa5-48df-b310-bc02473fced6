import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/messages/controller/messages_controller.dart';
import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/icon_widget/icon_widget.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

ValueNotifier<int?> bottomNavMessageCountValue = ValueNotifier<int?>(null);

class BottomNavBarWidget extends HookConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);

    final localMessageCount = GetStorageService.getLocalData(
      key: LocalKeys.messageCount,
    );

    final messageCount = ref.watch(getAllMessageData(context)).when(
      data: (data) {
        List<MessageModel> filteredMessages;
        if (const UserModel().isAdmin) {
          filteredMessages = data;
        } else if (const UserModel().isTeacher) {
          filteredMessages = data
              .where(
                  (message) => message.admin == null && message.teacher != null)
              .toList();
        } else {
          filteredMessages = [];
        }

        final onlineMessageCount = filteredMessages.length;
        final newMessages = onlineMessageCount -
            (int.tryParse(localMessageCount.toString()) ?? 0);
        bottomNavMessageCountValue.value = newMessages > 0 ? newMessages : 0;
        return bottomNavMessageCountValue.value!;
      },
      error: (error, stackTrace) {
        return 0;
      },
      loading: () {
        return 0;
      },
    );

    return Container(
      height: 60.h,
      decoration: BoxDecoration(
        color: ColorManager.secondaryColor,
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(AppRadius.bottomNavBarRadius),
          topLeft: Radius.circular(AppRadius.bottomNavBarRadius),
        ),
        boxShadow: ConstantsWidgets.boxShadow,
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        unselectedItemColor: ColorManager.bottomNavIconColor,
        selectedItemColor: ColorManager.primaryColor,
        selectedLabelStyle: const TextStyle(
            color: ColorManager.primaryColor, fontWeight: FontWeight.bold),
        unselectedLabelStyle: const TextStyle(color: ColorManager.primaryColor),
        selectedFontSize: currentIndex == 1 || currentIndex == 4 ? 10 : 12,
        unselectedFontSize: 9,
        elevation: 0,
        backgroundColor: Colors.transparent,
        currentIndex: currentIndex,
        onTap: (index) {
          if (index == 2) return;
          if (index == 3) {
            GetStorageService.setLocalData(
              key: LocalKeys.messageCount,
              value: ref.read(getAllMessageData(context)).when(
                    data: (data) {
                      List<MessageModel> filteredMessages;
                      if (const UserModel().isAdmin) {
                        filteredMessages = data;
                      } else if (const UserModel().isTeacher) {
                        filteredMessages = data
                            .where((message) =>
                                message.admin == null &&
                                message.teacher != null)
                            .toList();
                      } else {
                        filteredMessages = [];
                      }
                      return filteredMessages.length;
                    },
                    error: (error, stackTrace) => 0,
                    loading: () => 0,
                  ),
            );
            bottomNavMessageCountValue.value = 0;
          }
          bottomNavCtrl.changeIndex(index);
        },
        items: [
          BottomNavigationBarItem(
            icon: IconWidget(
              icon: Assets.svgHome,
              color: currentIndex == 0
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.home,
          ),
          BottomNavigationBarItem(
            icon: IconWidget(
              icon: Assets.svgDashboard,
              color: currentIndex == 1
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.dashboard,
          ),
          const BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '',
          ),
          BottomNavigationBarItem(
            icon: Stack(
              children: [
                Badge.count(
                  isLabelVisible: messageCount > 0,
                  backgroundColor: ColorManager.buttonColor,
                  count: messageCount,
                  child: IconWidget(
                    icon: Assets.svgMessages,
                    color: currentIndex == 3
                        ? ColorManager.primaryColor
                        : ColorManager.bottomNavIconColor,
                  ),
                ),
              ],
            ),
            label: context.tr.messages,
          ),
          BottomNavigationBarItem(
            icon: IconWidget(
              icon: Assets.svgSettings,
              color: currentIndex == 4
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.more,
          ),
        ],
      ),
    );
  }
}

// class BottomNavBarWidget extends ConsumerWidget {
//   const BottomNavBarWidget({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final currentIndex = ref.watch(bottomNavigationControllerProvider);
//     final bottomNavCtrl = ref.read(bottomNavControllerProvider);
//
//     final index0 = currentIndex == 0;
//     final index1 = currentIndex == 1;
//     final index3 = currentIndex == 3;
//     final index4 = currentIndex == 4;
//
//     return Container(
//       height: 60.h,
//       decoration: BoxDecoration(
//         color: ColorManager.secondaryColor,
//         borderRadius: const BorderRadius.only(
//           topRight: Radius.circular(AppRadius.bottomNavBarRadius),
//           topLeft: Radius.circular(AppRadius.bottomNavBarRadius),
//         ),
//         boxShadow: ConstantsWidgets.boxShadow,
//       ),
//       child: BottomNavigationBar(
//         type: BottomNavigationBarType.fixed,
//         unselectedItemColor: ColorManager.bottomNavIconColor,
//         selectedItemColor: ColorManager.primaryColor,
//         selectedLabelStyle: const TextStyle(
//             color: ColorManager.primaryColor, fontWeight: FontWeight.bold),
//         unselectedLabelStyle: const TextStyle(color: ColorManager.primaryColor),
//         selectedFontSize: index1 || index4 ? 10 : 12,
//         unselectedFontSize: 9,
//         elevation: 0,
//         backgroundColor: Colors.transparent,
//         currentIndex: currentIndex,
//         onTap: (index) {
//           if (index == 2) return;
//           bottomNavCtrl.changeIndex(index);
//         },
//         items: [
//           BottomNavigationBarItem(
//             icon: IconWidget(
//               icon: Assets.svgHome,
//               color: index0
//                   ? ColorManager.primaryColor
//                   : ColorManager.bottomNavIconColor,
//             ),
//             label: context.tr.home,
//           ),
//           BottomNavigationBarItem(
//             icon: IconWidget(
//               icon: Assets.svgDashboard,
//               color: index1
//                   ? ColorManager.primaryColor
//                   : ColorManager.bottomNavIconColor,
//             ),
//             label: context.tr.dashboard,
//           ),
//           const BottomNavigationBarItem(
//             icon: SizedBox.shrink(),
//             label: '',
//           ),
//           BottomNavigationBarItem(
//             icon: IconWidget(
//               icon: Assets.svgMessages,
//               color: index3
//                   ? ColorManager.primaryColor
//                   : ColorManager.bottomNavIconColor,
//             ),
//             label: context.tr.messages,
//           ),
//           BottomNavigationBarItem(
//             icon: IconWidget(
//               icon: Assets.svgSettings,
//               color: index4
//                   ? ColorManager.primaryColor
//                   : ColorManager.bottomNavIconColor,
//             ),
//             label: context.tr.more,
//           ),
//         ],
//       ),
//     );
//   }
// }
