import 'package:flutter/material.dart';
import 'package:flutter_native_contact_picker/flutter_native_contact_picker.dart';
import 'package:flutter_native_contact_picker/model/contact.dart';

String normalizePhoneNumber(String? rawPhoneNumber) {
  if (rawPhoneNumber == null || rawPhoneNumber.isEmpty) return '';

  // Remove all non-numeric characters
  String digitsOnly = rawPhoneNumber.replaceAll(RegExp(r'\D'), '');

  // Check if the number has a valid prefix (010, 011, 012, 015) and correct length
  if (digitsOnly.startsWith('010') ||
      digitsOnly.startsWith('011') ||
      digitsOnly.startsWith('012') ||
      digitsOnly.startsWith('015')) {
    if (digitsOnly.length > 11) {
      // Truncate to 11 digits if it's too long
      digitsOnly = digitsOnly.substring(0, 11);
    } else if (digitsOnly.length < 11) {
      // Pad with zeros if it's too short (unlikely but ensures 11 digits)
      digitsOnly = digitsOnly.padRight(11, '0');
    }
    return digitsOnly;
  }

  // Handle cases where the number starts with country code (e.g., +20 or 20)
  if (digitsOnly.startsWith('20') && digitsOnly.length > 2) {
    digitsOnly = digitsOnly.substring(2); // Remove the country code
    if (digitsOnly.startsWith('10') ||
        digitsOnly.startsWith('11') ||
        digitsOnly.startsWith('12') ||
        digitsOnly.startsWith('15')) {
      // Truncate or pad to ensure 11 digits
      if (digitsOnly.length > 11) {
        digitsOnly = digitsOnly.substring(0, 11);
      } else if (digitsOnly.length < 11) {
        digitsOnly = digitsOnly.padRight(11, '0');
      }
      return digitsOnly;
    }
  }

  // If no valid prefix, assume it's malformed and return first 11 digits or pad
  if (digitsOnly.length > 11) {
    digitsOnly = digitsOnly.substring(0, 11);
  } else if (digitsOnly.length < 11) {
    digitsOnly = digitsOnly.padRight(11, '0');
  }

  return digitsOnly;
}

class SelectContactIcon extends StatelessWidget {
  final Function(Contact) onSelected;

  const SelectContactIcon({super.key, required this.onSelected});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.contact_phone),
      onPressed: () async {
        final FlutterNativeContactPicker _contactPicker =
            FlutterNativeContactPicker();

        var contact = await _contactPicker.selectContact();
        if (contact != null) {
          onSelected(contact);
        }
      },
    );
  }
}
