part of shared_widgets;

class TapBarWidget extends ConsumerWidget {
  final List<String> tabs;
  final int initialIndex;
  final Function(int)? onTab;
  final ClassModel classModel;

  const TapBarWidget({
    super.key,
    required this.tabs,
    required this.initialIndex,
    required this.onTab,
    required this.classModel,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrl = ref.watch(tabBarControllerProvider);

    final isTeacher = const UserModel().currentUser.isTeacher;

    return DefaultTabController(
      initialIndex: initialIndex,
      length: tabs.length,
      child: TabBar(
        splashBorderRadius: BorderRadius.circular(AppRadius.tabBarRadius),
        tabAlignment: TabAlignment.start,
        unselectedLabelStyle: context.labelLarge,
        onTap: onTab,
        indicatorColor: Colors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        unselectedLabelColor: context.isDark
            ? ColorManager.grey.withOpacity(0.5)
            : ColorManager.secondaryColor,
        labelPadding: const EdgeInsets.all(8),
        labelColor: ColorManager.white,
        labelStyle: context.subTitle.copyWith(fontWeight: FontWeight.w400),
        isScrollable: true,
        tabs: tabs.indexed.map((e) {
          final index = e.$1;

          if (index == tabCtrl) {
            return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                decoration: BoxDecoration(
                    color: ColorManager.primaryColor,
                    borderRadius:
                        BorderRadius.circular(AppRadius.tabBarRadius)),
                child: Text(
                    '${e.$2} ${isTeacher ? '' : lengthList(tabCtrl, classModel: classModel)}'));
          }
          return Text(
            e.$2,
            style: context.subTitle.copyWith(fontWeight: FontWeight.w200),
          );
        }).toList(),
      ),
    );
  }
}

String lengthList(int index, {required ClassModel classModel}) {
  final studentsLength =
      classModel.students != null ? classModel.students!.length - 1 : 0;
  final filteredTeachers =
      classModel.teachers?.where((element) => element.id != 0).toList();
  final teachersLength = filteredTeachers!.length;

  switch (index) {
    case 0:
      return '($studentsLength)';
    case 1:
      return '($teachersLength)';
  }

  return '';
}
