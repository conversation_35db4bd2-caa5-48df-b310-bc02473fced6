library shared_widgets;

import 'dart:io';

import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/admin_setup/view/congratulation_screen.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/controllers/class_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/controllers/stepper_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:connectify_app/src/shared/widgets/stepper_widget/stepper_widget.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:shimmer/shimmer.dart';
import 'package:xr_helper/xr_helper.dart';

import '../consts/app_constants.dart';

part 'add_entry_widget/add_rectangle_widget.dart';
part 'add_entry_widget/add_square_widget.dart';
part 'animated/lottie_icon.dart';
part 'appbar/setup_appbar.dart';
part 'back_ground/back_ground.dart';
part 'box_shadow.dart';
part 'cached_image/base_cached_image.dart';
part 'details_top_section/details_top_section_widget.dart';
//? Dialog

part 'dialogs/alert_dialog.dart';
part 'dialogs/base_dialog.dart';
part 'dialogs/show_dialog.dart';
part 'my_divider/my_divider.dart';
//? Pick Single Image
part 'pick_image/pick_single_image/pick_single_image_widget.dart';
part 'pick_image/pick_single_image/widgets/pick_image_button.dart';
part 'pick_image/pick_single_image/widgets/view_network_image.dart';
part 'pick_image/pick_single_image/widgets/view_picked_image.dart';
part 'tab_bar_widgets/tab_bar_widget.dart';
