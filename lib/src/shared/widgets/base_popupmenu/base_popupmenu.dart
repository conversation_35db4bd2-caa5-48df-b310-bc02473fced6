import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class BasePopupmenu extends HookWidget {
  final Function()? editOnTap;
  final Function() deleteOnTap;
  final PopupMenuItem? switchWidget;
  final PopupMenuItem? additionalWidget;
  final bool isPopUpMenuShow;

  const BasePopupmenu({
    super.key,
    this.editOnTap,
    required this.deleteOnTap,
    this.switchWidget,
    this.isPopUpMenuShow = false,
    this.additionalWidget,
  });

  @override
  Widget build(BuildContext context) {
    final isAdmin = const UserModel().isAdmin;
    // isAdmin = const UserModel().isAdmin;

    if (!isAdmin && !isPopUpMenuShow) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton(
        elevation: 0,
        shape: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
            borderSide: const BorderSide(color: ColorManager.grey)),
        color: ColorManager.secondaryColor,
        itemBuilder: (context) => [
              if (editOnTap != null)
                //! Edit
                PopupMenuItem(
                    onTap: editOnTap,
                    child: Row(
                      children: [
                        Text(
                          context.tr.edit,
                          style: context.labelMedium
                              .copyWith(color: ColorManager.primaryColor),
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.edit,
                          color: ColorManager.primaryColor,
                        )
                      ],
                    )),

              //! Delete
              PopupMenuItem(
                onTap: deleteOnTap,
                child: Row(
                  children: [
                    Text(
                      context.tr.delete,
                      style: context.labelMedium
                          .copyWith(color: ColorManager.errorColor),
                    ),
                    const Spacer(),
                    const Icon(
                      Icons.delete,
                      color: ColorManager.errorColor,
                    )
                  ],
                ),
              ),

              if (additionalWidget != null) additionalWidget!,

              if (switchWidget != null) switchWidget!,
            ]);
  }
}
