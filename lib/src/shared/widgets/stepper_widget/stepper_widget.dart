import 'package:connectify_app/src/screens/admin_setup/view/Activities/setup_activities_screen.dart';
import 'package:connectify_app/src/shared/controllers/stepper_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../screens/admin_setup/view/add_students/setup_students_screen.dart';
import '../../../screens/admin_setup/view/setup_classes/setup_classes_screen.dart';
import '../../../screens/admin_setup/view/teachers_team/setup_teachers_screen.dart';

class StepperWidget extends ConsumerWidget {
  const StepperWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stepperCtrl = ref.read(stepperController);
    final stepperIndex = ref.watch(stepperControllerProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        //! Classes
        Expanded(
          child: stepperCircle(context,
                  title: context.tr.classes,
                  isActive: stepperIndex == 0,
                  isPassed: stepperIndex > 0)
        ),

        //! Nursery Team
        Expanded(
          child: stepperCircle(context,
                  withSpace: true,
                  title: context.tr.staff,
                  isActive: stepperIndex == 1,
                  isPassed: stepperIndex > 1)
        ),

        //! Add Students
        Expanded(
          child: stepperCircle(context,
                  title: context.tr.students,
                  isActive: stepperIndex == 2,
                  isPassed: stepperIndex > 2)
        ),

        //! Add Activities
        Expanded(
          child: stepperCircle(context,
                  title: context.tr.activities,
                  isActive: stepperIndex == 3,
                  isPassed: stepperIndex > 3)
        ),
      ],
    );
  }
}

Widget stepperCircle(
  BuildContext context, {
  required String title,
  bool isActive = false,
  bool isPassed = false,
  bool withSpace = false,
}) {
  final borderColor = isActive
      ? ColorManager.white
      : isPassed
          ? Colors.transparent
          : ColorManager.grey;

  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      if (withSpace) context.smallGap,
      Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            decoration: isActive || isPassed
                ? BoxDecoration(
                    shape: BoxShape.circle,
                    border:
                        Border.all(color: borderColor, width: isActive ? 2 : 0),
                  )
                : null,
            padding: EdgeInsets.all(isActive ? 4 : 2),
            child: CircleAvatar(
              radius: 5,
              backgroundColor: isActive || isPassed
                  ? ColorManager.secondaryColor
                  : ColorManager.grey,
            ),
          ),
          context.smallGap,
          Text(
            title,
            style: context.whiteSmallHint,
            textAlign: TextAlign.center,
          )
        ],
      ).paddingOnly(
        top: isActive ? 0 : AppSpaces.xSmallPadding / 2,
      ),
      if (withSpace) context.smallGap,
      const Expanded(
        child: Divider(
          thickness: 2,
          color: ColorManager.white,
        ),
      ),
    ],
  );
}
