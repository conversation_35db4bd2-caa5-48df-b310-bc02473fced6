part of shared_widgets;

class SetupAppBar extends ConsumerWidget {
  final Widget child;
  final Widget nextWidget;
  final int? nextIndex;

  const SetupAppBar({
    super.key,
    required this.child,
    required this.nextWidget,
    this.nextIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stepperCtrl = ref.read(stepperController);
    final stepperIndex = ref.watch(stepperControllerProvider);
    final bool isAfternoon = DateTime.now().hour >= 12;

    return WillPopScope(
      onWillPop: () async {
        if (stepperIndex == 0) {
          exit(0);
        } else {
          stepperCtrl.changeIndex(stepperIndex - 1);
        }
        return true;
      },
      child: SafeArea(
        child: Scaffold(
          bottomNavigationBar: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Button(
                  label: context.tr.next,
                  color: ColorManager.primaryColor,
                  onPressed: () {
                    if (nextIndex != null) {
                      stepperCtrl.changeIndex(nextIndex!);
                    }

                    context.to(nextWidget);
                  }).sized(
                height: 40.h,
                width: context.width * .9,
              ),
              context.smallGap,
              TextButton(
                onPressed: () => context.to(const CongratulationsScreen()),
                child: Text(
                  context.tr.skipForNow,
                  style: context.subTitle,
                ),
              ),
            ],
          ),
          body: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                width: double.infinity,
                decoration: const BoxDecoration(
                    color: ColorManager.primaryColor,
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(AppRadius.appBarRadius),
                      bottomLeft: Radius.circular(AppRadius.appBarRadius),
                    )),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              isAfternoon
                                  ? context.tr.goodAfternoon
                                  : context.tr.goodMorning,
                              style: context.whiteHeadLine,
                            ),
                            Text(
                              'Kinder Daycare and Nursery',
                              style: context.whiteLabelLarge,
                            ),
                          ],
                        ),
                        const Spacer(),
                        Image.asset(Assets.imagesCircleK)
                      ],
                    ),
                    const StepperWidget().sized(height: context.height * .06)
                  ],
                ),
              ),
              Expanded(
                  child: child.paddingOnly(
                      left: AppSpaces.mediumPadding,
                      right: AppSpaces.mediumPadding,
                      bottom: AppSpaces.mediumPadding))
            ],
          ),
        ),
      ),
    );
  }
}
