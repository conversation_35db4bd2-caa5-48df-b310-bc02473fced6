import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ClassDropDown extends HookConsumerWidget {
  final ValueNotifier<ClassModel?> selectedClass;
  final int? classId;
  final bool withLoading;
  final bool selectFirstClass;
  final List<ClassModel> classesData;

  const ClassDropDown(
      {super.key,
      required this.selectedClass,
      this.classId,
      this.selectFirstClass = false,
      this.withLoading = true,
      this.classesData = const []});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getClassController = ref.watch(getClassDataProvider(context));
    final classController = ref.watch(classChangeNotifierController(context));
    var classes = useState<List<ClassModel>>([]);

    if (classesData.isEmpty) {
      getClassController.whenData((data) {
        classes.value = data;

        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          if (selectFirstClass &&
              classes.value.isNotEmpty &&
              selectedClass.value == null) {
            selectedClass.value = classes.value.firstOrNull;
          }
          if (classId != null && selectedClass.value == null) {
            selectedClass.value = classes.value
                .firstWhereOrNull((element) => element.id == classId);
          }
        });
      });

      if (getClassController.isLoading && withLoading) {
        return const LinearProgressIndicator();
      }
    } else {
      if (classController.isLoading) {
        return const LinearProgressIndicator();
      }
    }

    if (selectFirstClass &&
        classesData.isNotEmpty &&
        selectedClass.value == null) {
      selectedClass.value = classesData.firstOrNull;
    }

    return IgnorePointer(
      ignoring:
          withLoading && classesData.isEmpty && getClassController.isLoading,
      child: BaseSearchDropDown(
        label: context.tr.className,
        data: classesData.isEmpty ? classes.value : classesData,
        itemModelAsName: (classModel) => (classModel as ClassModel).name,
        selectedValue: selectedClass.value,
        isEng: context.isEng,
        onChanged: (value) {
          selectedClass.value = value as ClassModel?;
        },
      ),
    );
  }
}

class MultiClassDropDown extends HookConsumerWidget {
  final ValueNotifier<List<ClassModel>?> selectedClasses;
  final List<int?>? classIds;

  const MultiClassDropDown({
    super.key,
    required this.selectedClasses,
    this.classIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classController = ref.watch(getClassDataProvider(context));
    final classes = useState<List<ClassModel>>([]);

    useEffect(() {
      classController.whenData((data) {
        classes.value = data;
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          if ((selectedClasses.value?.isEmpty ?? true) && classIds != null) {
            selectedClasses.value = classes.value
                .where((element) => classIds!.contains(element.id))
                .toList();
          }
        });
      });
      return null;
    }, [classController]);

    if (classController.isLoading) {
      return const Center(child: LinearProgressIndicator());
    }

    void showSearchBottomSheet(BuildContext context) {
      final ValueNotifier<String> searchQuery = ValueNotifier<String>('');

      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  onChanged: (value) => searchQuery.value = value,
                  decoration: InputDecoration(
                    hintText: context.tr.searchClasses,
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ValueListenableBuilder<String>(
                    valueListenable: searchQuery,
                    builder: (context, query, child) {
                      final filteredItems = classes.value
                          .where((item) => item.name
                              .toLowerCase()
                              .contains(query.toLowerCase()))
                          .toList();

                      if (filteredItems.isEmpty) {
                        return Center(
                          child: Text(
                            context.tr.noResultsFound,
                            style: context.labelLarge,
                          ),
                        );
                      }

                      return StatefulBuilder(builder: (context, setState) {
                        return Column(
                          children: [
                            Expanded(
                              child: ListView.builder(
                                itemCount: filteredItems.length,
                                itemBuilder: (context, index) {
                                  final item = filteredItems[index];
                                  final isSelected = selectedClasses.value?.any(
                                          (selected) =>
                                              selected?.id == item.id) ??
                                      false;

                                  return CheckboxListTile(
                                    value: isSelected,
                                    onChanged: (isChecked) {
                                      final currentSelections =
                                          selectedClasses.value ?? [];
                                      if (isChecked == true) {
                                        selectedClasses.value = [
                                          ...currentSelections,
                                          item
                                        ];
                                      } else {
                                        selectedClasses.value =
                                            currentSelections
                                                .where((selected) =>
                                                    selected?.id != item.id)
                                                .toList();
                                      }

                                      setState(() {});
                                    },
                                    title: Text(item.name),
                                    subtitle: Text(item.description),
                                  );
                                },
                              ),
                            ),
                            context.fieldsGap,
                            Button(
                              onPressed: () {
                                context.back();
                              },
                              label: context.tr.submit,
                            ),
                          ],
                        );
                      });
                    },
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    return GestureDetector(
      onTap: () => showSearchBottomSheet(context),
      child: Container(
        width: MediaQuery.of(context).size.width,

        // height: 50,
        alignment: Alignment.centerLeft,
        child: BaseTextField(
          isRequired: false,
          enabled: false,
          // selectedClasses.value != null && selectedClasses.value!.isNotEmpty
          //     ? selectedClasses.value!.map((e) => e?.name).toList().join(", ")
          //     : context.tr.selectClasses,
          title: context.tr.classes,
          textInputType: TextInputType.text,
          label: selectedClasses.value != null &&
                  selectedClasses.value!.isNotEmpty
              ? selectedClasses.value!.map((e) => e?.name).toList().join(", ")
              : context.tr.selectClasses,
        ),
      ),
    );
  }
}

// class MultiClassDropDown extends HookConsumerWidget {
//   final ValueNotifier<List<ClassModel?>?> selectedClasses;
//   final List<int?>? classIds;
//
//   const MultiClassDropDown(
//       {super.key, required this.selectedClasses, this.classIds});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final classController = ref.watch(getClassDataProvider(context));
//
//     var classes = useState<List<ClassModel>>([]);
//
//     classController.whenData((data) {
//       classes.value = data;
//       //      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
//       //         if (teacherIds != null &&
//       //             teacherIds!.isNotEmpty &&
//       //             selectedTeachers.value == null) {
//       //           selectedTeachers.value = teachers.value
//       //               .where((element) => teacherIds!.contains(element.id))
//       //               .toList();
//       //         }
//       //       });
//       //     });
//
//       WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
//         log('asffsafsaf ${classIds}');
//         if ((selectedClasses.value?.isEmpty ?? false) && classIds != null) {
//           selectedClasses.value = classes.value
//               .where((element) => classIds!.contains(element.id))
//               .toList();
//           log('afasfa ${selectedClasses.value}');
//         }
//       });
//     });
//
//     if (classController.isLoading) return const LinearProgressIndicator();
//
//     return BaseSearchDropDown(
//       label: context.tr.className,
//       data: classes.value,
//       isMultiSelect: true,
//       isEng: context.isEng,
//       multiItemsAsName: (data) =>
//           (data).map((e) => e.name).toList().toString().filterMultiDropDownList,
//       itemModelAsName: (classModel) => (classModel as ClassModel).name,
//       selectedValue: selectedClasses.value,
//       onChanged: (value) {
//         selectedClasses.value = List<ClassModel>.from(value as List);
//       },
//     );
//   }
// }
