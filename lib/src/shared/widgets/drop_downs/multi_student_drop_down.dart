import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class MultiStudentDropDown extends HookConsumerWidget {
  final ValueNotifier<List<StudentModel>> selectedStudents;
  final String? label;
  final bool withLoading;
  final List<StudentModel> studentsData;

  const MultiStudentDropDown({
    super.key,
    required this.selectedStudents,
    this.label,
    this.withLoading = true,
    this.studentsData = const [],
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getStudentController = ref.watch(getActiveStudents(context));
    final studentsController =
        ref.watch(studentChangeNotifierProvider(context));

    var students = useState<List<StudentModel>>([]);

    if (studentsData.isEmpty) {
      getStudentController.whenData((data) {
        students.value = data.toList();
      });

      if (getStudentController.isLoading && withLoading) {
        return const LinearProgressIndicator();
      }
    } else {
      if (studentsController.isLoading) {
        return const LinearProgressIndicator();
      }
    }

    return IgnorePointer(
      ignoring: withLoading && studentsData.isEmpty
          ? getStudentController.isLoading
          : false,
      child: BaseSearchDropDown(
        label: label ?? context.tr.studentName,
        data: (studentsData.isEmpty ? students.value : studentsData)
            .where((element) => element.id != 0)
            .toList(),
        itemModelAsName: (studentModel) => (studentModel as StudentModel).name,
        selectedValue: selectedStudents.value,
        multiItemsAsName: (data) => (data)
            .map((e) => e.name)
            .toList()
            .toString()
            .filterMultiDropDownList,
        isMultiSelect: true,
        isEng: context.isEng,
        onChanged: (value) {
          selectedStudents.value = List<StudentModel>.from(value as List);
        },
      ),
    );
  }
}
