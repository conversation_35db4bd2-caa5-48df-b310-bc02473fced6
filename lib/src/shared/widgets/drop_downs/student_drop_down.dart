import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class StudentDropDown extends HookConsumerWidget {
  final ValueNotifier<StudentModel?> selectedStudent;
  final String? label;
  final int? studentId;
  final bool withLoading;
  final List<StudentModel> studentsData;

  const StudentDropDown({
    super.key,
    required this.selectedStudent,
    this.studentId,
    this.label,
    this.withLoading = true,
    this.studentsData = const [],
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getStudentController = ref.watch(getActiveStudents(context));
    final studentsController =
        ref.watch(studentChangeNotifierProvider(context));

    var students = useState<List<StudentModel>>([]);

    if (studentsData.isEmpty) {
      getStudentController.whenData((data) {
        students.value = data.toList();

        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          if (studentId != null && selectedStudent.value == null) {
            selectedStudent.value = students.value
                .firstWhereOrNull((element) => element.id == studentId);
          }
        });
      });

      if (getStudentController.isLoading && withLoading) {
        return const LinearProgressIndicator();
      }
    } else {
      if (studentsController.isLoading) {
        return const LinearProgressIndicator();
      }
    }

    return IgnorePointer(
      ignoring: withLoading && studentsData.isEmpty
          ? getStudentController.isLoading
          : false,
      child: BaseSearchDropDown(
        label: label ?? context.tr.studentName,
        data: (studentsData.isEmpty ? students.value : studentsData)
            .where((element) => element.id != 0)
            .toList(),
        itemModelAsName: (studentModel) => (studentModel as StudentModel).name,
        selectedValue: selectedStudent.value,
        isEng: context.isEng,
        onChanged: (value) {
          selectedStudent.value = value as StudentModel?;
        },
      ),
    );
  }
}
