import 'dart:async';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repository/local_media_repo.dart';

final mediaPickerControllerProvider =
    ChangeNotifierProvider<MediaPickerController>(
  (ref) {
    final mediaLocalRepoProvider = ref.watch(localMediaRepoProvider);

    return MediaPickerController(mediaLocalRepoProvider);
  },
);

class MediaPickerController extends ChangeNotifier {
  final LocalMediaRepo _mediaRepo;

  MediaPickerController(this._mediaRepo);

  FilePickerResult? _filePickerResult;

  List<String> get filesPaths =>
      _filePickerResult?.paths.map((e) => e ?? '').toList() ?? [];

  String get filePath => _filePickerResult?.files.firstOrNull?.path ?? '';

  // Map to store section images with their IDs
  final Map<String, String> _sectionImages = {};

  // Getter for section images
  Map<String, String> get sectionImages => _sectionImages;

  // Set an image for a specific section
  void setSectionImage(String sectionId, String path) {
    _sectionImages[sectionId] = path;
    notifyListeners();
  }

  // Remove an image for a specific section
  void removeSectionImage(String sectionId) {
    _sectionImages.remove(sectionId);
    notifyListeners();
  }

  // Clear all section images
  void clearSectionImages() {
    _sectionImages.clear();
    notifyListeners();
  }

  Future<FilePickerResult> compressAndGetFile(
      File file, String targetPath) async {
    var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 75,
      rotate: 0,
    );

    Log.i('Compressed File: ${result?.path}');

    if (result == null) {
      throw Exception('Failed to compress file');
    }

    final platformFile = PlatformFile(
      name: result.path.split('/').last,
      path: result.path,
      bytes: await result.readAsBytes(),
      size: await result.length(),
    );

    return FilePickerResult([platformFile]);
  }

  Future<FilePickerResult?> pickFile({
    bool imageUpload = true,
    bool allowMultiple = false,
  }) async {
    try {
      final pickedFiles = await _mediaRepo.pickFiles(
        imageUpload: imageUpload,
        uploadMultiple: allowMultiple,
      );

      if (pickedFiles == null) return null;

      if (imageUpload) {
        List<PlatformFile> compressedFiles = [];
        for (var file in pickedFiles.files) {
          var result = await compressAndGetFile(
              File(file.path!), '${file.path!}.compressed.jpg');
          if (result.files.isNotEmpty) {
            compressedFiles.add(result.files.first);
          }
        }

        _filePickerResult = FilePickerResult(compressedFiles);
      } else {
        _filePickerResult = pickedFiles;
      }

      notifyListeners();

      return pickedFiles;
    } on Exception catch (e) {
      Log.e('Error Getting File $e');
      rethrow;
    }
  }

  void clearFiles() {
    _filePickerResult = null;

    Log.i('FileClearedSuccessfully');

    notifyListeners();
  }

  void removeFile(int index) {
    _filePickerResult?.files.removeAt(index);
    notifyListeners();
  }
}
