import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server/gmail.dart';
import 'package:xr_helper/xr_helper.dart';

class SendEmailService {
  static Future<void> sendEmail({
    required String subject,
    required String body,
    required List<String> recipient,
  }) async {
    final smtpServer =
        gmail('<EMAIL>', 'thxf ibfl uboa pasm');

    final message = Message()
      ..from = const Address(
          '<EMAIL>', 'New Nursery Registration')
      ..recipients.addAll(recipient)
      ..ccRecipients.add('<EMAIL>')
      ..subject = subject
      ..text = body;

    try {
      final sendReport = await send(message, smtpServer);
      Log.i('Message sent: $sendReport');
    } on MailerException catch (e) {
      Log.e('Message not sent.');
      for (var p in e.problems) {
        Log.e('Problem: ${p.code}: ${p.msg}');
      }
    }
  }
}
