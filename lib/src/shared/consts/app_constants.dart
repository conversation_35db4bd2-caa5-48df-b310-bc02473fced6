import 'package:flutter/material.dart'
    show
        Border,
        BorderRadius,
        BoxDecoration,
        BoxShape,
        Color,
        Locale,
        LocalizationsDelegate;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../generated/l10n.dart';

class AppConsts {
  static const String appName = 'Connectify';
  static const String infoEmail = '<EMAIL>';
  static const String subscriptionReminderChannelKey = 'subscription_reminder';
  static const int class0ID = 150; //? for un assigned teachers
  static const String appFont = 'ProductSans';
  static const String serverKey =
      'AAAAJg2fz0c:APA91bFw4I29Ml7Iit5T9u_UKIqTvHsM27yRB2WvduXlpLyWrNEDl4ovRzKa1swwscVNyQgezqUgFKPcEZTiyTCW83Sy9MBjQY-0d7IdUO7ZWula-MynT-hwx78i83ZfpMtOhEb8pU73';
  static const Locale locale = Locale('en');
  static const int animatedDuration = 50;
  static const int maxStudents = 5;

  static const List<Locale> supportedLocales = [
    locale,
    Locale('ar'),
  ];
  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  //? Widgets ------------------------
  static BoxDecoration eventBoxDecoration([Color? color]) => BoxDecoration(
        color: color,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(10),
      );

  static BoxDecoration markerBoxDecoration() => BoxDecoration(
        border: Border.all(
          color: ColorManager.lightBlue,
          width: 1,
        ),
        shape: BoxShape.circle,
      );

  static final months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];

  // * Place Holders

  static const teacherPlaceholder =
      'https://connectifyapp.org/appimages/Teacher.png';
  static const nurseryPlaceholder =
      'https://connectifyapp.org/appimages/nursery.png';
  static const studentPlaceholder =
      'https://connectifyapp.org/appimages/baby.png';
  static const activityPlaceholder =
      'https://connectifyapp.org/appimages/activity.png';

  //? Payment Methods
  static const String instapay = 'InstaPay';
  static const String vodafoneCash = 'Vodafone Cash';
  static const String etisalatCash = 'Etisalat Cash';
  static const String weCash = 'WE Cash';
  static const String orangeCash = 'Orange Cash';
}
