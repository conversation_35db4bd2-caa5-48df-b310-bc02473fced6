import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/teacher/controllers/teacher_controller.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/data/remote/api_strings.dart';
import '../../../../../shared/services/media/controller/media_controller.dart';
import '../../../../admin_setup/view/teachers_team/widgets/add_teachers_fields.dart';

class AddTeacherDialog extends HookConsumerWidget {
  final Widget navigateWidget;
  final TeacherModel? teacher;

  const AddTeacherDialog({
    super.key,
    required this.navigateWidget,
    this.teacher,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return const AddSquareWidget().onTap(() {
      showAddTeacherDialog(context,
              navigateWidget: navigateWidget, teacher: teacher)
          .then((value) {
        mediaController.clearFiles();
      });
    });
  }
}

Future<void> showAddTeacherDialog(BuildContext context,
    {required Widget navigateWidget,
    TeacherModel? teacher,
    bool viewOnlyClasses = false}) async {
  return showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            //!-----------------------------------------------------

            final controllers = {
              ApiStrings.name: useTextEditingController(text: teacher?.name),
              ApiStrings.jobTitle:
                  useTextEditingController(text: teacher?.description),
              ApiStrings.phone: useTextEditingController(text: teacher?.phone),
            };

            final isEdit = teacher != null;

            //!-----------------------------------------------------

            final selectedClasses = useState<List<ClassModel>>([]);

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            //!-----------------------------------------------------

            final teacherChangeNotifier =
                ref.watch(teacherChangeNotifierProvider(context));

            //!-----------------------------------------------------

            final filePath = ref.watch(mediaPickerControllerProvider).filePath;

            Future<void> addEditTeacher() async {
              if (isEdit) {
                await teacherChangeNotifier.editTeacher(
                    controllers: controllers,
                    selectedClasses: selectedClasses.value,
                    id: teacher.id!,
                    pickedImage: filePath,
                    fcmToken: teacher.fcmToken,
                    navigateWidget: navigateWidget);
              } else {
                await teacherChangeNotifier.addTeacher(
                    controllers: controllers,
                    selectedClasses: selectedClasses.value,
                    pickedImage: filePath,
                    navigateWidget: navigateWidget);
              }
            }

            return AlertDialogWidget(
              networkImage: teacher?.image?.url ?? '',
              iconPath: Assets.iconsAdd,
              isImage: !viewOnlyClasses,
              header: viewOnlyClasses
                  ? context.tr.assignToClass
                  : isEdit
                      ? context.tr.editTeacher
                      : context.tr.addNurseryTeamMember,
              isLoading: teacherChangeNotifier.isLoading,
              child: Form(
                key: formKey.value,
                child: AddTeachersFields(
                  classId: teacher?.teacherClasses
                      .map(
                        (e) => e?.id,
                      )
                      .toList(),
                  controllers: controllers,
                  selectedClass: selectedClasses,
                  viewOnlyClasses: viewOnlyClasses,
                ),
              ),
              onConfirm: () async {
                if (!formKey.value.currentState!.validate()) {
                  return;
                }

                await addEditTeacher();
              },
            );
          },
        );
      });
}
