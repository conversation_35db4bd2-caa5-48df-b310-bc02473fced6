import 'package:connectify_app/src/screens/teacher/controllers/teacher_controller.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/widgets/add_teacher_dialog.dart';
import 'package:connectify_app/src/screens/teacher/view/teachers_details_screen/teachers_details_screen.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:connectify_app/src/shared/widgets/switch_button_widget/switch_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/base_popupmenu/base_popupmenu.dart';

class TeacherCard extends ConsumerWidget {
  final bool isSignUp;
  final TeacherModel teacher;
  final Widget navigateWidget;

  const TeacherCard(
      {super.key,
      required this.teacher,
      required this.navigateWidget,
      this.isSignUp = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return isSignUp
        ? _SignUpCard(teacher: teacher)
        : _TeacherCardWidget(
            teacher: teacher,
            navigateWidget: navigateWidget,
          );
  }
}

class _SignUpCard extends StatelessWidget {
  final TeacherModel teacher;

  const _SignUpCard({required this.teacher});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 150.w,
      padding: const EdgeInsets.all(AppSpaces.smallPadding),
      decoration: BoxDecoration(
          border: Border.all(color: ColorManager.black.withOpacity(0.4)),
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: ConstantsWidgets.boxShadowFromBottom),
      child: Column(
        children: [
          Container(
            height: 65.h,
            width: 80.w,
            decoration: BoxDecoration(
                color: ColorManager.white,
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius)),
            child: ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: BaseCachedImage(
                teacher.image?.url ?? '',
                fit: BoxFit.cover,
              ),
            ),
          ),
          context.mediumGap,
          Text(
            teacher.name,
            style: context.blueHint,
          ),
          context.smallGap,
          Text(
            teacher.description,
            style: context.smallHint.copyWith(fontSize: 12),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          )
        ],
      ),
    );
  }
}

class _TeacherCardWidget extends HookConsumerWidget {
  final TeacherModel teacher;
  final Widget navigateWidget;

  const _TeacherCardWidget({
    required this.teacher,
    required this.navigateWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherCtrl = ref.watch(teacherChangeNotifierProvider(context));

    final isActive = useState<bool>(teacher.isActive);

    return InkWell(
      onTap: () => context.to(TeachersDetailsScreen(
        teacher: teacher,
      )),
      child: Container(
        width: 150.w,
        padding: const EdgeInsets.all(AppSpaces.smallPadding),
        decoration: BoxDecoration(
            border: Border.all(color: ColorManager.black.withOpacity(0.4)),
            color: teacher.isActive
                ? ColorManager.white
                : ColorManager.grey.withOpacity(0.5),
            borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
            boxShadow: ConstantsWidgets.boxShadowFromBottom),
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            //! Image & Name & Description
            Center(
              child: Column(
                children: [
                  //! Image
                  ClipRRect(
                    borderRadius:
                        BorderRadius.circular(AppRadius.baseContainerRadius),
                    child: BaseCachedImage(teacher.image?.url ?? '',
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorWidget: const BaseCachedImage(
                          AppConsts.teacherPlaceholder,
                        )),
                  ).sized(height: 65.h, width: 80.w),

                  context.mediumGap,

                  //! Teacher Name
                  Text(
                    teacher.name,
                    style: context.blueHint,
                    maxLines: 1,
                  ),

                  context.smallGap,

                  //! Teacher Description
                  Text(
                    teacher.description,
                    style: context.smallHint.copyWith(fontSize: 12),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                ],
              ),
            ),
            BasePopupmenu(
              editOnTap: () {
                showAddTeacherDialog(context,
                    navigateWidget: navigateWidget, teacher: teacher);
              },
              additionalWidget:
                  // teacher.classes?.id == AppConsts.class0ID
                  teacher.classes
                              ?.map((e) => e.id)
                              .contains(AppConsts.class0ID) ??
                          false
                      ? null
                      : PopupMenuItem(
                          child: Row(
                            children: [
                              Text(
                                context.tr.unAssign,
                                style: context.labelMedium
                                    .copyWith(color: ColorManager.errorColor),
                              ),
                              const Spacer(),
                              const Icon(
                                Icons.person_remove,
                                color: ColorManager.errorColor,
                              )
                            ],
                          ),
                          onTap: () async {
                            showAddTeacherDialog(
                              context,
                              navigateWidget: navigateWidget,
                              teacher: teacher,
                              viewOnlyClasses: true,
                            );
                          },
                        ),
              deleteOnTap: () => showDialog(
                  context: context,
                  builder: (context) => BaseDeleteDialog(
                      description: context.tr.areYouSureToDeleteThisTeacher,
                      onConfirm: () async {
                        await teacherCtrl.deleteTeacher(
                            id: teacher.id!, navigateWidget: navigateWidget);

                        if (!context.mounted) return;

                        context.toReplacement(navigateWidget);
                        context.showBarMessage(context.tr.deletedSuccessfully,
                            isError: true);
                      })),
              switchWidget: PopupMenuItem(
                  child: Row(
                children: [
                  Text(
                    context.tr.active,
                    style: context.labelMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isActive.value
                            ? ColorManager.buttonColor
                            : ColorManager.grey),
                  ),
                  const Spacer(),
                  StatefulBuilder(builder: (context, setState) {
                    return SwitchButtonWidget(
                      value: isActive,
                      onChanged: (value) {
                        isActive.value = value;

                        teacherCtrl.activeOrDeActiveStudent(
                          id: teacher.id!,
                          isActive: value,
                        );

                        setState(() {});
                      },
                    );
                  })
                ],
              )),
            ),
          ],
        ),
      ),
    );
  }
}
