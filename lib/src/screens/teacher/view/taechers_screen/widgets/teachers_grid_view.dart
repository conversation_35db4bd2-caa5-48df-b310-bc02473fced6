import 'package:connectify_app/src/screens/teacher/view/taechers_screen/widgets/add_teacher_dialog.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/widgets/teacher_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/consts/app_constants.dart';
import '../../../models/teacher_model.dart';

class TeachersGridView extends HookConsumerWidget {
  final bool isSignUp;
  final Map<String, TextEditingController>? controllers;
  final List<TeacherModel> teachers;

  final Widget navigateWidget;

  const TeachersGridView(
      {super.key,
      this.isSignUp = false,
      this.controllers,
      required this.navigateWidget,
      required this.teachers});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEmptyList = teachers.isEmpty;
    return isEmptyList
        ? _EmptyTeachersList(
            navigateWidget: navigateWidget,
          )
        : GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: teachers.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return AddTeacherDialog(
                  navigateWidget: navigateWidget,
                );
              }

              final teachersData = teachers[index - 1];

              return WidgetAnimator(
                delay:
                    Duration(milliseconds: AppConsts.animatedDuration * index),
                child: TeacherCard(
                  navigateWidget: navigateWidget,
                  teacher: teachersData,
                  isSignUp: isSignUp,
                ),
              );
            },
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.1,
              crossAxisSpacing: 20,
              mainAxisSpacing: 20,
            ),
          );
  }
}

class _EmptyTeachersList extends StatelessWidget {
  final Widget navigateWidget;

  const _EmptyTeachersList({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AddTeacherDialog(
          navigateWidget: navigateWidget,
        ).sized(height: 120.h, width: 140.w),
        context.largeGap,
        Center(
          child: Text(
            context.tr.noTeachers,
            style: textTheme(context).headlineMedium,
          ),
        )
      ],
    );
  }
}
