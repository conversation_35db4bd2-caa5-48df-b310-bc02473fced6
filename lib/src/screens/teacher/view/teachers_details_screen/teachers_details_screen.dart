import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/teacher_activity_add_list.dart';
import 'package:connectify_app/src/screens/teacher/controllers/teacher_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/screens/teacher/view/teachers_details_screen/widgets/teacher_tab_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/consts/app_constants.dart';
import '../../../../shared/widgets/shared_widgets.dart';
import '../../../home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'widgets/teacher_info_tab/teacher_info_tab.dart';

class TeachersDetailsScreen extends ConsumerWidget {
  final TeacherModel teacher;

  const TeachersDetailsScreen({super.key, required this.teacher});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrl = ref.read(teacherTabBarController);
    final tabCtrlProvider = ref.watch(teacherTabBarControllerProvider);
    final List<String> tabs = [
      context.tr.teacherInfo,
      context.tr.dailySchedule,
    ];

    return SafeArea(
      child: Scaffold(
        appBar: MainAppBar(title: context.tr.back, isBackButton: true),
        body: Column(
          children: [
            //! Teacher Image & Tab Bar & Divider
            DetailsTopSectionWidget(
                imagePath: teacher.image?.url ?? '',
                name: teacher.name,
                description: teacher.description,
                errorImage: AppConsts.teacherPlaceholder,
                tabBarWidget: TeacherTabBar(
                  tabs: tabs,
                  initialIndex: 0,
                  onTab: (index) {
                    tabCtrl.changeIndex(index);
                  },
                  teacherModel: teacher,
                )),
            context.mediumGap,
            //! Button Assign To Class
            Expanded(
                child: _SelectedScreen(
              currentIndex: tabCtrlProvider,
              teacher: teacher,
            ).paddingSymmetric(horizontal: AppSpaces.mediumPadding)),
            context.mediumGap,
          ],
        ),
      ),
    );
  }
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;
  final TeacherModel teacher;

  const _SelectedScreen({required this.currentIndex, required this.teacher});

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return TeacherInfoTab(
          teacher: teacher,
        );
      case 1:
        return const TeacherActivityAddList();
    }
    return const SizedBox.shrink();
  }
}
