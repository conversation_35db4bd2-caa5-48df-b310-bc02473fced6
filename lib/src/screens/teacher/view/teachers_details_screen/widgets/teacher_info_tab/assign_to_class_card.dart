import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/widgets/shared_widgets.dart';
import '../../../../../class/controllers/class_tab_bar_controller.dart';
import '../../../../controllers/teacher_controller.dart';

class AssignClassCard extends ConsumerWidget {
  final bool isTeacherFound;
  final ClassModel classModel;
  final TeacherModel teacher;

  const AssignClassCard(
      {super.key,
      required this.classModel,
      required this.isTeacherFound,
      required this.teacher});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherChangeNotifier =
        ref.watch(teacherChangeNotifierProvider(context));
    final tabCtrl = ref.read(tabBarController);

    return BaseContainer(
        margin: 15,
        boxShadow: ConstantsWidgets.boxShadow,
        child: Row(
          children: [
            BaseCachedImage(
              classModel.image?.url ?? '',
              height: 40.h,
              width: 40.w,
              radius: AppRadius.baseContainerRadius,
            ),
            context.smallGap,
            Text(
              classModel.name,
              style: context.blueHint,
            ),
            const Spacer(),
            if (isTeacherFound)
              Text(
                context.tr.assigned,
                style: context.hint.copyWith(color: Colors.green),
              )
            else
              TextButton(
                onPressed: () {
                  final assignedTeacher = teacher.copyWith(
                      classes: teacher.classes!..add(classModel));
                  teacherChangeNotifier
                      .updateTeacher(teacher: assignedTeacher)
                      .then((value) {
                    context.toReplacement(const ClassesScreen());
                    tabCtrl.changeIndex(0);
                  });
                },
                child: Text(
                  context.tr.assign,
                  style:
                      context.hint.copyWith(color: ColorManager.primaryColor),
                ),
              )
          ],
        ));
  }
}
