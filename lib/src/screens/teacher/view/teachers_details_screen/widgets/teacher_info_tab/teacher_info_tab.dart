import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/widgets/class_card.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/widgets/add_teacher_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/widgets/shared_widgets.dart';
import '../../../../models/teacher_model.dart';

class TeacherInfoTab extends StatelessWidget {
  final TeacherModel teacher;

  const TeacherInfoTab({super.key, required this.teacher});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.phoneNumber} : ${teacher.phone}',
            style: context.title,
            textAlign: TextAlign.start,
          ),
          context.mediumGap,
          AddRectangleWidget(
            title: context.tr.assignToClass,
            onTap: () {
              showAddTeacherDialog(
                context,
                navigateWidget: const ClassesScreen(),
                teacher: teacher,
                viewOnlyClasses: true,
              );

              // showDialog(
              //   context: context,
              //   builder: (context) {
              //     return AssignToClassButton(
              //       teacher: teacher,
              //     );
              //   },
              // );
            },
          ),
          context.largeGap,
          BaseList(
            physics: const NeverScrollableScrollPhysics(),
            data: teacher.teacherClasses,
            itemBuilder: (data, index) {
              return ClassCard(
                classModel: data ?? ClassModel.empty(),
                isSignUp: true,
              );
            },
          ),
        ],
      ),
    );
  }
}
