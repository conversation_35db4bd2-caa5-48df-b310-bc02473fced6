import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';
import '../../nursery/models/nursery_model.dart';

List<TeacherModel> responseToTeacherModelList(response) {
  final data = (response as List?) ?? [];

  final teachers = data.map((e) => TeacherModel.fromJson(e)).toList();

  return teachers;
}

class TeacherModel extends UserModel {
  final List<ClassModel?> teacherClasses;
  final bool isActive;

  const TeacherModel({
    super.id,
    super.name,
    super.description,
    super.image,
    super.phone,
    super.fcmToken,
    this.teacherClasses = const [],
    this.isActive = true,
  });

  factory TeacherModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.image] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.image])
        : null;

    return TeacherModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.username] ?? '',
      fcmToken: json[ApiStrings.fcmToken] ?? '',
      description: json[ApiStrings.jobTitle] ?? '',
      phone: json[ApiStrings.phone] ?? '',
      isActive: json[ApiStrings.isActive] ?? true,
      teacherClasses: json[ApiStrings.teacherClasses] != null
          ? (json[ApiStrings.teacherClasses] as List)
              .map<ClassModel>((e) => ClassModel.fromJsonWithOutAttributes(e))
              .toList()
          : [],
      image: image,
    );
  }

  factory TeacherModel.fromAttributesJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return TeacherModel(
      id: json[ApiStrings.id],
      fcmToken: json[ApiStrings.fcmToken],
      name:
          attributes[ApiStrings.username] ?? attributes[ApiStrings.name] ?? '',
      description: attributes[ApiStrings.jobTitle] ?? '',
    );
  }

  //? Copy With
  @override
  TeacherModel copyWith({
    int? id,
    String? name,
    String? description,
    BaseMediaModel? image,
    String? phone,
    List<ClassModel?>? teacherClasses,
    List<ClassModel>? classes,
    UserTypeEnum? userType,
    String? email,
    String? password,
    String? fcmToken,
    List<(int? studentId, int? classId)>? studentIds,
    NurseryModel? nurseryModel,
  }) {
    return TeacherModel(
      id: id ?? this.id,
      teacherClasses: teacherClasses ?? this.teacherClasses,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      fcmToken: fcmToken ?? this.fcmToken,
    );
  }

  @override
  Map<String, dynamic> toJson(
      {bool isEdit = false,
      //? For Parents
      int? studentId,
      int? classId,
      bool sendUsername = true}) {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.username: name,
      ApiStrings.jobTitle: description,
      ApiStrings.phone: phone,
      ApiStrings.type: ApiStrings.teacher,
      if (teacherClasses.isNotEmpty)
        ApiStrings.teacherClasses: teacherClasses.map((e) => e?.id).toList(),
      // if (classModel != null)
      // ApiStrings.classString: teacherClasses.firstOrNull?.id,
      // if (classModel?.id != null) ApiStrings.classString: classModel?.id,
      if (!isEdit) ApiStrings.email: '$<EMAIL>',
      if (!isEdit) ApiStrings.password: '${name.trim()}@1234',
    };
  }

  Map<String, dynamic> toAssignedClassJson({bool unAssign = false}) {
    return {
      ApiStrings.classString:
          unAssign ? AppConsts.class0ID : classes?.map((e) => e.id).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        image,
        phone,
        fcmToken,
        teacherClasses,
        classes,
      ];
}
