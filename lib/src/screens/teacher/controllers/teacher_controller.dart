import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/screens/teacher/repos/teacher_repo.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/teachers_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

// * Teacher Provider Controller ==================================
final teacherProviderController =
    Provider.family<TeacherController, BuildContext>((ref, context) {
  final teacherRepo = ref.watch(teacherRepoProvider);

  return TeacherController(context, teacherRepo: teacherRepo);
});

// * Change Notifier Teacher Controller ==================================
final teacherChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherController, BuildContext>(
        (ref, context) {
  final teacherRepo = ref.watch(teacherRepoProvider);

  return TeacherController(context, teacherRepo: teacherRepo);
});

/// * Get Teacher Data ==========================================
final getTeacherDataProvider =
    FutureProvider.family<List<TeacherModel>, BuildContext>(
        (ref, context) async {
  final teacherController = ref.watch(teacherProviderController(context));

  return await teacherController.getTeachersData();
});

/// * Get Teacher By Class Data ==========================================
final getTeacherByClassDataProvider =
    FutureProvider.family<List<TeacherModel>, (BuildContext, int classId)>(
        (ref, params) async {
  final context = params.$1;
  final classId = params.$2;

  final teacherController = ref.watch(teacherProviderController(context));

  final filteredTeachers =
      await teacherController.getTeachersByClassData(classId: classId);

  return filteredTeachers;
});

//? =============================================================
class TeacherController extends BaseVM {
  final TeacherRepo teacherRepo;
  final BuildContext context;

  TeacherController(this.context, {required this.teacherRepo});

  //? Get Teachers ------------------------------------------------------------
  Future<List<TeacherModel>> getTeachersData() async {
    return await baseFunction(context, () {
      final teachers = teacherRepo.getTeachers();
      return teachers;
    });
  }

  //? Get Teachers By Class ------------------------------------------------------------
  Future<List<TeacherModel>> getTeachersByClassData(
      {required int classId}) async {
    return await baseFunction(context, () {
      final teachers = teacherRepo.getTeachersByClass(classId: classId);
      return teachers;
    });
  }

  //? Add Teacher
// ? ------------------------------------------------------------
  Future<void> addTeacher(
      {required Map<String, TextEditingController> controllers,
      required List<ClassModel?>? selectedClasses,
      required Widget navigateWidget,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final teacher = TeacherModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.jobTitle]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        teacherClasses: selectedClasses ?? [],
      );

      await teacherRepo.addTeacher(
        teacher: teacher,
        pickedImage: pickedImage,
      );

      NotificationService.sendNotification(
        title: "Welcome New Teacher",
        body: "A new teacher named ${teacher.name} has joined the staff.",
        userTokenOrTopic: NurseryModelHelper.allTeacherTopic(),
        isTopic: true,
      );

      postNewNotification(
        notificationModel: NotificationModel(
          title: "Welcome New Teacher",
          body: "A new teacher named ${teacher.name} has joined the staff.",
          topic: NurseryModelHelper.allTeacherTopic(),
        ),
      );

      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.addedSuccessfully);
      }
    }, additionalFunction: (_) => getTeachersData());
  }

  //? Edit Teachers Data ----------------------------------------------------
  Future<void> editTeacher(
      {required Map<String, TextEditingController> controllers,
      required List<ClassModel?>? selectedClasses,
      required int id,
      required String pickedImage,
      required Widget navigateWidget,
      required String? fcmToken}) async {
    return await baseFunction(context, () async {
      final teacher = TeacherModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.jobTitle]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        teacherClasses: selectedClasses ?? [],
        fcmToken: fcmToken,
      );

      await teacherRepo.editTeacher(
        teacher: teacher,
        pickedImage: pickedImage,
        id: id,
      );

      log('afafsadgasgdsg ${teacher.name} FVCCC ${fcmToken}');

      // NotificationService.sendNotification(
      //   title: "Teacher Assigned to Class",
      //   body: "You have been assigned to class ${teacher.classes?.name}",
      //   userTokenOrTopic: fcmToken ?? '',
      // );

      // postNewNotification(
      //   notificationModel: NotificationModel(
      //     title: "Teacher Assigned to Class",
      //     body: "You have been assigned to class ${teacher.classes?.name}",
      //     teacher: teacher,
      //   ),
      // );

      for (var item in selectedClasses!) {
        if (item != null) {
          NotificationService.sendNotification(
            title: "Teacher Assigned to Class",
            body: "You have been assigned to class ${item.name}",
            userTokenOrTopic: fcmToken ?? '',
          );

          postNewNotification(
            notificationModel: NotificationModel(
              title: "Teacher Assigned to Class",
              body: "You have been assigned to class ${item.name}",
              teacher: teacher,
            ),
          );
        }
      }

      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.editSuccessfully);
      }
    }, additionalFunction: (_) => getTeachersData());
  }

  //? Delete Teacher ===============================
  Future<void> deleteTeacher({
    required int id,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      await teacherRepo.deleteTeacher(id: id);
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      }
    });
  }

  //? Active or DeActive Student Data ------------------------------------------
  Future<void> activeOrDeActiveStudent(
      {required int id, required bool isActive}) async {
    return await baseFunction(context, () async {
      await teacherRepo.activeDeActiveTeacher(id: id, isActive: isActive);
      if (!context.mounted) return;

      context.toReplacement(const TeachersScreen());

      context.showBarMessage(context.tr.editSuccessfully);
    });
  }

  Future<void> updateTeacher(
      {required TeacherModel teacher, bool unAssign = false}) async {
    return await baseFunction(context, () async {
      await teacherRepo.updateUser(teacher: teacher, unAssign: unAssign);

      // NotificationService.sendNotification(
      //   title: "Teacher Assigned to Class",
      //   body: "You have been assigned to class ${teacher.classes?.name}",
      //   userTokenOrTopic: teacher.fcmToken ?? '',
      // );
      //
      // postNewNotification(
      //   notificationModel: NotificationModel(
      //     title: "Teacher Assigned to Class",
      //     body: "You have been assigned to class ${teacher.classes?.name}",
      //     teacher: teacher,
      //   ),
      // );

      for (var item in teacher.teacherClasses) {
        if (item != null) {
          NotificationService.sendNotification(
            title: "Teacher Assigned to Class",
            body: "You have been assigned to class ${item.name}",
            userTokenOrTopic: teacher.fcmToken ?? '',
          );

          postNewNotification(
            notificationModel: NotificationModel(
              title: "Teacher Assigned to Class",
              body: "You have been assigned to class ${item.name}",
              teacher: teacher,
            ),
          );
        }
      }
    });
  }
}
