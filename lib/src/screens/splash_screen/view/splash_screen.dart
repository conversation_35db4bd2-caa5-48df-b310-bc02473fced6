import 'dart:developer';

import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/attendees_of_today.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/current_activity.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/onboarding/controllers/onboarding_controller.dart';
import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/view/onboarding_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_endpoints.dart';
import '../../Activities/controllers/teacher_activities_controller.dart';
import '../../nursery/models/nursery_model.dart';

class SplashScreen extends HookConsumerWidget {
  final bool fromLogout;

  const SplashScreen({super.key, this.fromLogout = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onBoardingCtrl = ref.watch(onBoardingController(context));
    final onBoardingPage = useState<List<OnBoardingModel>>([]);

    void getOnBoardingData() async {
      final onBoardingPages = await onBoardingCtrl.getOnBoardingData();
      onBoardingPage.value = onBoardingPages;
    }

    final haveSeenOnBoarding =
        GetStorageService.getLocalData(key: LocalKeys.haveSeenOnBoarding) ??
            false;

    final user = const UserModel().currentUser;

    final signedUser = user != UserModel.empty();

    final teacherActivityCtrl =
        ref.watch(getTeacherActivitiesByTodayDataProvider(context));

    if (const UserModel().isAdmin) {
      teacherActivityCtrl.when(
        data: (teacherActivities) {
          final filteredActivities = teacherActivities.where(
            (element) {
              if (element.date.isNotEmpty && !element.isWeekly) {
                final date = DateTime.parse(element.date);
                final now = DateTime.now();
                return date.day == now.day &&
                    date.month == now.month &&
                    date.year == now.year;
              }

              return true;
            },
          ).toList();

          currentActivities.value = filteredActivities;
          return filteredActivities;
        },
        loading: () => <TeacherActivityModel>[],
        error: (error, stackTrace) => <TeacherActivityModel>[],
      );
    }

    final attendanceCtrl =
        ref.watch(getAttendanceDataForTodayProvider(context));

    final studentCtrl = ref.watch(getActiveStudentsCountProvider(context));

    final activeStudents = studentCtrl.when(
      data: (activeStudentsData) {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          StudentController.allActiveStudentsLength.value = activeStudentsData;
        });

        return activeStudentsData;
      },
      loading: () => 0,
      error: (error, stackTrace) => 0,
    );

    attendanceCtrl.when(
      data: (attendance) {
        final selectedClassId = GetStorageService.getLocalData(
          key: LocalKeys.savedTeacherClassId,
        );

        log('asfasfasfasfsa ${attendance.map(
          (e) => '${e.classModel?.name}: ${e.classModel?.id}',
        )} DDD ${selectedClassId} FFF ${const UserModel().currentUser.classes?.firstOrNull}');

        final filteredAttendanceBySelectedTeacherClass = const UserModel()
                .isTeacher
            ? attendance
                .where((element) =>
                    element.classModel?.id ==
                    (selectedClassId ??
                        const UserModel().currentUser.classes?.firstOrNull?.id))
                .toList()
            : attendance;

        final value =
            filteredAttendanceBySelectedTeacherClass.length / activeStudents;

        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          attendanceCount.value =
              filteredAttendanceBySelectedTeacherClass.length;
          attendanceValue.value = value;
        });

        return filteredAttendanceBySelectedTeacherClass.length / activeStudents;
      },
      loading: () => 0,
      error: (error, stackTrace) => 0,
    );

    final isNurserySubscriptionExpired = useState(false);

    final isLoading = useState(true);

    Future<void> getNurseryById({required int? id}) async {
      final response = await NetworkApiServices().getResponse(
        ApiEndpoints.nurseryById(id),
      );

      final data = response['data'] != null && response['data'].isNotEmpty
          ? response['data']
          : null;

      if (data == null) return;

      final nurseryModel = NurseryModel.fromAttributesJson(data);

      isNurserySubscriptionExpired.value =
          nurseryModel?.endDate?.isBefore(DateTime.now()) ?? false;

      log('asfsafasff ${isNurserySubscriptionExpired.value} gggg ${nurseryModel?.endDate}');

      isLoading.value = false;
    }

    useEffect(() {
      if (signedUser) {
        getNurseryById(id: NurseryModelHelper.currentNurseryId()).then(
          (value) {
            if (isNurserySubscriptionExpired.value) {
              QuickAlert.show(
                context: context,
                title: context.tr.warning,
                disableBackBtn: true,
                text: context.tr.subscriptionExpiredPleaseContactSupport,
                type: QuickAlertType.info,
                confirmBtnText: context.tr.contactSupport,
                cancelBtnText: context.tr.cancel,
                onConfirmBtnTap: () {
                  launchUrl(
                    Uri.parse('https://wa.me/201014878502'),
                  );
                },
              );
            }
          },
        );
      } else {
        isLoading.value = false;
      }

      return;
    }, []);

    if ((signedUser && !fromLogout) &&
        (teacherActivityCtrl.isLoading ||
            attendanceCtrl.isLoading ||
            studentCtrl.isLoading ||
            isLoading.value ||
            isNurserySubscriptionExpired.value)) {
      return Scaffold(
        body: Center(
          child: Image.asset(
            Assets.imagesSplash,
            height: context.height,
            width: context.width,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    useEffect(() {
      if (signedUser && !fromLogout) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.toReplacement(const MainScreen());
        });
        return;
      }

      if (!haveSeenOnBoarding) {
        getOnBoardingData();

        GetStorageService.setLocalData(
          key: LocalKeys.haveSeenOnBoarding,
          value: true,
        );

        // * Navigate to On Boarding after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          context.toReplacement(OnBoardingScreen(
            onBoardingPages: onBoardingPage.value,
          ));
        });
      } else {
        Future.delayed(const Duration(seconds: 3), () {
          context.toReplacement(const SignInScreen());
        });
      }

      // if (haveSeenOnBoarding) {
      //   Future.delayed(const Duration(seconds: 3), () {
      //     context.toReplacement(const SignInScreen());
      //   });
      // }

      return;
    }, []);

    // if (!showOnBoarding && fromLogout) {
    //   Log.w('asffaassasfa');
    //   return const SignInScreen();
    // }

    return Scaffold(
      body: Center(
        child: Image.asset(
          Assets.imagesSplash,
          height: context.height,
          width: context.width,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
