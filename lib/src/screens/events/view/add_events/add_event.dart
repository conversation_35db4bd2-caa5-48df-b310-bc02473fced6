import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/events/controllers/event_controller.dart';
import 'package:connectify_app/src/screens/events/models/event_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/teacher_drop_down.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/services/media/controller/media_controller.dart';

class AddEventDialog extends HookConsumerWidget {
  const AddEventDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return Button(
        label: context.tr.addNewEvent,
        onPressed: () {
          showAddEventDialog(
            context,
          ).then((value) {
            mediaController.clearFiles();
          });
        });
  }
}

Future<void> showAddEventDialog(
  BuildContext context, {
  EventModel? event,
}) async {
  context.to(_AddEventScreen(
    event: event,
  ));
}

class _AddEventScreen extends StatelessWidget {
  final EventModel? event;

  const _AddEventScreen({super.key, this.event});

  @override
  Widget build(BuildContext context) {
    return HookConsumer(
      builder: (context, ref, child) {
        final controllers = {
          ApiStrings.eventName: useTextEditingController(text: event?.name),
          ApiStrings.description:
              useTextEditingController(text: event?.description),
        };

        final valueNotifiers = {
          ApiStrings.date: useState<DateTime?>(event?.startTime),
          ApiStrings.classString: useState<List<ClassModel>>([]),
          ApiStrings.teachers: useState<List<TeacherModel>?>(null),
        };

        final isParentPhoneAdded = useState(false);

        final formKey = useState(GlobalKey<FormState>());

        void clearData() {
          controllers[ApiStrings.eventName]!.clear();
          controllers[ApiStrings.description]!.clear();

          valueNotifiers[ApiStrings.classString]!.value = null;
          valueNotifiers[ApiStrings.date]!.value = null;
          valueNotifiers[ApiStrings.teachers]!.value = null;

          isParentPhoneAdded.value = false;

          ref.watch(mediaPickerControllerProvider).clearFiles();
        }

        final eventChangeNotifier =
            ref.watch(eventChangeNotifierController(context));

        final filePath = ref.watch(mediaPickerControllerProvider).filePath;

        final isEdit = event != null;
        Future<void> addEditEvent() async {
          if (isEdit) {
            await eventChangeNotifier.editEvent(
                controllers: controllers,
                valueNotifiers: valueNotifiers,
                pickedImage: filePath,
                id: event?.id);
          } else {
            await eventChangeNotifier.addEvent(
              controllers: controllers,
              valueNotifiers: valueNotifiers,
              pickedImage: filePath,
            );
          }
        }

        return PopScope(
          onPopInvoked: (didPop) =>
              ref.watch(mediaPickerControllerProvider).clearFiles(),
          child: Scaffold(
            appBar: MainAppBar(
              title: isEdit ? context.tr.editEvent : context.tr.addNewEvent,
              isBackButton: true,
            ),
            body: SafeArea(
              child: Form(
                key: formKey.value,
                child: Padding(
                  padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        //! Birth Date Field
                        BaseDatePicker(
                          isRequired: true,
                          selectedDateNotifier: valueNotifiers[ApiStrings.date]!
                              as ValueNotifier<DateTime?>,
                          label: context.tr.date,
                        ),

                        context.largeGap,

                        //! Event Name
                        BaseTextField(
                          title: context.tr.eventName,
                          controller: controllers[ApiStrings.eventName],
                        ),

                        context.largeGap,

                        //! Class Drop Down
                        MultiClassDropDown(
                          selectedClasses:
                              valueNotifiers[ApiStrings.classString]!
                                  as ValueNotifier<List<ClassModel>>,
                          classIds: event?.classes?.map((e) => e.id).toList(),
                        ),

                        context.largeGap,

                        //! Teacher Drop Down
                        TeachersDropDown(
                          selectedTeachers: valueNotifiers[ApiStrings.teachers]!
                              as ValueNotifier<List<TeacherModel>?>,
                          teacherIds:
                              event?.teachers?.map((e) => e.id).toList(),
                        ),

                        context.largeGap,

                        //! Description
                        BaseTextField(
                          title: context.tr.description,
                          controller: controllers[ApiStrings.description],
                          maxLines: 4,
                        ),

                        context.xlLargeGap,

                        Button(
                            loadingWidget: const LoadingWidget(),
                            isLoading: eventChangeNotifier.isLoading,
                            label: context.tr.save,
                            onPressed: () async {
                              if (!formKey.value.currentState!.validate()) {
                                return;
                              }

                              await addEditEvent();
                              clearData();
                            })
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
