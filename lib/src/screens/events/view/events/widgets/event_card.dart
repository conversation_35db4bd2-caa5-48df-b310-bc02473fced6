import 'package:connectify_app/src/screens/events/controllers/event_controller.dart';
import 'package:connectify_app/src/screens/events/models/event_model.dart';
import 'package:connectify_app/src/screens/events/view/add_events/add_event.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_popupmenu/base_popupmenu.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

class EventCard extends ConsumerWidget {
  final EventModel event;

  const EventCard({super.key, required this.event});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final eventCtrl = ref.watch(eventChangeNotifierController(context));

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 14),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      decoration: BoxDecoration(
        color: ColorManager.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: ColorManager.black.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              context.smallGap,
              Text(
                event.name ?? '-',
                style:
                    context.subTitle.copyWith(color: ColorManager.primaryColor),
                maxLines: 1,
              ),
              context.xSmallGap,
              Text(
                event.description ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: context.labelLarge,
              ),
            ],
          ),
          Positioned(
            right: -5.w,
            child: BasePopupmenu(
              editOnTap: () => showAddEventDialog(context, event: event),
              deleteOnTap: () => showDialog(
                  context: context,
                  builder: (context) => BaseDeleteDialog(
                      description: context.tr.areYouSureToDeleteThisEvent,
                      onConfirm: () async {
                        await eventCtrl.deleteEvent(event: event);
                      })),
            ),
          ),
          Positioned(
            right: 0,
            bottom: -3,
            child: Text(
              DateFormat.jm().format(event.startTime!),
              style: context.labelMedium.copyWith(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
}
