import 'package:connectify_app/src/screens/events/models/event_model.dart';
import 'package:connectify_app/src/screens/events/view/add_events/add_event.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../auth/models/user_model.dart';
import '../../controllers/event_controller.dart';
import '../widgets/calendar_view.dart';
import 'widgets/events_list.dart';

class EventsScreen extends HookConsumerWidget {
  const EventsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(DateTime.now());

    final allEvents = ref.watch(allEventsDataProvider(context));
    final filteredEvents =
        ref.watch(filteredEventsProvider((context, selectedDate.value)));



    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          bottomNavigationBar:
              const   UserModel().isAdmin ? const AddEventDialog().paddingAll(AppSpaces.mediumPadding) : null,
          appBar: MainAppBar(
            title: context.tr.events,
            isBackButton: true,
          ),
          body: allEvents.get(
            data: (data) => Column(
              children: [
                CalendarView(
                  events: data,
                  selectedDay: selectedDate,
                ),
                context.mediumGap,

                filteredEvents.get(data: (events) => Column(
                  children: [
                    events.isNotEmpty
                        ? EventsList(
                          events: events,
                        )
                        : Padding(
                      padding: const EdgeInsets.only(top: 30.0),
                      child: Center(
                        child: Text(
                          context.tr.noEvents,
                          style: textTheme(context).headlineMedium,
                        ),
                      ),
                    )
                  ],
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
