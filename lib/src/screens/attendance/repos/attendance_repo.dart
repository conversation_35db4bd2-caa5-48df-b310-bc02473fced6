import 'dart:developer';

import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final attendanceRepoProvider = Provider<AttendanceRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return AttendanceRepo(networkApiService);
});

class AttendanceRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  AttendanceRepo(this._networkApiService);

  //? Get Attendance Data ------------------------------------------
  Future<List<AttendanceModel>> getAttendance() async {
    return baseFunction(
      () async {
        final response =
            await _networkApiService.getResponse(ApiEndpoints.attendances);

        final attendanceData = compute(responseToAttendanceModelList, response);

        return attendanceData;
      },
    );
  }

  // Other methods...

  Future<List<AttendanceModel>> getAttendancesDataByDate({
    required String date,
  }) async {
    final response = await _networkApiService.getResponse(
      '${ApiEndpoints.attendances}&filters[attendance_date][\$eq]=$date',
    );

    log('asfsaffs ${response}');

    return compute(responseToAttendanceModelList, response);
  }

  Future<List<AttendanceModel>> getAttendancesDataByMonth({
    required String monthName,
    required int? studentId,
  }) async {
    if (studentId == null) return [];

    final month =
        DateFormat.MMMM('en').parse(monthName).month.toString().padLeft(2, '0');

    final response = await _networkApiService.getResponse(
      '${ApiEndpoints.attendances}&filters[attendance_date][\$contains]=-$month-&filters[student][id]=$studentId',
      // '${ApiEndpoints.attendances}&filters[attendance_date][\$contains]=-$month-'
    );

    return compute(responseToAttendanceModelList, response);
  }

//? Add Attendance Data ------------------------------------------

  Future<void> addAttendance({required AttendanceModel attendance}) async {
    return await baseFunction(() async {
      final res = await _networkApiService.postResponse(
        ApiEndpoints.attendances,
        body: attendance.toJson(),
      );
    });
  }

  //? Delete Attendance Data ------------------------------------------
  Future<void> deleteAttendance({required int id}) async {
    return await baseFunction(() async {
      await _networkApiService
          .deleteResponse('${ApiEndpoints.deleteAttendances}/$id');
    });
  }
}
