import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

List<AttendanceModel> responseToAttendanceModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final attendance = data.map((e) => AttendanceModel.fromJson(e)).toList();

  return attendance;
}

class AttendanceModel {
  final int? id;
  final StudentModel? student;
  final TeacherModel? teacher;
  final ClassModel? classModel;
  final String attendanceDate;

  const AttendanceModel({
    this.id,
    this.student,
    this.teacher,
    this.classModel,
    this.attendanceDate = '',
  });

  //? From Json
  factory AttendanceModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    final teacher = attributes[ApiStrings.teacher][ApiStrings.data] != null
        ? TeacherModel.fromAttributesJson(
            attributes[ApiStrings.teacher][ApiStrings.data])
        : null;

    final student = attributes[ApiStrings.student][ApiStrings.data] != null
        ? StudentModel.fromJson(attributes[ApiStrings.student][ApiStrings.data])
        : null;
    final classModel =
        attributes[ApiStrings.classString][ApiStrings.data] != null
            ? ClassModel.fromJson(
                attributes[ApiStrings.classString][ApiStrings.data])
            : null;

    return AttendanceModel(
      id: json[ApiStrings.id],
      attendanceDate: attributes[ApiStrings.attendanceDate],
      student: student,
      teacher: teacher,
      classModel: classModel,
    );
  }

  //? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      ApiStrings.student: student?.id,
      ApiStrings.attendanceDate: DateTime.now().formatDateToString,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.classString: selectedTeacherClass.value?.id,
      // const UserModel().currentUser.classes?.id,
      // ApiStrings.nursery: NurseryModel.currentNurseryId(),
    };
  }
}
