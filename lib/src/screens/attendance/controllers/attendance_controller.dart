import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/attendance/repos/attendance_repo.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * Attendances Provider Controller ========================================
final attendancesProviderController =
    Provider.family<AttendancesController, BuildContext>((ref, context) {
  final attendanceRepo = ref.watch(attendanceRepoProvider);

  return AttendancesController(context, attendanceRepo: attendanceRepo);
});

final attendancesChangeNotifierController =
    ChangeNotifierProvider.family<AttendancesController, BuildContext>(
        (ref, context) {
  final attendanceRepo = ref.watch(attendanceRepoProvider);

  return AttendancesController(context, attendanceRepo: attendanceRepo);
});

// * Get Activities Data ========================================
final getAttendanceDataProvider =
    FutureProvider.family<List<AttendanceModel>, BuildContext>(
        (ref, context) async {
  final attendancesCtrl = ref.watch(attendancesProviderController(context));

  return await attendancesCtrl.getAttendancesData();
});

// * Get Activities Data For Today ========================================
final getAttendanceDataForTodayProvider =
    FutureProvider.family<List<AttendanceModel>, BuildContext>(
        (ref, context) async {
  final attendancesCtrl = ref.watch(attendancesProviderController(context));

  final today = DateTime.now().formatDateToString;

  return await attendancesCtrl.getAttendancesDataByDate(
    date: today,
  );
});

// * Get Activities Data By Month ========================================
final getAttendanceDataByDateProvider = FutureProvider.family<
    List<AttendanceModel>,
    (BuildContext, String date, int? studentId)>((ref, params) async {
  final attendancesCtrl = ref.watch(attendancesProviderController(params.$1));

  return await attendancesCtrl.getAttendancesDataByMonthName(
    monthName: params.$2,
    studentId: params.$3,
  );
});

//?==============================================================
class AttendancesController extends BaseVM {
  final BuildContext context;
  final AttendanceRepo attendanceRepo;

  AttendancesController(this.context, {required this.attendanceRepo});

  //? Get Activities Data ------------------------------------------
  Future<List<AttendanceModel>> getAttendancesData() async {
    return await baseFunction(
      context,
      () async {
        final attendances = await attendanceRepo.getAttendance();

        // Log.w('attendances =========== ${attendances.map((e) => e.toJson())}');

        return attendances;
      },
    );
  }

  //? Get Activities Data ------------------------------------------
  Future<List<AttendanceModel>> getAttendancesDataByDate(
      {required String date}) async {
    return await baseFunction(
      context,
      () async {
        final attendances =
            await attendanceRepo.getAttendancesDataByDate(date: date);

        Log.i('totalAttendance ${attendances.map((e) => e.attendanceDate)}');

        return attendances;
      },
    );
  }

  Future<List<AttendanceModel>> getAttendancesDataByMonthName({
    required String monthName,
    required int? studentId,
  }) async {
    return await baseFunction(
      context,
      () async {
        final attendances = await attendanceRepo.getAttendancesDataByMonth(
            monthName: monthName, studentId: studentId);

        Log.i('totalAttendance ${attendances.map((e) => e.attendanceDate)}');

        return attendances;
      },
    );
  }

  // Future<List<AttendanceModel>> getAttendancesDataForToday() async {
  //   return await baseFunction(
  //     context,
  //     () async {
  //       final attendances = await attendanceRepo.getAttendance();
  //
  //       final today = attendances.lastOrNull?.attendanceDate;
  //
  //
  //       final totalAttendance = attendances
  //           .where((element) =>
  //               element.attendanceDate == DateTime.now().formatDateToString)
  //           .toList();
  //
  //
  //       Log.i(
  //           'totalAttendance ${totalAttendance.map((e) => e.attendanceDate)}');
  //
  //       return totalAttendance;
  //     },
  //   );

//? Add Attendance Data ------------------------------------------
  Future<void> addAttendance({required AttendanceModel attendance}) async {
    return await baseFunction(context, () async {
      await attendanceRepo.addAttendance(attendance: attendance);

      NotificationService.sendNotification(
        title: "Student Attendance",
        body: "${attendance.student?.name} has been marked as present.",
        userTokenOrTopic:
            NurseryModelHelper.parentByStudentTopic(attendance.student?.id),
        isTopic: true,
      );
      postNewNotification(
          notificationModel: NotificationModel(
              title: "Student Attendance",
              body: "${attendance.student?.name} has been marked as present.",
              topic: NurseryModelHelper.parentByStudentTopic(
                  attendance.student?.id)));

      // if (!context.mounted) return;
      // context.to(const AttendanceScreen());
    });
  }

  //? Delete Attendance Data ------------------------------------------
  Future<void> deleteAttendance({required int id}) async {
    return await baseFunction(context, () async {
      await attendanceRepo.deleteAttendance(id: id);
      // if (!context.mounted) return;
      // context.to(const AttendanceScreen());
    });
  }

  void onNext(
      {required List attendanceDates,
      required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value = (selectedIndex.value + 1) % attendanceDates.length;
  }

  void onPrev(
      {required List attendanceDates,
      required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value = (selectedIndex.value - 1 + attendanceDates.length) %
        attendanceDates.length;
  }

  List<String> attendanceDates({required List<AttendanceModel> attendances}) =>
      attendances.map((e) => e.attendanceDate).toList();

  int lastAttendanceIndex({required List<AttendanceModel> attendances}) =>
      attendances.indexWhere((element) => attendances.last.id == element.id);
}
