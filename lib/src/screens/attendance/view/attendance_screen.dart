import 'package:connectify_app/src/screens/attendance/view/widgets/attendance_card.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/attendance_filter_widget/main_attendance_filter.dart';

class AttendanceScreen extends StatelessWidget {
  const AttendanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        isClickAttendance.clear();

        return false;
      },
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          appBar: MainAppBar(
            title: context.tr.attendance,
            isBackButton: true,
          ),
          body: const AttendanceFilterWidget()
              .scroll()
              .paddingSymmetric(horizontal: AppSpaces.mediumPadding),
        ),
      ),
    );
  }
}
