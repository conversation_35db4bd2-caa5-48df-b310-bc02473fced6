import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/attendance/view/widgets/attendance_card.dart';
import 'package:connectify_app/src/screens/attendance/view/widgets/attendance_filter_widget/attendance_filter_body.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AttendanceFilterWidget extends ConsumerWidget {
  const AttendanceFilterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getAttendancesDataCtrl =
        ref.watch(getAttendanceDataProvider(context));

    final studentsCtrl = ref.watch(getActiveStudents(context));

    return WillPopScope(
      onWillPop: () async {
        isClickAttendance.clear();

        return true;
      },
      child: getAttendancesDataCtrl.when(
        error: (_, __) => const SizedBox(),
        loading: () => const LoadingWidget(),
        data: (attendances) => studentsCtrl.when(
          error: (_, __) => const SizedBox(),
          loading: () => const LoadingWidget(),
          data: (studentsData) {
            final students =
                studentsData.where((element) => element.id != 0).toList();

            return AttendanceFilterBody(
              attendances: attendances,
              students: students,
            );
          },
        ),
      ),
    );
  }
}
