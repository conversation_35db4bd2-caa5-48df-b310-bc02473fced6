import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/date_attendance_filter_widget.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class TopSectionDateFilter extends ConsumerWidget {
  final ValueNotifier<int> selectedIndex;
  final ValueNotifier<List<String>> filteredListByDate;

  const TopSectionDateFilter({
    super.key,
    required this.filteredListByDate,
    required this.selectedIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final attendanceCtrl = ref.watch(attendancesProviderController(context));

    return DateAttendanceFilterWidget(
      showPrevious: _canShowPrevious(),
      showNext: _canShowNext(),
      selectedDay: _getSelectedDate(context),
      onNext: () => attendanceCtrl
        ..onNext(
            attendanceDates: filteredListByDate.value,
            selectedIndex: selectedIndex),
      onPrevious: () => attendanceCtrl
        ..onPrev(
            attendanceDates: filteredListByDate.value,
            selectedIndex: selectedIndex),
    );
  }

  bool _canShowPrevious() {
    return filteredListByDate.value[selectedIndex.value] !=
        filteredListByDate.value.first;
  }

  bool _canShowNext() {
    return filteredListByDate.value[selectedIndex.value] !=
        filteredListByDate.value.last;
  }

  String _getSelectedDate(BuildContext context) {
    return DateTime.now().formatDateToString ==
            filteredListByDate.value[selectedIndex.value]
        ? context.tr.today
        : filteredListByDate.value[selectedIndex.value];
  }
}
