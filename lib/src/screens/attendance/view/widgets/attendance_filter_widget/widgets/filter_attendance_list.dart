import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/attendance/view/widgets/attendance_card.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class FilterAttendanceList extends StatelessWidget {
  final List<AttendanceModel> allAttendances;
  final List<StudentModel> students;
  final bool isToday;
  final String day;

  const FilterAttendanceList(
      {super.key,
      required this.allAttendances,
      required this.students,
      required this.day,
      required this.isToday});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: AppSpaces.mediumPadding),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var studentAttendance = allAttendances.firstWhereOrNull(
              (attendance) => attendance.student?.id == students[index].id);

          final isAttended = studentAttendance != null;
          Log.w(studentAttendance?.toJson());

          return AttendanceCard(
            attendance: studentAttendance,
            student: students[index],
            isAttended: isAttended,
            isToday: isToday,
            day: day,
          );
        },
        separatorBuilder: (context, index) => context.mediumGap,
        itemCount: students.length);
  }
}
