import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/filter_attendance_list.dart';
import 'widgets/top_section_date_filter.dart';

class AttendanceFilterBody extends HookConsumerWidget {
  final List<AttendanceModel> attendances;
  final List<StudentModel> students;

  const AttendanceFilterBody({
    super.key,
    required this.attendances,
    required this.students,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final attendanceCtrl = ref.watch(attendancesProviderController(context));

    final filteredListByDate = _getFilteredListByDate(attendanceCtrl);

    final today = filteredListByDate.value.length - 1;
    final selectedIndex = useState(today);
    final allAttendances =
        _getFilteredAttendances(filteredListByDate, selectedIndex);

    return Column(
      children: [
        context.mediumGap,

        //! Top Section Date Filter
        TopSectionDateFilter(
          filteredListByDate: filteredListByDate,
          selectedIndex: selectedIndex,
        ),

        //! Filtered Attendance List
        FilterAttendanceList(
          allAttendances: allAttendances,
          students: students,
          isToday: filteredListByDate.value[selectedIndex.value] ==
              DateTime.now().formatDateToString,
          day: filteredListByDate.value[selectedIndex.value],
        ),
      ],
    );
  }

  // * Get filtered list by date ==============================================
  ValueNotifier<List<String>> _getFilteredListByDate(
      AttendancesController attendanceCtrl) {
    final allAttendanceDates =
        attendanceCtrl.attendanceDates(attendances: attendances);
    final filteredListByDate = useState<List<String>>([]);

    useEffect(() {
      filteredListByDate.value = _filterAndSortDates(allAttendanceDates);
      return () {};
    }, []);

    return filteredListByDate;
  }

  // * Filter and sort the dates ===============================================
  List<String> _filterAndSortDates(List<String> allAttendanceDates) {
    final filteredDates = allAttendanceDates.toSet().toList();
    if (!filteredDates.contains(DateTime.now().formatDateToString)) {
      filteredDates.add(DateTime.now().formatDateToString);
    }

    filteredDates.sort((a, b) => a.compareTo(b));

    return filteredDates;
  }

  // * Get filtered attendances ===============================================
  List<AttendanceModel> _getFilteredAttendances(
      ValueNotifier<List<String>> filteredListByDate,
      ValueNotifier<int> selectedIndex) {
    return attendances
        .where((element) =>
            element.attendanceDate ==
            filteredListByDate.value[selectedIndex.value])
        .toList();
  }
}
