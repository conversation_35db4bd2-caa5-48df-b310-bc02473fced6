import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../controllers/attendance_controller.dart';
import 'attendance_card.dart';

class AttendanceList extends ConsumerWidget {
  final ClassModel classModel;

  const AttendanceList({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final attendancesCtrl = ref.watch(getAttendanceDataProvider(context));

    final students =
        classModel.students?.where((student) => student.id != 0).toList() ?? [];

    return attendancesCtrl.get(
      data: (attendances) => ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final student = students[index];

            final studentAttendance = attendances.firstWhereOrNull(
                (attendance) => attendance.student?.id == student.id);

            final isAttended = studentAttendance != null;

            return AttendanceCard(
              attendance: studentAttendance,
              student: student,
              isAttended: isAttended,
            );
          },
          separatorBuilder: (context, index) => context.mediumGap,
          itemCount: students.length),
    );
  }
}
