import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/exam/controllers/exams_controller.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/exam_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/student_result_card_widget.dart';

class StudentResultDetailsScreen extends ConsumerStatefulWidget {
  final List<ExamModel> exams;
  final StudentModel student;

  const StudentResultDetailsScreen({
    super.key,
    required this.exams,
    required this.student,
  });

  @override
  _StudentResultDetailsScreenState createState() =>
      _StudentResultDetailsScreenState();
}

class _StudentResultDetailsScreenState
    extends ConsumerState<StudentResultDetailsScreen> {
  late List<TextEditingController> controllers;
  late List<ValueNotifier<num>> ratings;
  late List<ValueNotifier<bool>> canAddNote;
  late List<String?> initialNotes;
  late List<num> initialRatings;

  @override
  void initState() {
    super.initState();
    controllers = List.generate(
      widget.exams.length,
      (index) => TextEditingController(
        text: widget.exams[index].studentsResult
            .firstWhereOrNull(
                (element) => element.student?.id == widget.student.id)
            ?.note,
      ),
    );

    ratings = List.generate(widget.exams.length, (index) {
      final studentRate = widget.exams[index].studentsResult
          .firstWhereOrNull(
              (element) => element.student?.id == widget.student.id)
          ?.rate;
      return ValueNotifier<num>(studentRate ?? 0.0);
    });

    canAddNote = List.generate(widget.exams.length, (index) {
      final note = widget.exams[index].studentsResult
          .firstWhereOrNull(
              (element) => element.student?.id == widget.student.id)
          ?.note;

      final canAdd = note != null && note.isNotEmpty;

      return ValueNotifier<bool>(canAdd);
    });

    initialNotes = controllers.map((controller) => controller.text).toList();
    initialRatings = ratings.map((rating) => rating.value).toList();
  }

  @override
  void dispose() {
    for (var controller in controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final examController =
        ref.watch(examsControllerChangeNotifierProvider(context));

    return Scaffold(
      bottomNavigationBar: const UserModel().isAdmin
          ? null
          : Button(
              isLoading: examController.isLoading,
              loadingWidget: const LoadingWidget(),
              label: context.tr.submit,
              onPressed: () async {
                final List<ExamModel> modifiedExams = [];

                for (var i = 0; i < widget.exams.length; i++) {
                  if (controllers[i].text != initialNotes[i] ||
                      ratings[i].value != initialRatings[i]) {
                    final copiedExam = widget.exams[i].copyWith(
                      studentsResult: [
                        ...widget.exams[i].studentsResult
                          ..removeWhere((element) =>
                              element.student?.id == widget.student.id),
                        StudentsResultModel(
                          student: widget.student,
                          rate: ratings[i].value,
                          note: controllers[i].text,
                        ),
                      ],
                    );
                    modifiedExams.add(copiedExam);
                  }
                }

                if (modifiedExams.isNotEmpty) {
                  await examController.updateStudentRate(exams: modifiedExams);
                }

                // await Future.forEach(widget.exams, (exam) async {
                //   final index = widget.exams.indexOf(exam);
                //   if (controllers[index].text != initialNotes[index] ||
                //       ratings[index].value != initialRatings[index]) {
                //     final copiedExam = exam.copyWith(
                //       studentsResult: [
                //         ...exam.studentsResult
                //           ..removeWhere((element) =>
                //               element.student?.id == widget.student.id),
                //         StudentsResultModel(
                //           student: widget.student,
                //           rate: ratings[index].value,
                //           note: controllers[index].text,
                //         ),
                //       ],
                //     );
                //
                //     await examController.updateStudentRate(
                //       exams: copiedExam,
                //     );
                //   }
                // });

                NotificationService.sendNotification(
                  title: "Exam Update",
                  body:
                      "Exam results for ${widget.student.name} have been updated",
                  userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(
                    widget.student.id,
                  ),
                  isTopic: true,
                );

                if (!context.mounted) return;

                context.back();
                context.toReplacement(const ExamScreen());
                context.showBarMessage(context.tr.editSuccessfully);
              },
            ).paddingAll(AppSpaces.mediumPadding),
      appBar: MainAppBar(
        title: context.isEng
            ? '${widget.student.name} ${context.tr.results}'
            : 'نتائج ${widget.student.name}',
        isBackButton: true,
        iconPath: '',
      ),
      body: BaseList(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        data: widget.exams,
        emptyWidget: Center(
          child: Text(
            context.tr.noQuestions,
            style: context.subHeadLine,
          ),
        ),
        separatorGap: context.mediumGap,
        itemBuilder: (exam, index) {
          final studentResult = widget.exams[index].studentsResult
              .firstWhereOrNull(
                  (element) => element.student?.id == widget.student.id);

          return StudentResultCardWidget(
            exam: exam,
            number: index + 1,
            controller: controllers[index],
            rating: ratings[index],
            studentResult: studentResult,
            canAddNote: canAddNote[index],
            setState: setState,
          );
        },
        mainAxisSpacing: 15.h,
        crossAxisSpacing: 10,
      ),
    );
  }
}

// class StudentResultDetailsScreen extends HookConsumerWidget {
//   final List<ExamModel> exams;
//   final StudentModel student;
//
//   const StudentResultDetailsScreen({
//     super.key,
//     required this.exams,
//     required this.student,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final examController =
//         ref.watch(examsControllerChangeNotifierProvider(context));
//
//     final controllers = List.generate(
//         exams.length,
//         (index) => useTextEditingController(
//               text: exams[index]
//                   .studentsResult
//                   .firstWhereOrNull(
//                       (element) => element.student?.id == student.id)
//                   ?.note,
//             ));
//
//     final ratings = List.generate(exams.length, (index) {
//       final studentRate = exams[index]
//           .studentsResult
//           .firstWhereOrNull((element) => element.student?.id == student.id)
//           ?.rate;
//       return useState<num>(studentRate ?? 0.0);
//     });
//
//     final canAddNote = List.generate(exams.length, (index) {
//       final note = exams[index]
//           .studentsResult
//           .firstWhereOrNull((element) => element.student?.id == student.id)
//           ?.note;
//
//       final canAdd = note != null && note.isNotEmpty;
//
//       return useState<bool>(canAdd);
//     });
//
//     return Scaffold(
//       bottomNavigationBar: const UserModel().isAdmin
//           ? null
//           : Button(
//               isLoading: examController.isLoading,
//               loadingWidget: const LoadingWidget(),
//               label: context.tr.submit,
//               onPressed: () async {
//                 for (var i = 0; i < exams.length; i++) {
//                   final copiedExam = exams[i].copyWith(
//                     studentsResult: [
//                       ...exams[i].studentsResult
//                         ..removeWhere(
//                             (element) => element.student?.id == student.id),
//                       StudentsResultModel(
//                         student: student,
//                         rate: ratings[i].value,
//                         note: controllers[i].text,
//                       ),
//                     ],
//                   );
//
//                   await examController.updateStudentRate(
//                     exam: copiedExam,
//                   );
//                 }
//               },
//             ).paddingAll(AppSpaces.mediumPadding),
//       appBar: MainAppBar(
//         title: context.isEng
//             ? '${student.name} ${context.tr.results}'
//             : 'نتائج ${student.name}',
//         isBackButton: true,
//         iconPath: '',
//       ),
//       body: StatefulBuilder(builder: (context, setState) {
//         return BaseList(
//           padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//           data: exams,
//           emptyWidget: Center(
//             child: Text(
//               context.tr.noQuestions,
//               style: context.subHeadLine,
//             ),
//           ),
//           separatorGap: context.mediumGap,
//           itemBuilder: (exam, index) {
//             final studentResult = exams[index].studentsResult.firstWhereOrNull(
//                 (element) => element.student?.id == student.id);
//
//             return StudentResultCardWidget(
//               exam: exam,
//               number: index + 1,
//               controller: controllers[index],
//               rating: ratings[index],
//               studentResult: studentResult,
//               canAddNote: canAddNote[index],
//             );
//           },
//           mainAxisSpacing: 15.h,
//           crossAxisSpacing: 10,
//         );
//       }),
//     );
//   }
// }
