import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/exam/controllers/exam_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/exam/controllers/exams_controller.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/base_date_filter_widget.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_selected_screen/exams_tab_screen/exams_tab_screen.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_selected_screen/exams_tab_screen/widgets/add_exams_dialog.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_selected_screen/results_tab_screen/results_tab_screen.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_tab_bar.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class ExamScreen extends HookConsumerWidget {
  const ExamScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examTabBarIndex = ref.watch(examTabBarControllerProvider);
    final selectedDate = useState<DateTime>(DateTime.now());
    final selectedClass = useState<ClassModel?>(null);

    final params = (context, selectedDate.value.formatDateToString);

    final getResultsStudentsFuture =
        ref.watch(getExamsDataByMonthProvider(params));

    final students = selectedClass.value?.students
            ?.where((studentsData) =>
                studentsData.isActive == true && studentsData.id != 0)
            .toList() ??
        <StudentModel>[];

    final getActiveStudentsFuture = ref.watch(getActiveStudents(context));

    final teacherStudents = getActiveStudentsFuture.when(
      data: (data) => data,
      loading: () => <StudentModel>[],
      error: (error, stack) => <StudentModel>[],
    );

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          bottomNavigationBar:
              examTabBarIndex == 1 ? const AddExamsDialog() : null,
          appBar: MainAppBar(
            title: context.tr.exams,
            isBackButton: true,
            iconPath: '',
          ),
          body: Column(
            children: [
              BaseDateFilterWidget(
                date: selectedDate.value,
                onNext: () {
                  selectedDate.value = DateTime(
                    selectedDate.value.year,
                    selectedDate.value.month + 1,
                  );
                },
                onPrevious: () {
                  selectedDate.value = DateTime(
                    selectedDate.value.year,
                    selectedDate.value.month - 1,
                  );
                },
              ),
              if (const UserModel().isAdmin)
                ClassDropDown(
                  selectedClass: selectedClass,
                  selectFirstClass: true,
                ).paddingAll(
                  AppSpaces.mediumPadding,
                )
              else
                const ExamTabBarWidget(),
              Expanded(
                child: getResultsStudentsFuture.get(
                  data: (questions) {
                    final isEmptyList = questions.isEmpty;

                    if (isEmptyList && !const UserModel().isAdmin) {
                      return Center(
                        child: Text(
                          context.tr.noQuestions,
                          style: textTheme(context).headlineMedium,
                        ),
                      );
                    }

                    return SelectedScreen(
                      questions: questions,
                      students: const UserModel().isAdmin
                          ? students
                          : teacherStudents,
                    ).paddingAll(
                      AppSpaces.mediumPadding,
                    );
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class SelectedScreen extends HookConsumerWidget {
  final List<ExamModel> questions;
  final List<StudentModel> students;

  const SelectedScreen({
    super.key,
    required this.questions,
    required this.students,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examTabBarIndex = ref.watch(examTabBarControllerProvider);

    switch (examTabBarIndex) {
      case 0:
        return ResultsTabScreen(
          questions: questions,
          students: students,
        );
      case 1:
        return ExamsTabScreen(
          examsData: questions,
        );
    }
    return const SizedBox.shrink();
    // switch (examTabBarIndex) {
    //   case 0:
    //     return ResultsTabScreen(
    //       selectedDate: selectedDate,
    //       questions: questionsNotifier.value,
    //     );
    //   case 1:
    //     return ExamsTabScreen(
    //       selectedDate: selectedDate,
    //       exams: questionsNotifier,
    //     );
    // }
    // return const SizedBox.shrink();
  }
}
