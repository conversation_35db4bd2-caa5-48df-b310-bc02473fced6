import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/exams_card_widget.dart';

class ExamsTabScreen extends HookConsumerWidget {
  final List<ExamModel> examsData;

  const ExamsTabScreen({
    super.key,
    required this.examsData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final exams = useState(examsData);

    return StatefulBuilder(builder: (context, setState) {
      return Column(
        children: [
          SearchBar(
            hintText: context.tr.searchQuestion,
            leading: const Icon(CupertinoIcons.search),
            onChanged: (value) {
              if (value.isEmpty) {
                exams.value = examsData;
              } else {
                final filteredExams = examsData
                    .where((exam) => exam.question
                        .toLowerCase()
                        .contains(value.toLowerCase()))
                    .toList();

                exams.value = filteredExams;
              }
            },
          ),
          context.largeGap,
          Expanded(
            child: BaseList(
              data: exams.value,
              separatorGap: context.mediumGap,
              itemBuilder: (exam, index) {
                return ExamsCardWidget(
                  exam: exam,
                  examNotifier: exams,
                  setState: setState,
                  number: index + 1,
                );
              },
              mainAxisSpacing: 15.h,
              crossAxisSpacing: 10,
            ),
          ),
        ],
      );
    });
  }
}
