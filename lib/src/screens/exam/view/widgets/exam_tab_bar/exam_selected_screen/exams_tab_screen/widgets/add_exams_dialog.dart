import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../controllers/exams_controller.dart';

class AddExamsDialog extends HookConsumerWidget {
  const AddExamsDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Button(
      label: context.tr.addNewQuestion,
      onPressed: () {
        showAddEditExamDialog(
          context,
        );
      },
    ).paddingAll(AppSpaces.mediumPadding);
  }
}

void showAddEditExamDialog(BuildContext context, {ExamModel? exam}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final date = DateTime.tryParse(exam?.date ?? '') ?? DateTime.now();

            final selectedDate = useState<DateTime>(date);

            final questionController =
                useTextEditingController(text: exam?.question);

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            //!-----------------------------------------------------

            final examController =
                ref.watch(examsControllerChangeNotifierProvider(context));

            Future<void> addAndEditExam() async {
              await examController.addExam(
                  questionController: questionController,
                  selectedDate: selectedDate);
            }

            return PopScope(
              onPopInvoked: (_) => examController.clearData(
                  questionController: questionController,
                  selectedDate: selectedDate,
                  ref: ref),
              child: AlertDialogWidget(
                isImage: false,
                header: context.tr.addNewQuestion,
                isLoading: examController.isLoading,
                child: Form(
                  key: formKey.value,
                  child: BaseTextField(
                    title: context.tr.question,
                    maxLines: 3,
                    controller: questionController,
                    textInputType: TextInputType.text,
                  ),
                ),
                onConfirm: () async {
                  if (!formKey.value.currentState!.validate()) return;
                  await addAndEditExam();
                },
              ),
            );
          },
        );
      });
}
