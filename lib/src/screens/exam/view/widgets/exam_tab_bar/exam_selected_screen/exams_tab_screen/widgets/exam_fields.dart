import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class ExamFields extends StatelessWidget {
  final Map<String, TextEditingController> fieldsControllers;
  final ValueNotifier<DateTime> selectedDate;

  const ExamFields(
      {super.key, required this.fieldsControllers, required this.selectedDate});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // BaseDatePicker(
        //     selectedDateNotifier: selectedDate, label: context.tr.date),

        // context.largeGap,

        //! Exam Name Field
        BaseTextField(
          title: context.tr.question,
          maxLines: 3,
          controller: fieldsControllers[ApiStrings.question],
          textInputType: TextInputType.text,
        ),
      ],
    );
  }
}
