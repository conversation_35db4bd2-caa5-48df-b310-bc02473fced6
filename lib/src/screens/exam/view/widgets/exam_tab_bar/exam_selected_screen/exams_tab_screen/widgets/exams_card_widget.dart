import 'package:connectify_app/src/screens/exam/controllers/exams_controller.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

class ExamsCardWidget extends ConsumerWidget {
  final ExamModel exam;
  final ValueNotifier<List<ExamModel>> examNotifier;
  final Function setState;
  final int number;

  const ExamsCardWidget(
      {super.key,
      required this.exam,
      required this.examNotifier,
      required this.number,
      required this.setState});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examCtrl = ref.watch(examsControllerChangeNotifierProvider(context));

    return BaseContainer(
        padding: AppSpaces.appbarPadding,
        child: Row(
          children: [
            //! Exam Number
            Text(
              '$number.',
              style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
            ),

            context.smallGap,

            //! Exam (Name - Date)
            Text(
              exam.question,
              style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
            ),

            const Spacer(),

            context.mediumGap,

            IconButton(
              onPressed: () {
                if (exam.studentsResult.isNotEmpty) {
                  context.showBarMessage(
                      context.tr
                          .youCannotDeleteThisQuestionBecauseitsHasStudentResults,
                      isError: true);
                } else {
                  QuickAlert.show(
                    context: context,
                    title: context.tr.warning,
                    text: context.tr.areYouSureToDeleteThisQuestion,
                    type: QuickAlertType.error,
                    confirmBtnText: context.tr.confirm,
                    cancelBtnText: context.tr.cancel,
                    showCancelBtn: true,
                    confirmBtnColor: Colors.red.shade600,
                    onConfirmBtnTap: () async {
                      Navigator.of(context).pop();

                      context.showBarMessage(
                        context.tr.deletedSuccessfully,
                        isError: true,
                      );

                      examCtrl.deleteExam(id: exam.id!);

                      examNotifier.value.removeWhere(
                        (element) => element.id == exam.id,
                      );

                      setState(() {});
                    },
                  );
                }
              },
              icon: const Icon(
                //delete
                CupertinoIcons.trash,
                color: ColorManager.errorColor,
              ),
            ),
            //! Exam Price
            // Text(
            //   ' \$${exam.amount}',
            //   style: context.priceTitle,
            // ).paddingOnly(right: AppSpaces.smallPadding)
          ],
        ).paddingOnly(right: AppSpaces.mediumPadding));
  }
}
