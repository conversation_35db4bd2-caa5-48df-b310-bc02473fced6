import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_selected_screen/results_tab_screen/widgets/result_card_widget.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ResultsTabScreen extends HookConsumerWidget {
  final List<ExamModel> questions;
  final List<StudentModel> students;

  const ResultsTabScreen({
    super.key,
    required this.questions,
    required this.students,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseList(
      data: students,
      separatorGap: context.mediumGap,
      showEmptyWidget: false,
      itemBuilder: (student, index) {
        return ResultCardWidget(
          questions: questions,
          student: student,
        );
      },
      mainAxisSpacing: 15.h,
      crossAxisSpacing: 10,
    );
  }
}
