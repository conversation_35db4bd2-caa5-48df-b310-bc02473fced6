import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/student_result_details_screen/student_result_details_screen.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ResultCardWidget extends ConsumerWidget {
  final List<ExamModel> questions;
  final StudentModel student;

  const ResultCardWidget({
    super.key,
    required this.questions,
    required this.student,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentAverageRate = questions.map((e) {
      final rate = e.studentsResult
          .firstWhereOrNull((element) => element.student?.id == student.id)
          ?.rate;

      return rate == null || rate == 0 ? 1.0 : rate;
    }).toList();

    final calculateRate = studentAverageRate.isNotEmpty
        ? studentAverageRate.fold(0.0, (prev, element) => prev + element) /
            studentAverageRate.length
        : 0.0;

    return BaseContainer(
        padding: AppSpaces.appbarPadding,
        onTap: () => context.to(StudentResultDetailsScreen(
              exams: questions,
              student: student,
            )),
        child: Row(
          children: [
            //! Result (Name - Date)
            ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: BaseCachedImage(student.image?.url ?? '',
                  height: 50.h,
                  width: 50.w,
                  fit: BoxFit.cover,
                  errorWidget: const BaseCachedImage(
                    AppConsts.studentPlaceholder,
                  )),
            ),

            context.smallGap,

            //! Student Name
            Expanded(
              child: Text(
                student.name,
                style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
              ),
            ),

            context.mediumGap,

            //! Rating
            RatingBar.builder(
              initialRating: calculateRate.toDouble(),
              minRating: 1,
              direction: Axis.horizontal,
              allowHalfRating: true,
              ignoreGestures: true,
              itemCount: 5,
              itemSize: 17,
              itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
              itemBuilder: (context, _) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              onRatingUpdate: (rating) {},
            ),

            context.mediumGap,

            //arrow right
            const Icon(
              Icons.arrow_forward_ios,
              size: 17,
            ),
          ],
        ).paddingOnly(
          right: AppSpaces.smallPadding,
        ));
  }
}
