import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

// *  Exam Repository Provider =====================================
final examRepoProvider = Provider<ExamsRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ExamsRepo(networkApiService);
});

//? =================================================================
class ExamsRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ExamsRepo(this._networkApiServices);

  //? Get Exams --------------------------------------
  Future<List<ExamModel>> getExams() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.exams);

      final examsData = compute(responseToExamModelList, response);
      return examsData;
    });
  }

//? Get Exams By Month --------------------------------------
  Future<List<ExamModel>> getExamsByMonth({
    required String date,
  }) async {
    return await baseFunction(() async {
      final monthWithYear =
          DateFormat('yyyy-MM', 'en').format(DateTime.parse(date));

      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.exams}&filters[date][\$contains]=$monthWithYear',
      );

      final examsData = compute(responseToExamModelList, response);
      return examsData;
    });
  }

//? Add Exam --------------------------------------
  Future<dynamic> addExam({required ExamModel examModel}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(ApiEndpoints.exams,
          body: examModel.toJson());
    });
  }

//? Edit Exam --------------------------------------
  Future<void> editExam({required List<ExamModel> exams}) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(ApiEndpoints.updateExams,
          data: {
            'exams': exams.map((e) => e.toJson()).toList(),
          },
          isNormalMethod: true);
    });
  }

//? Edit Exam --------------------------------------
//   Future<void> editExam({required ExamModel examModel}) async {
//     return await baseFunction(() async {
//       await _networkApiServices.putResponse(
//           '${ApiEndpoints.deleteExams}/${examModel.id}',
//           data: examModel.toJson());
//     });
//   }

  //? Delete Exam -----------------------------------
  Future<void> deleteExam({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.deleteExams}/$id');
    });
  }
}
