import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final financialTabBarController = Provider<FinancialTabBarController>(
  (ref) {
    return FinancialTabBarController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final financialTabBarControllerProvider =
    StateNotifierProvider<FinancialTabBarController, int>(
  (ref) => ref.watch(financialTabBarController),
);

class FinancialTabBarController extends StateNotifier<int> {
  FinancialTabBarController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
