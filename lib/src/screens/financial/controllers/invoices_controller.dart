import 'package:connectify_app/src/screens/financial/repos/invoices_repo.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/invoices_model.dart';
import '../view/financial_screen.dart';

// *  Invoice Controller Provider ============================================
final invoicesControllerProvider =
    Provider.family<InvoicesController, BuildContext>((ref, context) {
  final invoicesRepo = ref.watch(invoicesRepoProvider);

  return InvoicesController(invoicesRepo: invoicesRepo, context);
});

// *  Invoice Controller Change Notifier Provider ============================================
final invoicesControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<InvoicesController, BuildContext>(
        (ref, context) {
  final invoicesRepo = ref.watch(invoicesRepoProvider);

  return InvoicesController(invoicesRepo: invoicesRepo, context);
});

// *  Get Invoice Data ============================================

final getInvoicesDataProvider =
    FutureProvider.family<List<InvoicesModel>, BuildContext>(
        (ref, context) async {
  final invoicesCtrl = ref.watch(invoicesControllerProvider(context));

  return await invoicesCtrl.getInvoices();
});

// * Get Invoices Data By Date ============================================
final getInvoicesDataByMonthProvider = FutureProvider.family<
    List<InvoicesModel>, (BuildContext, String monthName)>((ref, params) async {
  final invoicesCtrl = ref.watch(invoicesControllerProvider(params.$1));

  return await invoicesCtrl.getInvoicesByMonth(
    monthName: params.$2,
  );
});

// *  Get Invoices Data By Date From & To ============================================
final getInvoicesDataByDateFromToProvider = FutureProvider.family<
    List<InvoicesModel>,
    (BuildContext, String fromDate, String toDate)>((ref, params) async {
  final invoicesCtrl = ref.watch(invoicesControllerProvider(params.$1));

  return await invoicesCtrl.getInvoicesByDateFromTo(
    fromDate: params.$2,
    toDate: params.$3,
  );
});

//? =======================================================================
class InvoicesController extends BaseVM {
  final InvoicesRepo invoicesRepo;
  final BuildContext context;

  InvoicesController(this.context, {required this.invoicesRepo});

  //? Get Invoices ----------------------------
  Future<List<InvoicesModel>> getInvoices() async {
    return await baseFunction(context, () async {
      final invoices = await invoicesRepo.getInvoices();

      invoices.sort(
        (a, b) => a.createdAt!.compareTo(b.createdAt!),
      );

      return invoices;
    });
  }

//? Get Invoices Data By Month ============================================
  Future<List<InvoicesModel>> getInvoicesByMonth(
      {required String monthName}) async {
    return await baseFunction(context, () async {
      final invoices =
          await invoicesRepo.getInvoicesDataByMonth(monthName: monthName);

      invoices.sort(
        (a, b) => a.createdAt!.compareTo(b.createdAt!),
      );

      return invoices;
    });
  }

// ? Get Invoices Data By Date From & To ============================================
  Future<List<InvoicesModel>> getInvoicesByDateFromTo(
      {required String fromDate, required String toDate}) async {
    return await baseFunction(context, () async {
      final invoices = await invoicesRepo.getInvoicesDataByDateFromTo(
        fromDate: fromDate,
        toDate: toDate,
      );

      invoices.sort(
        (a, b) => a.createdAt!.compareTo(b.createdAt!),
      );

      return invoices;
    });
  }

//? Add Invoice --------------------------------------
  Future<void> addInvoice({
    required Map<String, TextEditingController> controllers,
    required ValueNotifier<DateTime> selectedDate,
  }) async {
    return await baseFunction(
      context,
      () async {
        final invoicesModel = InvoicesModel(
          date: selectedDate.value.formatDateToString,
          name: controllers[ApiStrings.name]!.text,
          amount: num.parse(controllers[ApiStrings.amount]!.text),
        );

        await invoicesRepo.addInvoice(invoicesModel: invoicesModel);

        if (!context.mounted) return;
        context.back();
        context.toReplacement(const FinancialScreen());
        context.showBarMessage(context.tr.addedSuccessfully);
      },
      additionalFunction: (context) => getInvoices(),
    );
  }

  //? Edit Invoice --------------------------------------
  Future<void> editInvoice(
      {required Map<String, TextEditingController> controllers,
      required ValueNotifier<DateTime> selectedDate,
      required int id}) async {
    return await baseFunction(
      context,
      () async {
        final invoicesModel = InvoicesModel(
          id: id,
          name: controllers[ApiStrings.name]!.text,
          date: selectedDate.value.formatDateToString,
          amount: num.parse(controllers[ApiStrings.amount]!.text),
        );
        await invoicesRepo.editInvoice(invoicesModel: invoicesModel);
        if (!context.mounted) return;
        context.back();
        context.toReplacement(const FinancialScreen());
        context.showBarMessage(context.tr.editSuccessfully);
      },
      additionalFunction: (context) => getInvoices(),
    );
  }

  //? Delete Invoice -----------------------------------
  Future<void> deleteInvoice({required int id}) async {
    return await baseFunction(
      context,
      () async {
        await invoicesRepo.deleteInvoice(id: id);
        if (!context.mounted) return;
        context.back();

        context.toReplacement(const FinancialScreen());

        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      },
      additionalFunction: (context) => getInvoices(),
    );
  }

  void clearData(
      {required Map<String, TextEditingController> controllers,
      required ValueNotifier<DateTime> selectedDate,
      required WidgetRef ref}) {
    selectedDate.value = DateTime.now();
    controllers[ApiStrings.name]!.clear();
    controllers[ApiStrings.amount]!.clear();
  }
}
