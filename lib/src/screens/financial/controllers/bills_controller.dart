import 'package:connectify_app/src/screens/financial/models/bill_model.dart';
import 'package:connectify_app/src/screens/financial/repos/bills_repo.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../view/financial_screen.dart';

// *  Bills Controller Provider ============================================
final billsControllerProvider =
    Provider.family<BillsController, BuildContext>((ref, context) {
  final billsRepo = ref.watch(billRepoProvider);

  return BillsController(billsRepo: billsRepo, context);
});

// *  Bills Controller Change Notifier Provider ============================================
final billsControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<BillsController, BuildContext>(
        (ref, context) {
  final billsRepo = ref.watch(billRepoProvider);

  return BillsController(billsRepo: billsRepo, context);
});

// *  Get Bills Data ============================================
final getBillsDataProvider =
    FutureProvider.family<List<BillModel>, BuildContext>((ref, context) async {
  final billsCtrl = ref.watch(billsControllerProvider(context));

  return await billsCtrl.getBills();
});

// *  Get Bills Data By Month ============================================
final getBillsDataByMonthProvider =
    FutureProvider.family<List<BillModel>, (BuildContext, String monthName)>(
        (ref, params) async {
  final billsCtrl = ref.watch(billsControllerProvider(params.$1));

  return await billsCtrl.getBillsByMonth(
    monthName: params.$2,
  );
});

// *  Get Bills Data By Date From & To ============================================
final getBillsDataByDateFromToProvider = FutureProvider.family<List<BillModel>,
    (BuildContext, String fromDate, String toDate)>((ref, params) async {
  final billsCtrl = ref.watch(billsControllerProvider(params.$1));

  return await billsCtrl.getBillsByDateFromTo(
    fromDate: params.$2,
    toDate: params.$3,
  );
});

//? =======================================================================
class BillsController extends BaseVM {
  final BillsRepo billsRepo;
  final BuildContext context;

  BillsController(this.context, {required this.billsRepo});

  //? Get Bills ----------------------------
  Future<List<BillModel>> getBills() async {
    return await baseFunction(context, () async {
      final bills = await billsRepo.getBills();

      return bills;
    });
  }

  //? Get Bills By Month ----------------------------
  Future<List<BillModel>> getBillsByMonth({required String monthName}) async {
    return await baseFunction(context, () async {
      final bills = await billsRepo.getBillsByMonth(monthName: monthName);

      return bills;
    });
  }

  Future<List<BillModel>> getBillsByDateFromTo(
      {required String fromDate, required String toDate}) async {
    return await baseFunction(context, () async {
      final bills = await billsRepo.getBillsByDateFromTo(
        fromDate: fromDate,
        toDate: toDate,
      );

      return bills;
    });
  }
  // List<DateTime> getDaysInBetween(DateTime startDate, DateTime endDate) {
  //   List<DateTime> days = [];
  //   for (int i = 0; i <= endDate.difference(startDate).inDays; i++) {
  //     days.add(startDate.add(Duration(days: i)));
  //   }
  //   return days;
  // }

//? Add Bill --------------------------------------
  Future<void> addBill({
    required Map<String, TextEditingController> controllers,
    required ValueNotifier<DateTime> selectedDate,
  }) async {
    return await baseFunction(context, () async {
      final billModel = BillModel(
        date: selectedDate.value.formatDateToString,
        name: controllers[ApiStrings.name]!.text,
        amount: num.parse(controllers[ApiStrings.amount]!.text),
      );

      await billsRepo.addBill(billModel: billModel);

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const FinancialScreen());
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //? Edit Bill --------------------------------------
  Future<void> editBill(
      {required Map<String, TextEditingController> controllers,
      required ValueNotifier<DateTime> selectedDate,
      required int id}) async {
    return await baseFunction(
      context,
      () async {
        final bill = BillModel(
          id: id,
          name: controllers[ApiStrings.name]!.text,
          date: selectedDate.value.formatDateToString,
          amount: num.parse(controllers[ApiStrings.amount]!.text),
        );
        await billsRepo.editBill(billModel: bill);
        if (!context.mounted) return;
        context.back();
        context.toReplacement(const FinancialScreen());
        context.showBarMessage(context.tr.editSuccessfully);
      },
      additionalFunction: (context) => getBills(),
    );
  } //? Delete Bill -----------------------------------

  Future<void> deleteBill({required int id}) async {
    return await baseFunction(
      context,
      () async {
        await billsRepo.deleteBill(id: id);
        if (!context.mounted) return;
        context.back();
        context.toReplacement(const FinancialScreen());
        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      },
      additionalFunction: (context) => getBills(),
    );
  }

  void clearData(
      {required Map<String, TextEditingController> controllers,
      required ValueNotifier<DateTime> selectedDate,
      required WidgetRef ref}) {
    selectedDate.value = DateTime.now();
    controllers[ApiStrings.name]!.clear();
    controllers[ApiStrings.amount]!.clear();
  }
}
