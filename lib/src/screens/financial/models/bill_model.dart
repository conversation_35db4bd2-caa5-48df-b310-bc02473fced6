
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../../shared/data/remote/api_strings.dart';

List<BillModel> responseToBillModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final bills = data.map((e) => BillModel.fromJson(e)).toList();

  return bills;
}

class BillModel extends BaseModel {
  final num amount;
  final String date;

  const BillModel(
      {super.id,
      super.name,
      super.description,
      super.image,
      super.createdAt,
      this.amount = 0,
      this.date = ''});

  //? From Json
  factory BillModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return BillModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name] ?? '',
      date: attributes[ApiStrings.date] ?? '',
      amount:
          num.tryParse(attributes[ApiStrings.amount]?.toString() ?? '0') ?? 0,
      createdAt: attributes[ApiStrings.createdAt] ?? '',
    );
  }

  //? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.date: date,
      ApiStrings.amount: amount,
    };
  }
}
