import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../../shared/data/remote/api_strings.dart';

List<InvoicesModel> responseToInvoicesModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final invoices = data.map((e) => InvoicesModel.fromJson(e)).toList();

  return invoices;
}

class InvoicesModel extends BaseModel {
  final num amount;
  final String date;

  const InvoicesModel(
      {super.id,
      super.name,
      super.description,
      super.image,
      super.createdAt,
      this.amount = 0,
      this.date = ''});

  //? From Json
  factory InvoicesModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return InvoicesModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name] ?? '',
      date: attributes[ApiStrings.date] ?? '',
      amount:
          num.tryParse(attributes[ApiStrings.amount]?.toString() ?? '0') ?? 0,
      createdAt: attributes[ApiStrings.createdAt] ?? '',
    );
  }

  //? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.date: date,
      ApiStrings.amount: amount,
    };
  }
}
