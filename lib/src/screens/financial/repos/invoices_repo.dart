import 'package:connectify_app/src/screens/financial/models/invoices_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

// *  Invoice Repository Provider =====================================
final invoicesRepoProvider = Provider<InvoicesRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return InvoicesRepo(networkApiService);
});

//? =================================================================
class InvoicesRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  InvoicesRepo(this._networkApiServices);

  //? Get Invoices --------------------------------------
  Future<List<InvoicesModel>> getInvoices() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.invoices);

      final invoicesData = compute(responseToInvoicesModelList, response);
      return invoicesData;
    });
  }

//? Get Invoices Data By Date ============================================
  Future<List<InvoicesModel>> getInvoicesDataByMonth({
    required String monthName,
  }) async {
    final month =
        DateFormat.MMMM().parse(monthName).month.toString().padLeft(2, '0');

    final response = await _networkApiServices.getResponse(
      '${ApiEndpoints.invoices}&filters[date][\$contains]=-$month-',
    );
    return compute(responseToInvoicesModelList, response);
  }

// ? Get Invoices Data By Date From & To ============================================
  Future<List<InvoicesModel>> getInvoicesDataByDateFromTo({
    required String fromDate,
    required String toDate,
  }) async {
    final response = await _networkApiServices.getResponse(
      '${ApiEndpoints.invoices}&filters[date][\$gte]=$fromDate&filters[date][\$lte]=$toDate',
    );
    return compute(responseToInvoicesModelList, response);
  }

  //? Add Invoices --------------------------------------
  Future<dynamic> addInvoice({required InvoicesModel invoicesModel}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(ApiEndpoints.invoices,
          body: invoicesModel.toJson());
    });
  }

  //? Edit Invoices --------------------------------------
  Future<void> editInvoice({required InvoicesModel invoicesModel}) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(
          '${ApiEndpoints.deleteInvoices}/${invoicesModel.id}',
          data: invoicesModel.toJson());
    });
  }

  //? Delete Invoices -----------------------------------
  Future<void> deleteInvoice({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.deleteInvoices}/$id');
    });
  }
}
