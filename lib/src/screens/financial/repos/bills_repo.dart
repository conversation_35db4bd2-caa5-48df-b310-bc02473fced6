import 'package:connectify_app/src/screens/financial/models/bill_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

// *  Bill Repository Provider =====================================
final billRepoProvider = Provider<BillsRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return BillsRepo(networkApiService);
});

//? =================================================================
class BillsRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  BillsRepo(this._networkApiServices);

  //? Get Bills --------------------------------------
  Future<List<BillModel>> getBills() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.bills);

      final billsData = compute(responseToBillModelList, response);
      return billsData;
    });
  }

//? Get Bills By Month --------------------------------------
  Future<List<BillModel>> getBillsByMonth({
    required String monthName,
  }) async {
    return await baseFunction(() async {
      final month =
          DateFormat.MMMM().parse(monthName).month.toString().padLeft(2, '0');

      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.bills}&filters[date][\$contains]=-$month-',
      );

      final billsData = compute(responseToBillModelList, response);
      return billsData;
    });
  }

// *  Get Bills Data By Date From & To ============================================
  Future<List<BillModel>> getBillsByDateFromTo({
    required String fromDate,
    required String toDate,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.bills}&filters[date][\$gte]=$fromDate&filters[date][\$lte]=$toDate',
      );

      final billsData = compute(responseToBillModelList, response);
      return billsData;
    });
  }

//? Add Bill --------------------------------------
  Future<dynamic> addBill({required BillModel billModel}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(ApiEndpoints.bills,
          body: billModel.toJson());
    });
  }

//? Edit Bill --------------------------------------
  Future<void> editBill({required BillModel billModel}) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(
          '${ApiEndpoints.deleteBills}/${billModel.id}',
          data: billModel.toJson());
    });
  }

  //? Delete Bill -----------------------------------
  Future<void> deleteBill({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.deleteBills}/$id');
    });
  }
}
