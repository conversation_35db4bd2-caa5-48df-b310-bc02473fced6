import 'package:connectify_app/src/screens/financial/controllers/financial_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/financial/view/payment_methods_screen.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/invoices_tab_screen/invoices_tab_screen.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_tab_bar.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'widgets/financial_tab_bar/financial_selected_screen/bills_tab_screen/bills_tab_screen.dart';
import 'widgets/financial_tab_bar/financial_selected_screen/subscriptions_tab_screen/subscriptions_tab_screen.dart';

class FinancialScreen extends ConsumerWidget {
  const FinancialScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final financialChangeNotifierCtrl =
        ref.watch(financialTabBarControllerProvider);

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
          floatingActionButton: financialChangeNotifierCtrl == 2
              ? FloatingActionButton.extended(
                  backgroundColor: ColorManager.primaryColor,
                  label: Text(context.tr.paymentMethods),
                  icon: const Icon(Icons.payments_outlined),
                  onPressed: () => context.to(const PaymentMethodsScreen()),
                )
              : null,
          appBar: MainAppBar(
            title: context.tr.financial,
            isBackButton: true,
          ),
          body: Column(
            children: [
              const FinancialTabBarWidget(),
              Expanded(
                child: const SelectedScreen().paddingAll(
                  AppSpaces.mediumPadding,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class SelectedScreen extends ConsumerWidget {
  const SelectedScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final financialTabBarIndex = ref.watch(financialTabBarControllerProvider);
    switch (financialTabBarIndex) {
      case 0:
        return const BillsTabScreen();
      case 1:
        return const InvoicesTabScreen();
      case 2:
        return const SubscriptionsTabScreen();
    }
    return const SizedBox.shrink();
  }
}
