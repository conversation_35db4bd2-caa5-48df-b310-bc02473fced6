import 'package:connectify_app/src/screens/financial/controllers/financial_tab_bar_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class FinancialTabBarWidget extends ConsumerWidget {
  const FinancialTabBarWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final financialTabBarCtrl = ref.watch(financialTabBarController);
    final financialChangeNotifierCtrl =
        ref.watch(financialTabBarControllerProvider);

    final List<String> tabs = [
      context.tr.bills,
      context.tr.invoices,
      context.tr.subscriptions,
    ];

    return Container(
      margin: const EdgeInsets.all(AppSpaces.smallPadding),
      padding: const EdgeInsets.only(
        left: AppSpaces.smallPadding,
        right: AppSpaces.smallPadding,
        bottom: AppSpaces.smallPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: DefaultTabController(
        initialIndex: financialChangeNotifierCtrl,
        length: tabs.length,
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          tabAlignment: TabAlignment.center,
          onTap: (index) {
            financialTabBarCtrl.changeIndex(index);
          },
          indicatorColor: Colors.transparent,
          indicator: const BoxDecoration(
            color: ColorManager.primaryColor,
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          dividerColor: ColorManager.grey,
          dividerHeight: 0,
          unselectedLabelColor: ColorManager.darkGrey.withOpacity(0.5),
          isScrollable: false,
          tabs: tabs
              .map((e) => ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.width * 0.03,
                        vertical: 9.h,
                      ),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: financialChangeNotifierCtrl == tabs.indexOf(e)
                              ? context.whiteLabelLarge
                              : context.hint,
                        ),
                      ),
                    ),
                  ))
              .toList(),
        ),
      ).paddingOnly(
        top: AppSpaces.mediumPadding,
      ),
    );
  }
}
