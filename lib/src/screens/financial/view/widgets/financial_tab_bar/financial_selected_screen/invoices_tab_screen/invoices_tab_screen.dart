import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/add_invoices_dialog.dart';
import 'widgets/invoices_list.dart';

class InvoicesTabScreen extends HookConsumerWidget {
  const InvoicesTabScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fromDate =
        useState(DateTime.now().subtract(const Duration(days: 30)));
    final toDate = useState(DateTime.now());

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        //! Date Filter Fields
        Row(
          children: [
            //! Form Date Filter
            Expanded(
              child: BaseDatePicker(
                selectedDateNotifier: fromDate,
                label: context.tr.from,
              ),
            ),

            context.mediumGap,

            //! To Date Filter
            Expanded(
              child: BaseDatePicker(
                  selectedDateNotifier: toDate, label: context.tr.to),
            ),
          ],
        ),

        context.largeGap,

        //! Add Bill Dialog
        const AddInvoicesDialog(),

        context.largeGap,

        //! Invoices List
        Expanded(
          child: InvoicesList(
            fromDate: fromDate.value,
            toDate: toDate.value,
          ),
        )
      ],
    );
  }
}
