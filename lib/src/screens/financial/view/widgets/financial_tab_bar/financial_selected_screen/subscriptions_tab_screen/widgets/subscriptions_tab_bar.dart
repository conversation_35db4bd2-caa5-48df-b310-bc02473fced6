import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class SubscriptionsTabBarWidget extends HookWidget {
  final ValueNotifier<bool> isPaid;

  const SubscriptionsTabBarWidget({
    super.key,
    required this.isPaid,
  });

  @override
  Widget build(BuildContext context) {
    final selectedTab = useState<int>(0);

    return DefaultTabController(
      initialIndex: selectedTab.value,
      length: 2,
      child: Container(
        height: 58,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: TabBar(
          dividerHeight: 0,
          onTap: (index) {
            isPaid.value = index == 0;
            selectedTab.value = index;
          },
          // tabAlignment: TabAlignment.start,
          // isScrollable: true,
          padding: const EdgeInsets.all(AppSpaces.smallPadding),
          indicator: BoxDecoration(
            color:
                isPaid.value ? ColorManager.buttonColor : Colors.orangeAccent,
            borderRadius: BorderRadius.circular(10.0),
          ),
          labelStyle: context.hint.copyWith(
            fontSize: 16,
          ),
          labelColor: Colors.white,
          indicatorSize: TabBarIndicatorSize.tab,
          unselectedLabelColor: Colors.black,
          tabs: [
            Tab(text: context.tr.paid),
            Tab(text: context.tr.unpaid),
          ],
        ),
      ),
    ).paddingSymmetric(horizontal: 12);
  }
}
