import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SubscriptionTopSectionWidget extends ConsumerWidget {
  final bool isPaid;
  final List<StudentModel> paidStudentsList;
  final List<StudentModel> unpaidStudentsList;

  const SubscriptionTopSectionWidget({
    super.key,
    required this.isPaid,
    required this.paidStudentsList,
    required this.unpaidStudentsList,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final studentsSubscriptionData =
    //     ref.watch(getSubscriptionsDataProvider(context));

    // final subscriptionData = studentsSubscriptionData.when(
    //   data: (data) => data,
    //   error: (error, stackTrace) => (0, 0, 0),
    //   loading: () => (0, 0, 0),
    // );

    // final paidStudents = subscriptionData.$1;
    // final unpaidStudents = subscriptionData.$2;
    // final totalPaidAmounts = subscriptionData.$3;

    final paidAmounts = paidStudentsList
        .map((student) =>
            student.fees ?? NurseryModelHelper.currentNursery()?.fees ?? 0)
        .fold(0.0, (value, element) => value + element);

    final unPaidAmounts = unpaidStudentsList
        .map((student) =>
            student.fees ?? NurseryModelHelper.currentNursery()?.fees ?? 0)
        .fold(0.0, (value, element) => value + element);

    final total = isPaid ? paidAmounts : unPaidAmounts;

    return Column(
      children: [
        Row(
          children: [
            Text(
              context.tr.total,
              style: context.hint.copyWith(
                fontSize: 18,
              ),
            ),
            const Spacer(),
            Text(
              '\$$total',
              style: context.hint.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        context.largeGap,
        Row(
          children: [
            Text(
              context.tr.numberOfStudents,
              style: context.hint.copyWith(
                fontSize: 18,
              ),
            ),
            const Spacer(),
            Text(
              isPaid
                  ? paidStudentsList.length.toString()
                  : unpaidStudentsList.length.toString(),
              style: context.hint.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    ).paddingSymmetric(
      horizontal: AppSpaces.mediumPadding,
    );
  }
}
