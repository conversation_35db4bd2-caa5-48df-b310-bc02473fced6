import 'package:connectify_app/src/screens/financial/controllers/financial_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/financial/controllers/invoices_controller.dart';
import 'package:connectify_app/src/screens/financial/models/invoices_model.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/invoices_tab_screen/widgets/add_invoices_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../../shared/widgets/base_popupmenu/base_popupmenu.dart';
import '../../../../../../../../shared/widgets/dialogs/base_delete_dialog.dart';

class InvoicesList extends ConsumerWidget {
  final DateTime fromDate;
  final DateTime toDate;

  const InvoicesList({super.key, required this.fromDate, required this.toDate});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params =
        (context, fromDate.formatDateToString, toDate.formatDateToString);

    final invoicesCtrl = ref.watch(getInvoicesDataByDateFromToProvider(params));

    return invoicesCtrl.get(
      data: (invoices) {
        if (invoices.isEmpty) {
          return Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(
              child: Text(
                context.tr.noInvoices,
                style: textTheme(context).headlineMedium,
              ),
            ),
          );
        }

        final total = invoices.fold(
          0,
          (previousValue, element) => previousValue + element.amount.toInt(),
        );

        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.tr.total,
                    style: context.title,
                  ),
                  Text(
                    ' \$$total',
                    style: context.priceTitle,
                  )
                ],
              ),
            ),
            context.mediumGap,
            Expanded(
              child: ListView.separated(
                  shrinkWrap: true,
                  itemBuilder: (context, index) => InvoicesCardWidget(
                        invoices: invoices[index],
                      ),
                  separatorBuilder: (context, index) => context.mediumGap,
                  itemCount: invoices.length),
            ),
          ],
        );
      },
    );
  }
}

class InvoicesCardWidget extends ConsumerWidget {
  final InvoicesModel invoices;

  const InvoicesCardWidget({super.key, required this.invoices});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final invoicesCtrl =
        ref.watch(invoicesControllerChangeNotifierProvider(context));
    return Stack(
      alignment: Alignment.topRight,
      children: [
        BaseContainer(
            padding: AppSpaces.appbarPadding,
            child: Row(
              children: [
                //! Bill (Name - Date)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //! Bill Name
                    Text(
                      invoices.name,
                      style: context.blueHint
                          .copyWith(fontWeight: FontWeight.bold),
                    ),

                    context.smallGap,
                    //! Bill Date
                    Text(
                      invoices.date.toString(),
                      style: context.hint.copyWith(fontSize: 12),
                    ),
                  ],
                ),
                const Spacer(),
                //! Bill Price
                Text(
                  ' \$${invoices.amount}',
                  style: context.priceTitle,
                ).paddingOnly(right: AppSpaces.smallPadding)
              ],
            ).paddingOnly(right: AppSpaces.mediumPadding)),
        BasePopupmenu(
          editOnTap: () => showAddEditInvoiceDialog(context, invoice: invoices),
          deleteOnTap: () => showDialog(
            context: context,
            builder: (context) => BaseDeleteDialog(
                description: context.tr.areYouSureToDeleteThisInvoice,
                onConfirm: () async {
                  await invoicesCtrl.deleteInvoice(id: invoices.id!).then(
                      (value) =>
                          ref.watch(financialTabBarController).changeIndex(1));
                }),
          ),
        ),
      ],
    );
  }
}
