import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class BillFields extends StatelessWidget {
  final Map<String, TextEditingController> fieldsControllers;
  final ValueNotifier<DateTime> selectedDate;

  const BillFields(
      {super.key, required this.fieldsControllers, required this.selectedDate});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        BaseDatePicker(
            selectedDateNotifier: selectedDate, label: context.tr.date),

        context.largeGap,

        //! Bill Name Field
        BaseTextField(
          title: context.tr.billName,
          controller: fieldsControllers[ApiStrings.name],
          textInputType: TextInputType.name,
        ),

        context.largeGap,

        //! Bill Amount Field
        BaseTextField(
          title: context.tr.billAmount,
          controller: fieldsControllers[ApiStrings.amount],
          textInputType: TextInputType.number,
        ),
      ],
    );
  }
}
