import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class InvoiceFields extends StatelessWidget {
  final Map<String, TextEditingController> fieldsControllers;
  final ValueNotifier<DateTime> selectedDate;

  const InvoiceFields(
      {super.key, required this.fieldsControllers, required this.selectedDate});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // //! Date Field
        BaseDatePicker(
            selectedDateNotifier: selectedDate, label: context.tr.date),

        context.largeGap,

        //! Invoice Name Field
        BaseTextField(
          title: context.tr.invoiceName,
          controller: fieldsControllers[ApiStrings.name],
          textInputType: TextInputType.name,
        ),

        context.largeGap,

        //! Invoice Amount Field
        BaseTextField(
          title: context.tr.invoiceAmount,
          controller: fieldsControllers[ApiStrings.amount],
          textInputType: TextInputType.number,
        ),
      ],
    );
  }
}
