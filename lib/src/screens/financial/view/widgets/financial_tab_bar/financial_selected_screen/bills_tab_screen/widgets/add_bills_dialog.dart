import 'package:connectify_app/src/screens/financial/models/bill_model.dart';
import 'package:connectify_app/src/screens/financial/view/widgets/financial_tab_bar/financial_selected_screen/bills_tab_screen/widgets/bill_fields.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../../../../../shared/data/remote/api_strings.dart';
import '../../../../../../controllers/bills_controller.dart';

class AddBillsDialog extends HookConsumerWidget {
  const AddBillsDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AddRectangleWidget(
        title: context.tr.addNewBill,
        onTap: () {
          showAddEditBillDialog(
            context,
          );
        });
  }
}

void showAddEditBillDialog(BuildContext context, {BillModel? bill}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final date = DateTime.tryParse(bill?.date ?? '') ?? DateTime.now();

            final selectedDate = useState<DateTime>(date);

            final controllers = {
              ApiStrings.name: useTextEditingController(text: bill?.name),
              ApiStrings.amount:
                  useTextEditingController(text: bill?.amount.toString()),
            };

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            //!-----------------------------------------------------

            final billController =
                ref.watch(billsControllerChangeNotifierProvider(context));

            Future<void> addAndEditBill() async {
              if (bill == null) {
                await billController.addBill(
                    controllers: controllers, selectedDate: selectedDate);
              } else {
                await billController.editBill(
                    id: bill.id!,
                    controllers: controllers,
                    selectedDate: selectedDate);
              }
            }

            return PopScope(
              onPopInvoked: (_) => billController.clearData(
                  controllers: controllers,
                  selectedDate: selectedDate,
                  ref: ref),
              child: AlertDialogWidget(
                isImage: false,
                header: context.tr.addNewBill,
                isLoading: billController.isLoading,
                child: Form(
                  key: formKey.value,
                  child: BillFields(
                    fieldsControllers: controllers,
                    selectedDate: selectedDate,
                  ),
                ),
                onConfirm: () async {
                  if (!formKey.value.currentState!.validate()) return;
                  await addAndEditBill();
                },
              ),
            );
          },
        );
      });
}
