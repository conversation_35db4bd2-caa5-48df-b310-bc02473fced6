import 'package:connectify_app/src/screens/financial/models/invoices_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../../shared/data/remote/api_strings.dart';
import '../../../../../../controllers/financial_tab_bar_controller.dart';
import '../../../../../../controllers/invoices_controller.dart';
import '../../../../../financial_screen.dart';
import 'invoices_fields.dart';

class AddInvoicesDialog extends StatelessWidget {
  const AddInvoicesDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AddRectangleWidget(
      title: context.tr.addNewInvoice,
      onTap: () {
        showAddEditInvoiceDialog(
          context,
        );
      },
    );
  }
}

void showAddEditInvoiceDialog(BuildContext context,
    {InvoicesModel? invoice}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final date =
                DateTime.tryParse(invoice?.date ?? '') ?? DateTime.now();

            final selectedDate = useState<DateTime>(date);
            final controllers = {
              ApiStrings.name: useTextEditingController(text: invoice?.name),
              ApiStrings.amount:
                  useTextEditingController(text: invoice?.amount.toString()),
            };

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            //!-----------------------------------------------------

            final invoiceController =
                ref.watch(invoicesControllerChangeNotifierProvider(context));

            Future<void> addAndEditInvoice() async {
              if (invoice == null) {
                await invoiceController.addInvoice(
                    controllers: controllers, selectedDate: selectedDate);
              } else {
                await invoiceController.editInvoice(
                    id: invoice.id!,
                    controllers: controllers,
                    selectedDate: selectedDate);
              }
            }

            return PopScope(
              onPopInvoked: (_) => invoiceController.clearData(
                  selectedDate: selectedDate,
                  controllers: controllers,
                  ref: ref),
              child: AlertDialogWidget(
                isImage: false,
                header: context.tr.addNewInvoice,
                isLoading: invoiceController.isLoading,
                child: Form(
                  key: formKey.value,
                  child: InvoiceFields(
                    fieldsControllers: controllers,
                    selectedDate: selectedDate,
                  ),
                ),
                onConfirm: () async {
                  if (!formKey.value.currentState!.validate()) return;
                  await addAndEditInvoice().then((value) =>
                      ref.watch(financialTabBarController).changeIndex(1));
                  if (context.mounted) {
                    context.back();

                    context.toReplacement(const FinancialScreen());
                  }
                },
              ),
            );
          },
        );
      });
}
