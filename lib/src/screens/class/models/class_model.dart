import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

List<ClassModel> responseToClassModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final classes = data.map((e) => ClassModel.fromJson(e)).toList();

  return classes;
}

class ClassModel extends BaseModel {
  final List<StudentModel>? students;
  final List<TeacherModel>? teachers;

  const ClassModel({
    super.id,
    super.name,
    super.description,
    super.image,
    super.createdAt,
    this.students,
    this.teachers,
  });

  //? From Json ---------------------------------------------------
  factory ClassModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final logo = attributes.containsKey(ApiStrings.logo) &&
            attributes[ApiStrings.logo] != null &&
            attributes[ApiStrings.logo][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(
            attributes[ApiStrings.logo][ApiStrings.data][ApiStrings.attributes])
        : null;

    final students = attributes.containsKey(ApiStrings.classStudents) &&
            attributes[ApiStrings.classStudents] != null &&
            attributes[ApiStrings.classStudents][ApiStrings.data] != null
        ? attributes[ApiStrings.classStudents][ApiStrings.data]
        : [];

    final studentList = List<StudentModel>.from(
        students.map((student) => StudentModel.fromJson(student))).toList();

    final isTeacherContainDataKey =
        attributes.containsKey(ApiStrings.classTeachers) &&
            attributes[ApiStrings.classTeachers] != null &&
            (attributes[ApiStrings.classTeachers] as Map).containsKey('data');

    final teachers = (isTeacherContainDataKey &&
            attributes[ApiStrings.classTeachers][ApiStrings.data] != null
        ? attributes[ApiStrings.classTeachers][ApiStrings.data]
        : []) as List<dynamic>?;

    final teachersList = List<TeacherModel>.from(teachers?.map(
                (teacher) => TeacherModel.fromAttributesJson(teacher ?? {})) ??
            [])
        .toList();

    studentList.insert(0, const StudentModel(id: 0));

    return ClassModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name] ?? '',
      description: attributes[ApiStrings.description] ?? '',
      createdAt: attributes[ApiStrings.createdAt],
      image: logo,
      teachers: teachersList,
      students: studentList,
    );
  }

  //? From Json ---------------------------------------------------
  factory ClassModel.fromStudentJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return ClassModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name] ?? '',
      description: attributes[ApiStrings.description] ?? '',
      createdAt: attributes[ApiStrings.createdAt],
    );
  }

  factory ClassModel.fromJsonWithOutAttributes(Map<String, dynamic> json) {
    final logo = json[ApiStrings.logo] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.logo])
        : null;

    // final students = json[ApiStrings.classStudents] ?? [];

    // final studentList = List<StudentModel>.from(
    //     students.map((student) => StudentModel.fromJson(student))).toList();
    //
    // final teachers = json[ApiStrings.classTeachers] ?? [];
    //
    // final teachersList = List<TeacherModel>.from(
    //         teachers.map((teacher) => TeacherModel.fromAttributesJson(teacher)))
    //     .toList();

    // studentList.insert(0, const StudentModel(id: 0));

    return ClassModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.name] ?? '',
      description: json[ApiStrings.description] ?? '',
      createdAt: json[ApiStrings.createdAt],
      image: logo,
      // students: studentList,
      // teachers: teachersList
    );
  }

//? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.description: description,
    };
  }

  factory ClassModel.empty() => const ClassModel(
      name: 'empty',
      description: 'empty',
      id: 0,
      teachers: [],
      students: [],
      image: BaseMediaModel(id: 0, url: ''));
}
