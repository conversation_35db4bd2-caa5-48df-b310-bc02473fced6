import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/view/add_class/add_class_dialog.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/widgets/class_card.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../models/class_model.dart';

class ClassesScreen extends HookConsumerWidget {
  const ClassesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final page = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final isInitialLoadComplete = useState<bool>(false);
    final classes = useState<List<ClassModel>>([]);

    final params = (context, page.value);

    useEffect(() {
      ref.refresh(getClassDataProviderWithPagination(params));
      return () {};
    }, [page.value]);

    ref.listenPagination<ClassModel>(
      provider: getClassDataProviderWithPagination(params),
      dataNotifier: classes,
      isLoadingNotifier: isLoadingMore,
      isInitialLoadCompleteNotifier: isInitialLoadComplete,
    );

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        Log.f('classes back');
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          appBar: MainAppBar(title: context.tr.classes, isBackButton: true),
          backgroundColor: ColorManager.secondaryColor,
          body: Padding(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            child: Column(
              children: [
                Center(
                  child: Button(
                    label: context.tr.createNewClass,
                    onPressed: () => showAddClassDialog(context,
                        navigateWidget: const ClassesScreen()),
                  ).sized(width: context.width / 1.3, height: 40.h),
                ),
                context.largeGap,
                Expanded(
                  child: BaseList(
                    page: page,
                    isLoading: !isInitialLoadComplete.value,
                    isLoadingMore: isLoadingMore,
                    data: classes.value,
                    itemBuilder: (classModel, index) =>
                        ClassCard(classModel: classModel),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
// class ClassesScreen extends ConsumerWidget {
//   const ClassesScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final classController = ref.watch(getClassDataProvider(context));
//
//     return WillPopScope(
//       onWillPop: () async {
//         context.toReplacement(const MainScreen());
//         Log.f('classes back');
//         return false;
//       },
//       child: SafeArea(
//         child: Scaffold(
//           appBar: MainAppBar(title: context.tr.classes, isBackButton: true),
//           backgroundColor: ColorManager.secondaryColor,
//           body: Column(
//             children: [
//               Expanded(
//                 child: Column(
//                   children: [
//                     //! Text Setup Your Classes
//                     Center(
//                       child: Button(
//                               label: context.tr.createNewClass,
//                               onPressed: () => showAddClassDialog(context,
//                                   navigateWidget: const ClassesScreen()))
//                           .sized(width: context.width / 1.3, height: 40.h),
//                     ),
//
//                     context.largeGap,
//
//                     //! Classes List
//                     classController.get(data: (classData) {
//                       return ClassesList(
//                         classes: classData,
//                       );
//                     })
//                   ],
//                 ).scroll(
//                   padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//                 ),
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
