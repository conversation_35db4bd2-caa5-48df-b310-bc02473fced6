import 'package:connectify_app/src/screens/attendance/view/widgets/attendance_filter_widget/main_attendance_filter.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../Activities/view/teacher_activty/teacher_activty_view/view/widgets/teacher_activity_view_list.dart';
import '../../../../student/view/student_screen/widgets/students_grid_view.dart';
import '../../../controllers/class_tab_bar_controller.dart';

class SelectedTeacherClassTabWidget extends ConsumerWidget {
  final ClassModel classModel;

  const SelectedTeacherClassTabWidget({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrl = ref.watch(tabBarControllerProvider);

    Log.i(classModel.students);
    final filteredStudents =
        classModel.students?.where((element) => element.id != 0).toList();

    switch (tabCtrl) {
      // * Student By Class ------------------------------------------------
      case 0:
        return StudentGridView(
          students: filteredStudents ?? [],
          navigateWidget: const ClassesScreen(),
        );

      // * Activities By Class ------------------------------------------------
      case 1:
        return const TeacherActivityViewList();

      // * Attendance  ------------------------------------------------
      case 2:
        return const AttendanceFilterWidget();

      default:
        return const SizedBox.shrink();
    }
  }
}
