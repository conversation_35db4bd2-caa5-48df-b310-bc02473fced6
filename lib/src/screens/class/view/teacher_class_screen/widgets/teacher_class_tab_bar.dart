import 'package:connectify_app/src/screens/class/controllers/class_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TeacherClassTabBar extends ConsumerWidget {
  final ClassModel classModel;

  const TeacherClassTabBar({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrl = ref.read(tabBarController);

    final List<String> tabs = [
      context.tr.students,
      'Daily Schedule',
      'Attendance Tracking',
    ];

    return PopScope(
      onPopInvoked: (_) {
        tabCtrl.changeIndex(0);
      },
      child: TapBarWidget(
          tabs: tabs,
          initialIndex: 0,
          onTab: (index) {
            tabCtrl.changeIndex(index);
          },
          classModel: classModel),
    );
  }
}
