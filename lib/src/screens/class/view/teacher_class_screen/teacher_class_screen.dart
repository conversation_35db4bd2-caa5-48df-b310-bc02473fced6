import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/teacher_class_screen/widgets/selected_tab_teacher_class.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import '../../../home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'widgets/teacher_class_tab_bar.dart';

class TeacherClassScreen extends ConsumerWidget {
  const TeacherClassScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classCtrl = ref.watch(getClassDataProvider(context));

    return SafeArea(
      child: Scaffold(
        appBar: MainAppBar(title: context.tr.back, isBackButton: true),
        body: classCtrl.get(
          data: (classData) {
            final classByTeacherId = classData.firstWhereOrNull((element) =>
                element.teachers!
                    .map((e) => e.id)
                    .contains(const UserModel().currentUser.id));

            log('asjcbkdcbk $classByTeacherId');
            return Column(
              children: [
                DetailsTopSectionWidget(
                    imagePath: classByTeacherId?.image?.url ?? '',
                    name: classByTeacherId?.name ?? '',
                    description: classByTeacherId?.description ?? '',
                    tabBarWidget: TeacherClassTabBar(
                      classModel: classByTeacherId ?? ClassModel.empty(),
                    )),
                Expanded(
                    child: SelectedTeacherClassTabWidget(
                  classModel: classByTeacherId ?? ClassModel.empty(),
                ).scroll().paddingOnly(
                          right: AppSpaces.smallPadding,
                          left: AppSpaces.smallPadding,
                          bottom: AppSpaces.smallPadding,
                        ))
              ],
            );
          },
        ),
      ),
    );
  }
}
