import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/class_tab_bar.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/selected_tab_widget.dart';

class ClassesDetailsScreen extends ConsumerWidget {
  final ClassModel classModel;

  const ClassesDetailsScreen({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final getClassById =
    //     ref.watch(getClassByIdDataProvider((context, classModel.id!)));

    return SafeArea(
      child: Scaffold(
          appBar: MainAppBar(title: context.tr.back, isBackButton: true),
          body: Column(
            children: [
              DetailsTopSectionWidget(
                  imagePath: classModel?.image?.url ?? '',
                  name: classModel?.name ?? '',
                  description: classModel?.description ?? '',
                  tabBarWidget: ClassTabBar(
                    classModel: classModel ?? this.classModel,
                  )),
              Expanded(
                  child: SelectedClassTabWidget(
                classModel: classModel ?? this.classModel,
              ).scroll().paddingOnly(
                        right: AppSpaces.smallPadding,
                        left: AppSpaces.smallPadding,
                        bottom: AppSpaces.smallPadding,
                      ))
            ],
          )
          // getClassById.get(data: (classModel) {
          //   return ;
          // }),
          ),
    );
  }
}
