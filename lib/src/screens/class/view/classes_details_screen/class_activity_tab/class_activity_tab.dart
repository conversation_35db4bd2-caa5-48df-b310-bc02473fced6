import 'package:connectify_app/src/screens/Activities/controllers/teacher_activities_controller.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/teacher_activity_card.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ClassTeacherActivities extends HookConsumerWidget {
  final ClassModel classModel;

  const ClassTeacherActivities({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getTeacherActivityCtrl = ref.watch(
        getTeacherActivitiesByClassDataProvider((context, classModel.id)));

    return getTeacherActivityCtrl.get(data: (teacherActivities) {
      return HookBuilder(builder: (context) {
        final teacherActivityList =
            useState<List<TeacherActivityModel>>(teacherActivities);
        final deletedActivityIds = useState<Set<int>>({});

        return ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) => WidgetAnimator(
                delay:
                    Duration(milliseconds: AppConsts.animatedDuration * index),
                child: TeacherActivityCardWidget(
                  selectedDate: DateTime.now().formatDateToString,
                  teacherActivity: teacherActivityList.value[index],
                  teacherActivityList: teacherActivityList,
                  deletedActivityIds: deletedActivityIds,
                )),
            itemCount: teacherActivityList.value
                .where((activity) =>
                    !deletedActivityIds.value.contains(activity.id))
                .length);
      });
    });
  }
}
