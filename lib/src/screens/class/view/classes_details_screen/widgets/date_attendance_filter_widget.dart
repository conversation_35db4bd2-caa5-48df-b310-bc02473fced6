import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class DateAttendanceFilterWidget extends StatelessWidget {
  final String selectedDay;
  final Function() onNext;
  final Function() onPrevious;
  final bool showPrevious;
  final bool showNext;
  final String date;

  const DateAttendanceFilterWidget(
      {super.key,
      required this.selectedDay,
      required this.onNext,
      required this.onPrevious,
      this.date = '',
      this.showPrevious = true,
      this.showNext = true});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showPrevious)
          IconButton(
              onPressed: () => onPrevious(),
              icon: const Icon(Icons.arrow_back_ios_rounded)),
        context.largeGap,
        Text(
          '$selectedDay${date.isNotEmpty ? '\n$date' : ''}',
          style: context.greyLabelLarge,
          textAlign: TextAlign.center,
        ),
        context.largeGap,
        if (showNext)
          IconButton(
              onPressed: () => onNext(),
              icon: const Icon(Icons.arrow_forward_ios_rounded)),
      ],
    );
  }
}
