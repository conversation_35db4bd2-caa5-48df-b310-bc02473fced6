import 'package:connectify_app/src/screens/attendance/view/widgets/attendance_list.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/class_activity_tab/class_activity_tab.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/classes_details_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../student/view/student_screen/widgets/students_grid_view.dart';
import '../../../../teacher/view/taechers_screen/widgets/teachers_grid_view.dart';
import '../../../controllers/class_tab_bar_controller.dart';

class SelectedClassTabWidget extends ConsumerWidget {
  final ClassModel classModel;

  const SelectedClassTabWidget({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrl = ref.watch(tabBarControllerProvider);
    // final teacherCtrl =
    //     ref.watch(getTeacherByClassDataProvider((context, classModel.id!)));
    Log.i(classModel.students);

    switch (tabCtrl) {
      // * Student By Class ------------------------------------------------
      case 0:
        return StudentGridView(
          students: classModel.students ?? [],
          navigateWidget: ClassesDetailsScreen(classModel: classModel),
        );

      // * Teachers By Class ------------------------------------------------
      case 1:
        return TeachersGridView(
          teachers: classModel.teachers ?? [],
          navigateWidget: ClassesDetailsScreen(classModel: classModel),
        );
      //   teacherCtrl.get(
      //   data: (teachersByClass) => ,
      // );

      // * Activities By Class ------------------------------------------------
      case 2:
        return ClassTeacherActivities(
          classModel: classModel,
        );

      // * Attendance  ------------------------------------------------
      case 3:
        return Column(
          children: [context.largeGap, AttendanceList(classModel: classModel)],
        );

      default:
        return const SizedBox.shrink();
    }
  }
}
