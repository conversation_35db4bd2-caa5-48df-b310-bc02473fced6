import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'setup_classes_fields.dart';

class AddClassDialog extends HookConsumerWidget {
  final Widget navigateWidget;

  const AddClassDialog({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return AddRectangleWidget(onTap: () {
      showAddClassDialog(
        context,
        navigateWidget: navigateWidget,
      ).then((value) {
        mediaController.clearFiles();
      });
    });
  }
}

Future<void> showAddClassDialog(BuildContext context,
    {required Widget navigateWidget, ClassModel? classModel}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final controllers = {
              ApiStrings.name: useTextEditingController(text: classModel?.name),
              ApiStrings.description:
                  useTextEditingController(text: classModel?.description),
            };

            final isEdit = classModel != null;

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            //!-----------------------------------------------------

            final classController =
                ref.watch(classChangeNotifierController(context));
            final filePath = ref.watch(mediaPickerControllerProvider).filePath;

            Future<void> addAndEditClass() async {
              if (!formKey.value.currentState!.validate()) return;

              if (isEdit) {
                await classController.editClass(
                    id: classModel.id!,
                    controllers: controllers,
                    navigateWidget: navigateWidget,
                    pickedImage: filePath);
              } else {
                await classController.addClass(
                    controllers: controllers,
                    pickedImage: filePath,
                    navigateWidget: navigateWidget);
              }

              classController.clearData(controllers: controllers, ref: ref);
            }

            return PopScope(
              onPopInvoked: (_) =>
                  classController.clearData(controllers: controllers, ref: ref),
              child: AlertDialogWidget(
                  networkImage: classModel?.image?.url ?? '',
                  iconPath: Assets.imagesLogo,
                  header: isEdit
                      ? context.tr.editClass
                      : context.tr.createANewClass,
                  isLoading: classController.isLoading,
                  child: Form(
                    key: formKey.value,
                    child: SetupClassesFields(
                      controllers: controllers,
                    ),
                  ),
                  onConfirm: () async {
                    await addAndEditClass();
                  }),
            );
          },
        );
      });
}
