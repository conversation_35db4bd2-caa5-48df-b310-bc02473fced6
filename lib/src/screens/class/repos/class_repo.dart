import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final classRepoProvider = Provider<ClassRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ClassRepo(networkApiService);
});

//? ===========================================================
class ClassRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  ClassRepo(this._networkApiService);

  //? Get Class Data with Pagination ------------------------------------------
  Future<List<ClassModel>> getClassesPaginated({int page = 1}) async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.classes}&${ApiEndpoints.pagination(page)}',
        );

        final classPages = compute(responseToClassModelList, response);

        return classPages;
      },
    );
  }

  Future<List<ClassModel>> getClasses() async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          ApiEndpoints.classes,
        );

        final classPages = compute(responseToClassModelList, response);

        return classPages;
      },
    );
  }

  //? Add Class Data ------------------------------------------
  Future<dynamic> addClass(
      {required ClassModel classModel, required String pickedImage}) async {
    return await baseFunction(() async {
      return await _networkApiService.postResponse(
        ApiEndpoints.classes,
        body: classModel.toJson(),
        filePaths: [pickedImage],
        fieldName: ApiStrings.logo,
      );
    });
  }

  //? Edit Class Data ------------------------------------------
  Future<void> editClass({
    required ClassModel classModel,
    required int id,
    required String pickedImage,
  }) async {
    return await baseFunction(() async {
      await _networkApiService.putResponse(
        '${ApiEndpoints.editDeleteClass}/$id',
        data: classModel.toJson(),
        filePaths: [pickedImage],
        fieldName: ApiStrings.logo,
      );
    });
  }

//? Delete Class Data ------------------------------------------
  Future<void> deleteClass({required int id}) async {
    return await baseFunction(() async {
      await _networkApiService
          .deleteResponse('${ApiEndpoints.editDeleteClass}/$id');
    });
  }
}
