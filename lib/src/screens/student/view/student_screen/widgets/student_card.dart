import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/switch_button_widget/switch_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/base_popupmenu/base_popupmenu.dart';
import '../../../../../shared/widgets/shared_widgets.dart';
import '../../../../nursery/models/nursery_model_helper.dart'
    show NurseryModelHelper;
import '../../../models/student_model.dart';
import 'add_student.dart';

class StudentCard extends HookConsumerWidget {
  final StudentModel student;
  final bool isSignUp;
  final Widget navigateWidget;

  const StudentCard(
      {super.key,
      required this.student,
      this.isSignUp = false,
      required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return isSignUp
        ? _SignupCard(student: student)
        : _StudentCard(
            student: student,
            navigateWidget: navigateWidget,
          );
  }
}

class _StudentCard extends HookConsumerWidget {
  final StudentModel student;
  final Widget navigateWidget;

  const _StudentCard({required this.student, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.watch(studentChangeNotifierProvider(context));

    final isActive = useState<bool>(student.isActive);

    final currentMonthStudentSubscription =
        student.subscriptions.firstWhereOrNull(
      (element) => element.date.isCurrentMonth,
    );

    final isPaid = currentMonthStudentSubscription?.isPaid ?? false;

    return InkWell(
      // onTap: () {
      //   if (const UserModel().isTeacher) {
      //     context.to(StudentDetailsScreen(
      //       student: student,
      //     ));
      //   }
      // },
      child: Stack(
        children: [
          Container(
            height: 100.h,
            width: 120.w,
            padding: const EdgeInsets.all(AppSpaces.xSmallPadding),
            decoration: BoxDecoration(
                border: Border.all(color: ColorManager.black.withOpacity(0.4)),
                color: student.isActive
                    ? ColorManager.white
                    : ColorManager.grey.withOpacity(0.5),
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                boxShadow: ConstantsWidgets.boxShadowFromBottom),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 50.h,
                  width: 50.w,
                  child: ClipRRect(
                    borderRadius:
                        BorderRadius.circular(AppRadius.baseContainerRadius),
                    child: Image.network(
                      student.image?.url ?? '',
                      height: 50.h,
                      width: 50.w,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          Image.network(
                        AppConsts.studentPlaceholder,
                      ),
                    ),
                  ),
                ),
                context.smallGap,
                Text(
                  student.name,
                  style: context.blueHint,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
                if (student.classModel != null &&
                    student.classModel!.name.isNotEmpty)
                  Text(
                    student.classModel?.name ?? '-',
                    style: context.smallHint.copyWith(fontSize: 12),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
              ],
            ),
          ),
          CircleAvatar(
            backgroundColor:
                isPaid ? ColorManager.buttonColor : ColorManager.errorColor,
            radius: 10,
            child: isPaid
                ? const Icon(
                    Icons.check,
                    color: ColorManager.white,
                    size: 15,
                  )
                : const Icon(
                    Icons.close,
                    color: ColorManager.white,
                    size: 15,
                  ),
          ).paddingAll(AppSpaces.smallPadding),
          // Positioned(
          //   left: context.isEng ? 5 : null,
          //   right: context.isEng ? null : 5,
          //   bottom: 8,
          //   child: Image.asset(
          //     student.gender == 'male' ? Assets.iconsMale : Assets.iconsFemale,
          //     height: 20,
          //   ),
          // ),
          Positioned(
            right: context.isEng ? -5.w : null,
            left: context.isEng ? null : -5.w,
            child: BasePopupmenu(
              editOnTap: () => showAddStudentDialog(context,
                  ref: ref, student: student, navigateWidget: navigateWidget),
              deleteOnTap: () => showDialog(
                  context: context,
                  builder: (context) => BaseDeleteDialog(
                      description: context.tr.areYouSureToDeleteThisStudent,
                      onConfirm: () async {
                        await studentCtrl.deleteStudent(
                            student: student, navigateWidget: navigateWidget);
                      })),

              //? Subscription Remind
              additionalWidget: isPaid
                  ? null
                  : PopupMenuItem(
                      onTap: () {
                        QuickAlert.show(
                          context: context,
                          title: context.tr.subscriptionRemind,
                          text: context.tr.areYouSureToSendSubscriptionRemind,
                          type: QuickAlertType.info,
                          confirmBtnText: context.tr.confirm,
                          cancelBtnText: context.tr.cancel,
                          showCancelBtn: true,
                          onConfirmBtnTap: () {
                            studentCtrl.sendSubscriptionRemind(
                              student: student,
                              title: "Subscription Reminder",
                              body: "Please remember to pay your subscription",
                            );
                            Navigator.of(context).pop();
                            context.showBarMessage(
                              context.tr.reminderSentSuccessfully,
                            );
                          },
                        );
                      },
                      child: Row(
                        children: [
                          Text(
                            context.tr.subscriptionRemind,
                            style: context.labelMedium.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange),
                          ),
                          const Spacer(),
                          const Icon(
                            Icons.notifications,
                            color: Colors.orange,
                          ),
                        ],
                      ),
                    ),
              switchWidget: PopupMenuItem(
                  child: Row(
                children: [
                  Text(
                    context.tr.active,
                    style: context.labelMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isActive.value
                            ? ColorManager.buttonColor
                            : ColorManager.grey),
                  ),
                  const Spacer(),
                  StatefulBuilder(builder: (context, setState) {
                    return SwitchButtonWidget(
                      value: isActive,
                      onChanged: (value) {
                        if (!isActive.value) {
                          final nurseryMaxStudents =
                              NurseryModelHelper.currentNursery()?.maxStudents;

                          if (StudentController.allActiveStudentsLength.value >=
                              nurseryMaxStudents!) {
                            QuickAlert.show(
                              context: context,
                              title: context.tr.warning,
                              text: context
                                  .tr.maxStudentsReachedPleaseContactSupport,
                              type: QuickAlertType.info,
                              confirmBtnText: context.tr.contactSupport,
                              cancelBtnText: context.tr.cancel,
                              onConfirmBtnTap: () {
                                launchUrl(
                                  Uri.parse('https://wa.me/201014878502'),
                                );
                              },
                            );

                            return;
                          }
                        }

                        isActive.value = value;

                        studentCtrl.activeOrDeActiveStudent(
                          id: student.id!,
                          isActive: value,
                        );

                        ref.refresh(getAllStudentsProvider(context));
                        ref.invalidate(getAllStudentsProvider(context));

                        if (value) {
                          StudentController.allActiveStudentsLength.value += 1;
                        } else {
                          StudentController.allActiveStudentsLength.value -= 1;
                        }

                        setState(() {});
                      },
                    );
                  })
                ],
              )),
            ),
          ),
        ],
      ),
    );
  }
}

class _SignupCard extends StatelessWidget {
  final StudentModel student;

  const _SignupCard({required this.student});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topRight,
      children: [
        Container(
          height: 100.h,
          width: 100.w,
          padding: const EdgeInsets.all(AppSpaces.xSmallPadding),
          decoration: BoxDecoration(
              border: Border.all(color: ColorManager.black.withOpacity(0.4)),
              color: ColorManager.white,
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              boxShadow: ConstantsWidgets.boxShadowFromBottom),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                child: BaseCachedImage(student.image?.url ?? '',
                    height: 50.h,
                    width: 53.w,
                    fit: BoxFit.cover,
                    errorWidget: const BaseCachedImage(
                      AppConsts.studentPlaceholder,
                    )),
              ),
              context.smallGap,
              Text(
                student.name,
                style: context.blueHint,
                maxLines: 2,
                textAlign: TextAlign.center,
              ),
              context.xSmallGap,
              Text(
                student.classModel?.name ?? '-',
                style: context.smallHint.copyWith(fontSize: 12),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )
            ],
          ),
        ),
        // Image.asset(
        //   student.gender == 'male' ? Assets.iconsMale : Assets.iconsFemale,
        //   height: 20,
        // ).paddingOnly(
        //   right: AppSpaces.smallPadding,
        //   top: AppSpaces.smallPadding,
        // ),
      ],
    );
  }
}
