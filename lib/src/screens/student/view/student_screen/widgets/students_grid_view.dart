import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/student_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/consts/app_constants.dart';
import '../../../../auth/models/user_model.dart';
import 'add_student.dart';

class StudentGridView extends HookConsumerWidget {
  final List<StudentModel> students;
  final bool isSignUp;
  final bool fromClassDetails;
  final Widget navigateWidget;

  const StudentGridView(
      {super.key,
      required this.students,
      this.isSignUp = false,
      this.fromClassDetails = true,
      required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEmptyList =
        const UserModel().isAdmin ? students.length == 1 : students.isEmpty;

    return isEmptyList
        ? EmptyStudentsList(
            navigateWidget: navigateWidget,
          )
        : AutoHeightGridView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: students.length,
            crossAxisCount: 3,
            mainAxisSpacing: 15.h,
            crossAxisSpacing: 10,
            builder: (BuildContext context, int index) {
              final student = students[index];

              if (student.id == 0 && const UserModel().isAdmin) {
                return AddStudentDialog(
                  fromClassDetails: fromClassDetails,
                  navigateWidget: navigateWidget,
                );
              }

              return WidgetAnimator(
                delay:
                    Duration(milliseconds: AppConsts.animatedDuration * index),
                child: StudentCard(
                  isSignUp: isSignUp,
                  student: student,
                  navigateWidget: navigateWidget,
                ),
              );
            });
  }
}

class EmptyStudentsList extends StatelessWidget {
  final Widget navigateWidget;

  const EmptyStudentsList({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (const UserModel().isAdmin)
          AddStudentDialog(
            navigateWidget: navigateWidget,
          ).sized(height: 120.h, width: 140.w),
        context.largeGap,
        Center(
          child: Text(
            context.tr.noStudents,
            style: textTheme(context).headlineMedium,
          ),
        )
      ],
    );
  }
}
