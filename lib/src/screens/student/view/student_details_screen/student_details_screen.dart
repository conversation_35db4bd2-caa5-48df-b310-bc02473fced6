import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import '../../../emergency/view/widgets/emergency_card_widget.dart';

class StudentDetailsScreen extends StatelessWidget {
  final StudentModel student;

  const StudentDetailsScreen({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    final isFatherPhone =
        student.parentPhoneNumber!.isEmpty || student.parentPhoneNumber == null;
    return Scaffold(
      appBar: MainAppBar(
        isBackButton: true,
        title: student.name,
      ),
      body: Column(
        children: [
          DetailsTopSectionWidget(
            errorImage: AppConsts.studentPlaceholder,
            imagePath: student.image?.url ?? '',
            name: student.name,
            description: student.classModel?.name ?? '',
          ),
          Container(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: Column(
                children: [
                  EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                    context.tr.mother,
                    student.motherPhoneNumber.toString()
                  ), iconPath: Assets.svgPhoneNumber),
                  context.largeGap,
                  if (!isFatherPhone) ...[
                    EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                      context.tr.father,
                      student.parentPhoneNumber ?? ''
                    ), iconPath: Assets.svgPhoneNumber),
                    context.largeGap,
                  ],
                  EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                    context.tr.address,
                    student.homeAddress ?? ''
                  ), iconPath: Assets.svgLocation, isPhone: false),
                  context.largeGap,
                  EmergencyTitleAndSubTitleWithIcon(titleAndSubTitle: (
                    context.tr.birthDate,
                    student.birthDate.formatDateToString ?? ''
                  ), isPhone: false),
                ],
              ))
        ],
      ),
    );
  }
}
