import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final studentRepoProvider = Provider<StudentRepo>((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return StudentRepo(networkApiServices);
});

class StudentRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  StudentRepo(this._networkApiServices);

  // * Get Students ===================================
  Future<List<StudentModel>> getStudents({
    bool onlyPickupPersons = false,
    String searchText = '',
  }) async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.students(
        onlyPickupPersons: onlyPickupPersons,
        searchText: searchText,
      ));

      final studentData = compute(responseToStudentModelList, response);
      return studentData;
    });
  }

  // * Get Subscription Students ===================================
  Future<List<StudentModel>> getSubscriptionStudents() async {
    return await baseFunction(() async {
      final response = await _networkApiServices
          .getResponse(ApiEndpoints.subscriptionStudents);

      final studentData = compute(responseToStudentModelList, response);
      return studentData;
    });
  }

  // * Get Active Students count ===================================
  Future<int> activeStudentsCount() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.activeStudents());
      return response[ApiStrings.count];
    });
  }

  // * Get Students with Pagination ===================================
  Future<List<StudentModel>> getStudentsPaginated({int page = 1}) async {
    return await baseFunction(() async {
      log('asfasfasf ${selectedTeacherClass.value?.id}');
      final response = await _networkApiServices
          .getResponse(ApiEndpoints.studentsPaginated(page));
      final studentData = compute(responseToStudentModelList, response);
      return studentData;
    });
  }

  // * Add Students ===================================
  Future<Map<String, dynamic>> addStudent(
      {required StudentModel student,
      required String pickedImage,
      required ClassModel? selectedClass}) async {
    return await baseFunction(() async {
      final res = await _networkApiServices.postResponse(
        ApiEndpoints.students(),
        body: student.toJson(),
        filePaths: [pickedImage],
      );

      return res[ApiStrings.data];
    });
  }

  // * Edit Students ===================================
  Future<void> editStudent(
      {required StudentModel student,
      required String pickedImage,
      required int id,
      required ClassModel? selectedClass}) async {
    return await baseFunction(() async {
      return await _networkApiServices.putResponse(
          filePaths: [pickedImage],
          '${ApiEndpoints.editDeleteStudents}/$id',
          data: student.toJson());
    });
  }

  //? Active or DeActive Student Data ------------------------------------------
  Future<void> activeDeActiveStudent(
      {required int id, required bool isActive}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .putResponse('${ApiEndpoints.editDeleteStudents}/$id', data: {
        ApiStrings.isActive: isActive,
      });
    });
  }

  //? Pay Subscription ------------------------------------------
  Future<void> paySubscription({
    required int studentId,
    required List<SubscriptionModel> subscriptions,
  }) async {
    return await baseFunction(() async {
      await _networkApiServices
          .putResponse('${ApiEndpoints.editDeleteStudents}/$studentId', data: {
        ApiStrings.subscriptions: subscriptions.map((e) => e.toJson()).toList(),
      });
    });
  }

  //? Get Subscriptions Data ------------------------------------------
  Future<(num totalPaidStudents, num totalUnpaidStudents, num totalPaidAmounts)>
      getSubscriptionsData() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.subscriptions);
      final totalPaidStudents = (response['totalPaidStudents'] as num?) ?? 0;
      final totalUnpaidStudents =
          (response['totalUnpaidStudents'] as num?) ?? 0;
      final totalPaidAmounts = (response['totalPaidAmounts'] as num?) ?? 0;

      return (totalPaidStudents, totalUnpaidStudents, totalPaidAmounts);
    });
  }

//? Delete Student Data ------------------------------------------
  Future<void> deleteStudent({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices
          .deleteResponse('${ApiEndpoints.editDeleteStudents}/$id');
    });
  }
}
