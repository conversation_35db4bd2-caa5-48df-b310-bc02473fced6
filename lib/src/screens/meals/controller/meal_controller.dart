import 'package:connectify_app/src/screens/meals/repo/meal_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/meal_model.dart';

final mealControllerProvider =
    Provider.family<MealController, BuildContext>((ref, context) {
  final mealRepo = ref.watch(mealRepoProvider);

  return MealController(mealRepo: mealRepo, context: context);
});

final mealControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<MealController, BuildContext>((ref, context) {
  final mealRepo = ref.watch(mealRepoProvider);

  return MealController(mealRepo: mealRepo, context: context);
});

final getMealDataProvider =
    FutureProvider.family<List<MealModel>, BuildContext>((ref, context) async {
  final mealController = ref.watch(mealControllerProvider(context));

  return await mealController.getMealsData();
});

class MealController extends BaseVM {
  final MealRepo mealRepo;
  final BuildContext context;

  MealController({required this.mealRepo, required this.context});

  Future<List<MealModel>> getMealsData() async {
    return await baseFunction(context, () async {
      return await mealRepo.getMealsData();
    });
  }

  Future<void> addMeal({required MealModel meal}) async {
    return await baseFunction(context, () async {
      await mealRepo.addMeal(meal: meal);
    });
  }

  Future<void> editMeal({
    required MealModel meal,
  }) async {
    return await baseFunction(context, () async {
      await mealRepo.editMeal(meal: meal);
    });
  }

  Future<void> deleteMeal({required int id}) async {
    return await baseFunction(context, () async {
      await mealRepo.deleteMeal(id: id);
    });
  }
}
