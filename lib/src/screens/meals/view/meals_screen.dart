import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/meals/controller/meal_controller.dart';
import 'package:connectify_app/src/screens/meals/model/meal_model.dart';
import 'package:connectify_app/src/screens/meals/view/widgets/meal_tab_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../food/model/food_model.dart';

class MealsScreen extends HookConsumerWidget {
  const MealsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTab = useState<int>(0);
    final mealController = ref.watch(mealControllerProvider(context));
    final mealDataFuture = ref.watch(getMealDataProvider(context));

    final originalValues = useState<Map<String, String>>({});
    final changedValues = useState<Map<String, String>>({});

    final isLoading = useState<bool>(false);

    return Scaffold(
      appBar: AppBar(title: Text(context.tr.meals)),
      body: mealDataFuture.get(
        data: (meals) {
          return Column(
            children: [
              context.mediumGap,
              MealsTabBar(
                selectedTab: selectedTab,
                onTabChanged: (index) => selectedTab.value = index,
              ),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: WeekDays.values.length,
                  itemBuilder: (context, index) {
                    final day = WeekDays.values[index];
                    final mealType = MealTypes.values[selectedTab.value];
                    final meal = meals.firstWhereOrNull(
                      (m) => m.mealType == mealType && m.day == day,
                    );

                    return HookBuilder(builder: (context) {
                      final controller =
                          TextEditingController(text: meal?.name);

                      useEffect(() {
                        originalValues.value['${mealType.name}-${day.name}'] =
                            meal?.name ?? '';
                        return () {};
                      }, []);

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: TextField(
                          controller: controller,
                          decoration: InputDecoration(
                            labelText: day.name,
                            labelStyle: const TextStyle(color: Colors.grey),
                          ),
                          onChanged: (text) {
                            final key = '${mealType.name}-${day.name}';
                            if (originalValues.value[key] != text) {
                              changedValues.value[key] = text;
                            } else {
                              changedValues.value.remove(key);
                            }
                          },
                        ),
                      );
                    });
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Button(
                  isLoading: isLoading.value,
                  loadingWidget: const LoadingWidget(),
                  onPressed: () async {
                    isLoading.value = true;

                    await Future.forEach(
                      changedValues.value.entries,
                      (MapEntry<String, String> entry) async {
                        final parts = entry.key.split('-');
                        final mealType = MealTypes.values
                            .firstWhere((e) => e.name == parts[0]);
                        final day = WeekDays.values
                            .firstWhere((e) => e.name == parts[1]);
                        final existingMeal = meals.firstWhereOrNull(
                          (m) => m.mealType == mealType && m.day == day,
                        );

                        if (entry.value.isEmpty && existingMeal != null) {
                          await mealController.deleteMeal(id: existingMeal.id!);
                        } else {
                          final meal = MealModel(
                            id: existingMeal?.id,
                            mealType: mealType,
                            day: day,
                            name: entry.value,
                          );

                          if (existingMeal != null) {
                            await mealController.editMeal(meal: meal);
                          } else {
                            await mealController.addMeal(meal: meal);
                          }
                        }
                      },
                    );

                    context.toReplacement(const MealsScreen());
                    context.showBarMessage(context.tr.savedSuccessfully);
                  },
                  label: context.tr.save,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
