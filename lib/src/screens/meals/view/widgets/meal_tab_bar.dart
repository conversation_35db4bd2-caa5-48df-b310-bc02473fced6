import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class MealsTabBar extends HookWidget {
  final ValueNotifier<int> selectedTab;
  final Function(int) onTabChanged;

  const MealsTabBar(
      {super.key, required this.selectedTab, required this.onTabChanged});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: selectedTab.value,
      length: 3,
      child: Container(
        height: 58,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: TabBar(
          dividerHeight: 0,
          onTap: (index) {
            selectedTab.value = index;
            onTabChanged(index);
          },
          padding: const EdgeInsets.all(AppSpaces.smallPadding),
          indicator: BoxDecoration(
            color: ColorManager.buttonColor,
            borderRadius: BorderRadius.circular(10.0),
          ),
          labelStyle: context.hint.copyWith(fontSize: 16),
          labelColor: Colors.white,
          indicatorSize: TabBarIndicatorSize.tab,
          unselectedLabelColor: Colors.black,
          tabs: [
            Tab(text: context.tr.breakfast),
            Tab(text: context.tr.snack),
            Tab(text: context.tr.lunch),
          ],
        ),
      ),
    ).paddingSymmetric(horizontal: 12);
  }
}
