import 'package:connectify_app/src/screens/meals/model/meal_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final mealRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return MealRepo(networkApiServices);
});

class MealRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  MealRepo(this._networkApiServices);

  Future<List<MealModel>> getMealsData() async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(ApiEndpoints.meal);

      final mealsData = List.from(response[ApiStrings.data])
          .map((e) => MealModel.fromJson(e))
          .toList();

      return mealsData;
    });
  }

  Future<void> addMeal({required MealModel meal}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(ApiEndpoints.meal,
          body: meal.toJson());
    });
  }

  Future<void> editMeal({required MealModel meal}) async {
    return await baseFunction(() async {
      return await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeleteMeal}/${meal.id}',
        data: meal.toJson(),
      );
    });
  }

  Future<void> deleteMeal({required int id}) async {
    return await baseFunction(() async {
      return await _networkApiServices.deleteResponse(
        '${ApiEndpoints.editDeleteMeal}/$id',
      );
    });
  }
}
