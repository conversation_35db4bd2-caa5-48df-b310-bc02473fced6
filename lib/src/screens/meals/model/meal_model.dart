import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../food/model/food_model.dart';

enum WeekDays { Saturday, Sunday, Monday, Tuesday, Wednesday, Thursday, Friday }

class MealModel extends BaseModel {
  final MealTypes? mealType;
  final WeekDays? day;

  const MealModel({
    super.id,
    super.name,
    this.mealType,
    this.day,
  });

  factory MealModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return MealModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.meal],
      mealType: MealModel.getMealTypeValue(attributes[ApiStrings.type]),
      day: MealModel.getWeekDayValue(attributes[ApiStrings.day]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.meal: name,
      ApiStrings.type: mealType == MealTypes.breakFast
          ? "Breakfast"
          : mealType == MealTypes.snack
              ? "Snack"
              : "Lunch",
      ApiStrings.day: day?.name,
    };
  }

  static MealTypes getMealTypeValue(String value) {
    switch (value.toLowerCase()) {
      case 'breakfast':
        return MealTypes.breakFast;
      case 'snack':
        return MealTypes.snack;
      case 'lunch':
        return MealTypes.lunch;
      default:
        return MealTypes.breakFast;
    }
  }

  static WeekDays getWeekDayValue(String value) {
    switch (value.toLowerCase()) {
      case 'monday':
        return WeekDays.Monday;
      case 'tuesday':
        return WeekDays.Tuesday;
      case 'wednesday':
        return WeekDays.Wednesday;
      case 'thursday':
        return WeekDays.Thursday;
      case 'friday':
        return WeekDays.Friday;
      case 'saturday':
        return WeekDays.Saturday;
      case 'sunday':
        return WeekDays.Sunday;
      default:
        return WeekDays.Monday;
    }
  }
}
