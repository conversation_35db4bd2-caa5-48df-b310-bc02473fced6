// * Class Provider Controller ========================================
import 'package:connectify_app/src/screens/Activities/models/activity_model.dart';
import 'package:connectify_app/src/screens/Activities/repos/activities_repo.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

final activityProviderController =
    Provider.family<ActivityController, BuildContext>((ref, context) {
  final activityRepo = ref.watch(activityRepoProvider);

  return ActivityController(context, activityRepo: activityRepo);
});

final activityChangeNotifierProvider =
    ChangeNotifierProvider.family<ActivityController, BuildContext>(
        (ref, context) {
  final activityRepo = ref.watch(activityRepoProvider);

  return ActivityController(context, activityRepo: activityRepo);
});

//getActivitiesDataProviderWithPagination
final getActivitiesDataProviderWithPagination =
    FutureProvider.family<List<ActivityModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;
    final activityCtrl = ref.watch(activityProviderController(context));

    return await activityCtrl.getActivitiesDataPagination(
      page: page,
    );
  },
);

// * Get Activities Data ========================================
final getActivitiesDataProvider =
    FutureProvider.family<List<ActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(activityProviderController(context));

  return await activityCtrl.getActivitiesData();
});

//?==============================================================
class ActivityController extends BaseVM {
  final BuildContext context;
  final ActivityRepo activityRepo;

  ActivityController(this.context, {required this.activityRepo});

  //? Get Activities Data ------------------------------------------
  //getActivitiesDataProviderWithPagination
  Future<List<ActivityModel>> getActivitiesDataPagination({int page = 1}) async {
    return await baseFunction(
      context,
      () async {
        final activities = await activityRepo.getActivitiesDataPagination(page: page);

        return activities;
      },
    );
  }
  Future<List<ActivityModel>> getActivitiesData() async {
    return await baseFunction(
      context,
      () async {
        final activities = await activityRepo.getActivities();

        return activities;
      },
    );
  }

//? Get Activities Data ------------------------------------------
  Future<void> addActivity(
      {required Map<String, TextEditingController> controllers,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final activity = ActivityModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.description]!.text,
      );

      await activityRepo.addActivity(
        activity: activity,
        pickedImage: pickedImage,
      );

      NotificationService.sendNotification(
        title: "Activity Update",
        body: "A new activity named ${activity.name} has been added",
        userTokenOrTopic: NurseryModelHelper.allTeacherTopic(),
        isTopic: true,
      );

      postNewNotification(
        notificationModel: NotificationModel(
          title: "Activity Update",
          body: "A new activity named ${activity.name} has been added",
          topic: NurseryModelHelper.allTeacherTopic(),
        ),
      );

      getActivitiesData();
    });
  }

  //? Edit Activity Data ------------------------------------------
  Future<void> editActivity(
      {required Map<String, TextEditingController> controllers,
      required int id,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final activity = ActivityModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.description]!.text,
      );

      await activityRepo.editActivity(
          activity: activity, id: id, pickedImage: pickedImage);
    }, additionalFunction: (context) => getActivitiesData());
  }

//? Delete Activity Data ------------------------------------------
  Future<void> deleteActivity({required ActivityModel activity}) async {
    return await baseFunction(context, () async {
      await activityRepo.deleteActivity(id: activity.id!);

      NotificationService.sendNotification(
        title: "Activity Update",
        body: "The activity named ${activity.name} has been removed",
        userTokenOrTopic: NurseryModelHelper.allTeacherTopic(),
        isTopic: true,
      );

      postNewNotification(
        notificationModel: NotificationModel(
          title: "Activity Update",
          body: "The activity named ${activity.name} has been removed",
          topic: NurseryModelHelper.allTeacherTopic(),
        ),
      );

      getActivitiesData();
    });
  }
}
