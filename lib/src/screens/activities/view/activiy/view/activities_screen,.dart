import 'package:connectify_app/src/screens/Activities/controllers/activities_controller.dart';
import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/add_activity_dialog.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../Activities/models/activity_model.dart';
import '../../../../home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'widgets/activity_card.dart';

class ActivitiesScreen extends HookConsumerWidget {
  const ActivitiesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final page = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final isInitialLoadComplete = useState<bool>(false);
    final activities = useState<List<ActivityModel>>([]);

    final params = (context, page.value);

    useEffect(() {
      ref.refresh(getActivitiesDataProviderWithPagination(params));
      return () {};
    }, [page.value]);

    ref.listenPagination<ActivityModel>(
      provider: getActivitiesDataProviderWithPagination(params),
      dataNotifier: activities,
      isLoadingNotifier: isLoadingMore,
      isInitialLoadCompleteNotifier: isInitialLoadComplete,
    );

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          appBar: MainAppBar(
            title: context.tr.dailySchedule,
            isBackButton: true,
          ),
          body: Padding(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            child: Column(
              children: [
                // if (const UserModel().isAdmin) ...[
                context.largeGap,
                const AddActivityDialog(
                  navigateWidget: ActivitiesScreen(),
                ),
                // ],
                context.mediumGap,
                Expanded(
                  child: BaseList(
                    page: page,
                    isLoading: !isInitialLoadComplete.value,
                    isLoadingMore: isLoadingMore,
                    data: activities.value,
                    itemBuilder: (activityModel, index) => ActivityCard(
                      activity: activityModel,
                      navigateWidget: const ActivitiesScreen(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// import 'package:connectify_app/src/screens/Activities/controllers/activities_controller.dart';
// import 'package:connectify_app/src/screens/Activities/view/activiy/view/widgets/activities_list.dart';
// import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/add_activity_dialog.dart';
// import 'package:connectify_app/src/screens/auth/models/user_model.dart';
// import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
// import '../../../../home/<USER>/main_screen/widgets/main_app_bar.dart';
//
// class ActivitiesScreen extends ConsumerWidget {
//   const ActivitiesScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//
//     return WillPopScope(
//       onWillPop: ()async {
//         context.toReplacement(const MainScreen());
//         return false;
//       },
//       child: SafeArea(
//         child: Scaffold(
//           appBar: MainAppBar(
//             title: context.tr.dailySchedule,
//             isBackButton: true,
//           ),
//           body: Column(
//             children: [
//               //! Add Activity
//               if (const UserModel().isAdmin)...[
//                 context.largeGap,
//
//                 const AddActivityDialog(
//                   navigateWidget: ActivitiesScreen(),
//                 ),
//
//               ],
//
//               context.mediumGap,
//
//               //! Activities List
//               const ActivitiesList(
//                 navigateWidget: ActivitiesScreen(),
//               ),
//             ],
//           ).paddingAll(AppSpaces.mediumPadding).scroll(),
//         ),
//       ),
//     );
//   }
// }
