import 'package:connectify_app/src/screens/Activities/controllers/activities_controller.dart';
import 'package:connectify_app/src/screens/Activities/models/activity_model.dart';
import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/add_activity_dialog.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_popupmenu/base_popupmenu.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final List<String> weekDays = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

class ActivityCard extends HookConsumerWidget {
  final ActivityModel activity;
  final Widget navigateWidget;
  final bool isSignUp;

  const ActivityCard(
      {super.key,
      required this.activity,
      required this.navigateWidget,
      this.isSignUp = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityCtrl = ref.watch(activityChangeNotifierProvider(context));
    return Stack(
      alignment: Alignment.topRight,
      children: [
        Column(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.smallPadding),
              margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
              width: double.infinity,
              decoration: BoxDecoration(
                  border: Border.all(color: ColorManager.grey),
                  color: ColorManager.white,
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius),
                  boxShadow: ConstantsWidgets.boxShadowFromBottom),
              child: Row(
                children: [
                  SizedBox(
                    height: 50.h,
                    width: 55.w,
                    child: ClipRRect(
                      borderRadius:
                          BorderRadius.circular(AppRadius.baseContainerRadius),
                      child: BaseCachedImage(activity.image?.url ?? '',
                          errorWidget: const BaseCachedImage(
                            AppConsts.activityPlaceholder,
                          )),
                    ),
                  ),
                  context.smallGap,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        activity.name,
                        style: context.blueHint,
                        maxLines: 1,
                      ),
                      context.xSmallGap,
                      Text(
                        activity.description,
                        style: context.smallHint.copyWith(fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ).sized(width: context.width * .7),
                ],
              ),
            ),
          ],
        ),
        if (!isSignUp)
          BasePopupmenu(
              editOnTap: () {
                showAddEditActivityDialog(context,
                    navigateWidget: navigateWidget, activity: activity);
              },
              deleteOnTap: () => showDialog(
                    context: context,
                    builder: (context) => BaseDeleteDialog(
                      description: context.tr.areYouSureToDeleteThisActivity,
                      onConfirm: () async {
                        await activityCtrl.deleteActivity(activity: activity);
                        if (!context.mounted) return;
                        context.toReplacement(navigateWidget);
                        context.showBarMessage(context.tr.deletedSuccessfully,
                            isError: true);
                      },
                    ),
                  ))
      ],
    );
  }
}
