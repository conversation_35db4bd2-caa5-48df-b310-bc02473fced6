import 'package:connectify_app/src/screens/Activities/controllers/activities_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/consts/app_constants.dart';
import 'activity_card.dart';

class ActivitiesList extends ConsumerWidget {
  final Widget navigateWidget;
  final bool isSignUp;
  final int? classId;

  const ActivitiesList({
    super.key,
    required this.navigateWidget,
    this.isSignUp = false,
    this.classId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityCtrl = ref.watch(getActivitiesDataProvider(context));
    return activityCtrl.get(data: (activities) {
      if (activities.isEmpty) {
        return Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(
            child: Text(
              context.tr.noActivities,
              style: textTheme(context).headlineMedium,
            ),
          ),
        );
      }

      return ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) => WidgetAnimator(
              delay: Duration(milliseconds: AppConsts.animatedDuration * index),
              child: ActivityCard(
                  isSignUp: isSignUp,
                  navigateWidget: navigateWidget,
                  activity: activities[index])),
          itemCount: activities.length);
    });
  }
}
