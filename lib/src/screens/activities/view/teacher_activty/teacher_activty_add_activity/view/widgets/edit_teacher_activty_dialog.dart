import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/Activities/repos/teacher_activities_repo.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:connectify_app/src/screens/Activities/controllers/teacher_activities_controller.dart';
import 'package:flutter/scheduler.dart';
import 'package:xr_helper/xr_helper.dart';
import '../../../../../../sleep/view/widgets/add_sleep_widgets.dart';
import '../../../../activiy/view/widgets/activity_card.dart';

Future<void> showEditTeacherActivityDialog(BuildContext context,
    {required Widget navigateWidget,
    TeacherActivityModel? teacherActivity}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final controllers = {
              ApiStrings.from:
                  useTextEditingController(text: teacherActivity?.startTime),
              ApiStrings.to:
                  useTextEditingController(text: teacherActivity?.endTime),
            };

            final day = useState(teacherActivity?.day);

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            //!-----------------------------------------------------

            final activityCtrl =
                ref.watch(teacherActivityChangeNotifierProvider(context));

            Future<void> editTeacherActivity() async {
              await activityCtrl.editActivity(
                  teacherActivity: TeacherActivityModel(
                      startTime: controllers[ApiStrings.from]!.text,
                      endTime: controllers[ApiStrings.to]!.text,
                      day: day.value ?? ''),
                  id: teacherActivity!.id!);
            }

            return AlertDialogWidget(
                header: context.tr.edit,
                isImage: false,
                isLoading: activityCtrl.isLoading,
                child: Form(
                  key: formKey.value,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            height: 50.h,
                            width: 55.w,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  AppRadius.baseContainerRadius),
                              child: Image.network(
                                teacherActivity?.activity?.image?.url ?? '',
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          context.smallGap,
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                teacherActivity?.activity?.name ?? '',
                                style: context.blueHint,
                                maxLines: 1,
                              ),
                              context.xSmallGap,
                              Text(
                                teacherActivity?.activity?.description ?? '',
                                style: context.smallHint.copyWith(fontSize: 12),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ],
                      ),
                      context.mediumGap,
                      _WeekDaysAndStartTimeAndEndTime(
                        day: day,
                        teacherActivity: teacherActivity!,
                        controllers: controllers,
                      ),
                    ],
                  ),
                ),
                onConfirm: () async {
                  await editTeacherActivity();
                });
          },
        );
      });
}

class _WeekDaysAndStartTimeAndEndTime extends HookConsumerWidget {
  final TeacherActivityModel teacherActivity;
  final ValueNotifier<String?> day;
  final Map<String, TextEditingController> controllers;

  const _WeekDaysAndStartTimeAndEndTime(
      {required this.teacherActivity,
      required this.controllers,
      required this.day});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherActivityCtrl =
        ref.watch(teacherActivityProviderController(context));

    useEffect(() {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        controllers[ApiStrings.startTime]?.text = teacherActivity.startTime;
      });

      return () {};
    });
    return Column(
      children: [
        BaseDropDown(
            onChanged: (day) {
              this.day.value = day;
            },
            data: weekDays,
            label: context.tr.day,
            selectedValue: teacherActivity.day),
        context.largeGap,
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () async {
                  final data = await showTimePickerDialog(
                    context,
                  );

                  if (data == null) {
                    controllers[ApiStrings.from]?.text =
                        teacherActivity.startTime;
                  } else {
                    controllers[ApiStrings.from]?.text = data;
                  }
                },
                child: BaseTextField(
                  title: context.tr.from,
                  controller: controllers[ApiStrings.from],
                  enabled: false,
                  hint: teacherActivity.startTime,
                ),
              ),
            ),
            context.smallGap,
            Expanded(
              child: GestureDetector(
                onTap: () async {
                  final data = await showTimePickerDialog(
                    context,
                  );

                  if (data == null) {
                    controllers[ApiStrings.to]?.text = teacherActivity.endTime;
                  } else {
                    controllers[ApiStrings.to]?.text = data;
                  }
                },
                child: BaseTextField(
                  title: context.tr.to,
                  controller: controllers[ApiStrings.to],
                  enabled: false,
                  hint: teacherActivity.endTime,
                ),
              ),
            ),
          ],
        ),
      ],
    ).paddingAll(AppSpaces.mediumPadding);
  }
}
// class _WeekDaysAndStartTimeAndEndTime extends HookConsumerWidget {
//   final TeacherActivityModel teacherActivity;
//   final Map<String, TextEditingController> controllers;
//
//   const _WeekDaysAndStartTimeAndEndTime(
//       {required this.teacherActivity, required this.controllers});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final teacherActivityCtrl =
//     ref.watch(teacherActivityProviderController(context));
//
//     useEffect(() {
//       SchedulerBinding.instance.addPostFrameCallback((_) {
//         controllers[ApiStrings.startTime]?.text = teacherActivity.startTime;
//       });
//
//       return () {};
//     });
//     return Column(
//       children: [
//         BaseDropDown(
//             onChanged: (day) {
//               teacherActivityCtrl.editActivity(
//                   teacherActivity: TeacherActivityModel(day: day),
//                   id: teacherActivity.id!);
//             },
//             data: weekDays,
//             label: context.tr.day,
//             selectedValue: teacherActivity.day),
//         context.largeGap,
//         Row(
//           children: [
//             Expanded(
//               child: GestureDetector(
//                 onTap: () async {
//                   final data = await showTimePickerDialog(
//                     context,
//                   );
//
//                   if (data == null) {
//                     controllers[ApiStrings.from]?.text =
//                         teacherActivity.startTime;
//                   } else {
//                     controllers[ApiStrings.from]?.text = data;
//                     teacherActivityCtrl.editActivity(
//                         teacherActivity:
//                         TeacherActivityModel(startTime: data ?? ''),
//                         id: teacherActivity.id!);
//                   }
//                 },
//                 child: BaseTextField(
//                   title: context.tr.from,
//                   controller: controllers[ApiStrings.from],
//                   enabled: false,
//                   hint: teacherActivity.startTime,
//                 ),
//               ),
//             ),
//             context.smallGap,
//             Expanded(
//               child: GestureDetector(
//                 onTap: () async {
//                   final data = await showTimePickerDialog(
//                     context,
//                   );
//
//                   if (data == null) {
//                     controllers[ApiStrings.to]?.text = teacherActivity.endTime;
//                   } else {
//                     controllers[ApiStrings.to]?.text = data;
//                     teacherActivityCtrl.editActivity(
//                         teacherActivity:
//                         TeacherActivityModel(endTime: data ?? ''),
//                         id: teacherActivity.id!);
//                   }
//                 },
//                 child: BaseTextField(
//                   title: context.tr.to,
//                   controller: controllers[ApiStrings.to],
//                   enabled: false,
//                   hint: teacherActivity.endTime,
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ],
//     ).paddingAll(AppSpaces.mediumPadding);
//   }
// }
