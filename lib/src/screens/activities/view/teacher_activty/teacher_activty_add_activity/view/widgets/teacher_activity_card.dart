import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/edit_teacher_activity_fields.dart';
import 'package:connectify_app/src/screens/activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/teacher_activity_media_widget.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_popupmenu/base_popupmenu.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../shared/services/media/controller/media_controller.dart';
import '../../../../../../../shared/widgets/dialogs/base_delete_dialog.dart';
import '../../../../../../Activities/controllers/teacher_activities_controller.dart';

class TeacherActivityCardWidget extends HookConsumerWidget {
  final TeacherActivityModel teacherActivity;
  final Map<String, ValueNotifier>? controllers;
  final ValueNotifier<List<TeacherActivityModel>> teacherActivityList;
  final ValueNotifier<Set<int>> deletedActivityIds;
  final String selectedDate;
  final Function()? onSavedMedia;

  const TeacherActivityCardWidget({
    super.key,
    required this.teacherActivity,
    required this.teacherActivityList,
    required this.deletedActivityIds,
    required this.selectedDate,
    this.onSavedMedia,
    this.controllers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    useAutomaticKeepAlive();

    final activityCtrl =
        ref.watch(teacherActivityChangeNotifierProvider(context));
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final note = teacherActivity.notes
            .lastWhereOrNull((element) => element.date == selectedDate)
            ?.note ??
        '';

    final mediaList = useState(teacherActivity.notes
            .lastWhereOrNull((element) => element.date == selectedDate)
            ?.media ??
        []);

    log('afsfaff ${mediaList.value}');

    final noteController = useMemoized(
      () => TextEditingController(text: note),
      [teacherActivity.id, selectedDate],
    );

    useEffect(() {
      if (note.isNotEmpty && noteController.text.isEmpty) {
        noteController.text = note;
      }
      return () {};
    }, [selectedDate]);

    return Stack(
      alignment: context.isEng ? Alignment.topRight : Alignment.topLeft,
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpaces.smallPadding),
          margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
          width: double.infinity,
          decoration: BoxDecoration(
              border: Border.all(color: ColorManager.grey),
              color: ColorManager.white,
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              boxShadow: ConstantsWidgets.boxShadowFromBottom),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 50.h,
                    width: 55.w,
                    child: ClipRRect(
                      borderRadius:
                          BorderRadius.circular(AppRadius.baseContainerRadius),
                      child: Image.network(
                          teacherActivity.activity?.image?.url ?? '',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              const BaseCachedImage(
                                AppConsts.activityPlaceholder,
                              )),
                    ),
                  ),
                  context.smallGap,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          child: Text(
                            teacherActivity.activity?.name ?? '',
                            style: context.blueHint,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        context.xSmallGap,
                        Text(
                          teacherActivity.activity?.description ?? '',
                          style: context.smallHint.copyWith(fontSize: 12),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ).paddingOnly(top: 4),
                  ),
                  Container(
                    padding: const EdgeInsets.all(6),
                    margin: EdgeInsets.only(
                        right: context.isEng ? AppSpaces.largePadding + 4 : 0,
                        left: context.isEng ? 0 : AppSpaces.largePadding + 4),
                    decoration: BoxDecoration(
                      color: teacherActivity.isWeekly
                          ? ColorManager.purpleColor
                          : ColorManager.primaryColor,
                      borderRadius: const BorderRadius.all(
                        Radius.circular(AppRadius.baseRadius),
                      ),
                    ),
                    child: Text(
                      teacherActivity.isWeekly
                          ? context.tr.weeklyActivity
                          : context.tr.singleActivity,
                      style: context.whiteHint,
                    ),
                  ),
                ],
              ),
              if (controllers != null) ...[
                context.mediumGap,
                EditTeacherActivityFields(
                  teacherActivity: teacherActivity,
                  controllers: controllers!,
                ),
                context.mediumGap,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // * Note Field
                    Expanded(
                      child: Align(
                        alignment: context.isEng
                            ? Alignment.centerLeft
                            : Alignment.centerRight,
                        child: ValueListenableBuilder(
                          valueListenable: controllers![ApiStrings.note]!,
                          builder: (context, noteValue, child) {
                            if (noteController.text.isNotEmpty ||
                                noteValue.toString().isNotEmpty) {
                              return BaseTextField(
                                controller: noteController,
                                hint: context.tr.note,
                                maxLines: 3,
                                onChanged: (newValue) {
                                  controllers![ApiStrings.note]!.value =
                                      newValue;
                                  controllers![ApiStrings.isUpdated]?.value =
                                      true;
                                },
                              );
                            } else {
                              return TextButton(
                                onPressed: () {
                                  controllers![ApiStrings.note]!.value = '.';
                                  controllers![ApiStrings.isUpdated]?.value =
                                      true;
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      context.tr.addNote,
                                      style: context.whiteHint.copyWith(
                                        color: ColorManager.primaryColor,
                                      ),
                                    ),
                                    context.xSmallGap,
                                    const Icon(
                                      Icons.add,
                                      color: ColorManager.primaryColor,
                                    ),
                                  ],
                                ),
                              );
                            }
                          },
                        ),
                      ),
                    ),

                    // * Media Field
                    ValueListenableBuilder(
                      valueListenable: controllers![ApiStrings.media]!,
                      builder: (context, mediaValue, child) {
                        return IconButton(
                          icon: Icon(mediaList.value.isEmpty
                              ? Icons.add_photo_alternate_outlined
                              : CupertinoIcons.photo_on_rectangle),
                          onPressed: () async {
                            controllers?[ApiStrings.isUpdated]?.value = true;

                            showModalBottomSheet(
                              context: context,
                              showDragHandle: true,
                              builder: (context) {
                                return TeacherActivityMediaWidget(
                                  mediaList: mediaList,
                                  controllers: controllers,
                                  onSavedMedia: onSavedMedia,
                                );
                              },
                            ).then(
                              (value) {
                                mediaController.clearFiles();
                              },
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
              ]
            ],
          ),
        ),
        BasePopupmenu(
          isPopUpMenuShow: true,
          deleteOnTap: () => showDialog(
              context: context,
              builder: (context) => BaseDeleteDialog(
                  isLoading: false,
                  description: context.tr.areYouSureToDeleteThisActivity,
                  onConfirm: () async {
                    await activityCtrl.deleteActivity(id: teacherActivity.id!);

                    teacherActivityList.value = teacherActivityList.value
                        .where((element) => element.id != teacherActivity.id)
                        .toList();

                    deletedActivityIds.value = {
                      ...deletedActivityIds.value,
                      teacherActivity.id!
                    };
                  })),
        ),
      ],
    );
  }
}
