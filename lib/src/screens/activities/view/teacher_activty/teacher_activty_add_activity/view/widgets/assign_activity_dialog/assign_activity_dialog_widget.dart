import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/Activities/models/activity_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';
import 'package:connectify_app/src/screens/Activities/controllers/activities_controller.dart';
import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/add_activity_dialog.dart';
import 'package:connectify_app/src/shared/widgets/base_popupmenu/base_popupmenu.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../activiy/view/widgets/activity_card.dart';

class AssignActivityDialogWidget extends HookConsumerWidget {
  final ValueNotifier<List<int>> activityIds;

  const AssignActivityDialogWidget({super.key, required this.activityIds});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activityCtrl = ref.watch(getActivitiesDataProvider(context));
    return activityCtrl.get(data: (activities) {
      if (activities.isEmpty) {
        return Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(
            child: Text(
              context.tr.noActivities,
              style: textTheme(context).headlineMedium,
            ),
          ),
        );
      }
      return ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) => AssignActivityCard(
                activity: activities[index],
                activityIds: activityIds,
              ),
          itemCount: activities.length);
    });
  }
}

class AssignActivityCard extends HookWidget {
  final ActivityModel activity;
  final ValueNotifier<List<int>> activityIds;

  const AssignActivityCard(
      {super.key, required this.activity, required this.activityIds});

  @override
  Widget build(BuildContext context) {
    final value = useState(false);

    Log.i('value =============${activityIds.value}');

    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppSpaces.smallPadding),
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      width: double.infinity,
      decoration: BoxDecoration(
          border: Border.all(color: ColorManager.grey),
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: ConstantsWidgets.boxShadowFromBottom),
      child: Row(
        children: [
          SizedBox(
            height: 50.h,
            width: 55.w,
            child: ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: BaseCachedImage(
                activity.image?.url ?? '',
                fit: BoxFit.cover,
                errorWidget: Image.asset(
                  Assets.imagesActivities,
                ),
              ),
            ),
          ),
          context.smallGap,
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.name,
                  style: context.blueHint,
                  maxLines: 1,
                ),
                context.xSmallGap,
                Text(
                  activity.description,
                  style: context.smallHint.copyWith(fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const Spacer(),
          Checkbox(
            value: value.value,
            onChanged: (val) {
              value.value = val!;

              if (!activityIds.value.contains(activity.id!)) {
                activityIds.value.add(activity.id!);
              } else {
                activityIds.value.remove(activity.id!);
              }
            },
          ),
        ],
      ),
    );
  }
}
