import 'dart:io';

import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../controllers/teacher_activities_controller.dart';

class TeacherActivityMediaWidget extends HookConsumerWidget {
  final ValueNotifier<List<BaseMediaModel>> mediaList;
  final Map<String, ValueNotifier<dynamic>>? controllers;
  final Function? onSavedMedia;

  const TeacherActivityMediaWidget({
    super.key,
    required this.mediaList,
    required this.controllers,
    this.onSavedMedia,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final networkApiServices = ref.watch(networkServiceProvider);

    final activityController =
        ref.watch(teacherActivityChangeNotifierProvider(context));

    final isLoading = useState(false);

    return StatefulBuilder(builder: (context, setState) {
      return Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Column(
          children: [
            if (mediaList.value.isEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 80),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      CupertinoIcons.photo_on_rectangle,
                      size: 100,
                    ),
                    context.mediumGap,
                    Text(
                      context.tr.noMedia,
                      style: context.subHeadLine,
                    ),
                  ],
                ),
              ),
            Expanded(
              child: ListView.separated(
                itemCount: mediaList.value.length,
                padding: EdgeInsets.only(bottom: 20),
                separatorBuilder: (context, index) => context.mediumGap,
                itemBuilder: (context, index) {
                  final mediaItem = mediaList.value[index];

                  final isNetworkImage =
                      mediaItem.url?.contains('http') ?? false;

                  return Row(
                    children: [
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                              AppRadius.baseContainerRadius),
                          child: SizedBox(
                            height: 120,
                            child: GestureDetector(
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) => Dialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          AppRadius.baseContainerRadius),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(
                                          AppRadius.baseContainerRadius),
                                      child: isNetworkImage
                                          ? Image.network(
                                              mediaItem.url ?? '',
                                              errorBuilder: (context, error,
                                                      stackTrace) =>
                                                  const BaseCachedImage(
                                                AppConsts.activityPlaceholder,
                                              ),
                                            )
                                          : Image.file(
                                              File(mediaItem.url ?? ''),
                                            ),
                                    ),
                                  ),
                                );
                              },
                              child: isNetworkImage
                                  ? Image.network(
                                      mediaItem.url ?? '',
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              const BaseCachedImage(
                                        AppConsts.activityPlaceholder,
                                      ),
                                    )
                                  : Image.file(
                                      File(mediaItem.url ?? ''),
                                      fit: BoxFit.cover,
                                    ),
                            ),
                          ),
                        ),
                      ),
                      context.mediumGap,
                      IconButton(
                        icon: const CircleAvatar(
                          backgroundColor: ColorManager.errorColor,
                          child: Icon(
                            Icons.delete,
                            color: ColorManager.white,
                          ),
                        ),
                        onPressed: () async {
                          controllers?[ApiStrings.isUpdated]?.value = true;
                          networkApiServices
                              .deleteImage(mediaItem.id?.toString() ?? '');

                          mediaList.value.removeAt(index);

                          controllers![ApiStrings.media]!.value =
                              mediaList.value;

                          setState(() {});
                        },
                      ),
                    ],
                  );
                },
              ),
            ),

            // * Add Media Button
            Button(
              isLoading: activityController.isLoading || isLoading.value,
              loadingWidget: const Center(child: LinearProgressIndicator()),
              onPressed: () async {
                controllers?[ApiStrings.isUpdated]?.value = true;

                isLoading.value = true;

                final result =
                    await mediaController.pickFile(allowMultiple: true);

                if (result != null) {
                  final files = result.files;

                  final uploadedMedia = await networkApiServices.uploadFiles(
                    filePaths: files.map((e) => e.path).toList(),
                  );

                  Log.w('Uploaded_Media $uploadedMedia');

                  final uploadedFiles = uploadedMedia
                          ?.map(
                            (e) => BaseMediaModel(
                              id: e['id'],
                              url: e['url'],
                            ),
                          )
                          .toList() ??
                      <BaseMediaModel>[];

                  controllers![ApiStrings.media]!.value.addAll(uploadedFiles);

                  mediaList.value = controllers![ApiStrings.media]!.value;

                  if (onSavedMedia != null) {
                    onSavedMedia!();
                  }

                  isLoading.value = false;
                }
              },
              label: context.tr.addMedia,
            ),

            context.mediumGap,

            if (!activityController.isLoading && !isLoading.value)
              // * Cancel Button
              Button(
                color: ColorManager.errorColor,
                onPressed: () {
                  Navigator.of(context).pop();
                },
                label: context.tr.cancel,
              ),
          ],
        ),
      );
    });
  }
}
