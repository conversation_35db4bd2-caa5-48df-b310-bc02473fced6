import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/teacher_activity_screen.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/assign_activity_dialog/assign_activity_dialog_widget.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../controllers/teacher_activities_controller.dart';

class AssignActivityDialog extends StatelessWidget {
  const AssignActivityDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AddRectangleWidget(
        title: context.tr.assignActivityToClass,
        onTap: () {
          showChooseActivityAssignTypeDialog(context);
        });
  }
}

Future<void> showChooseActivityAssignTypeDialog(
  context,
) {
  return showDialog(
    context: context,
    builder: (context) {
      return AlertDialogWidget(
        header: context.tr.chooseActivityAssignType,
        isLoading: false,
        isImage: false,
        child: Column(
          children: [
            context.mediumGap,
            Button(
              label: context.tr.singleActivity,
              color: ColorManager.primaryColor,
              onPressed: () {
                context.back();
                showAssignActivityDialog(context,
                    isWeekly: false,
                    navigateWidget: const TeacherActivitiesScreen());
              },
            ),
            context.mediumGap,
            Button(
              label: context.tr.weeklyActivity,
              onPressed: () {
                context.back();
                showAssignActivityDialog(context,
                    navigateWidget: const TeacherActivitiesScreen());
              },
            ),
          ],
        ),
      );
    },
  );
}

Future<void> showAssignActivityDialog(
  context, {
  required Widget navigateWidget,
  bool isWeekly = true,
}) {
  return showDialog(
    context: context,
    builder: (context) {
      final now = DateTime.now();
      final days = List.generate(7, (index) {
        final date = now.add(Duration(days: index));
        return DateFormat('EEEE', 'en').format(date);
      });

      return HookConsumer(
        builder: (context, ref, child) {
          final formKey = useState(GlobalKey<FormState>());
          final teacherActivityController =
              ref.watch(teacherActivityChangeNotifierProvider(context));
          final activityIds = useState<List<int>>([]);
          final selectedDay = useState<String>(days[0]);
          final selectedDate = useState<DateTime>(DateTime.now());

          return AlertDialogWidget(
            header: context.tr.assignActivityToClass,
            isLoading: teacherActivityController.isLoading,
            isImage: false,
            child: Form(
              key: formKey.value,
              child: Column(
                children: [
                  AssignActivityDialogWidget(
                    activityIds: activityIds,
                  ),
                  context.mediumGap,
                  if (isWeekly)
                    BaseDropDown(
                      icon: const Icon(Icons.calendar_today),
                      label: context.tr.day,
                      selectedValue: selectedDay.value,
                      data: days,
                      onChanged: (value) {
                        selectedDay.value = value;
                      },
                    )
                  else
                    BaseDatePicker(
                      isRequired: true,
                      label: context.tr.date,
                      selectedDateNotifier: selectedDate,
                      shouldBeFutureDate: true,
                    ),
                ],
              ),
            ),
            onConfirm: () async {
              await Future.forEach(activityIds.value, (id) async {
                if (isWeekly) {
                  await teacherActivityController.addTeacherActivity(
                    id: id,
                    day: selectedDay.value,
                    isWeekly: true,
                  );
                } else {
                  await teacherActivityController.addTeacherActivity(
                    id: id,
                    day: selectedDate.value.formatToDay,
                    date: selectedDate.value.formatDateToString,
                    isWeekly: false,
                  );
                }
              });

              context.back();
              context.to(const TeacherActivitiesScreen());
              context.showBarMessage(context.tr.addedSuccessfully);
            },
          );
        },
      );
    },
  );
}
