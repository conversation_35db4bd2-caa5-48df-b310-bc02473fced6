import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../sleep/view/widgets/add_sleep_widgets.dart';

class EditTeacherActivityFields extends HookConsumerWidget {
  final TeacherActivityModel? teacherActivity;
  final Map<String, ValueNotifier> controllers;

  const EditTeacherActivityFields({
    super.key,
    required this.teacherActivity,
    required this.controllers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final day = useState(teacherActivity?.day);

    return _WeekDaysAndStartTimeAndEndTime(
      day: day,
      teacherActivity: teacherActivity!,
      controllers: controllers,
    );
  }
}

class _WeekDaysAndStartTimeAndEndTime extends HookConsumerWidget {
  final TeacherActivityModel teacherActivity;
  final ValueNotifier<String?> day;
  final Map<String, ValueNotifier> controllers;

  const _WeekDaysAndStartTimeAndEndTime({
    required this.teacherActivity,
    required this.controllers,
    required this.day,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useState(GlobalKey<FormState>());

    final fromController = useMemoized(
      () => TextEditingController(text: teacherActivity.startTime),
      [teacherActivity.id],
    );

    final toController = useMemoized(
      () => TextEditingController(text: teacherActivity.endTime),
      [teacherActivity.id],
    );

    useEffect(() {
      if (teacherActivity.startTime.isNotEmpty && fromController.text.isEmpty) {
        fromController.text = teacherActivity.startTime;
        controllers[ApiStrings.from]?.value = teacherActivity.startTime;
      }
      if (teacherActivity.endTime.isNotEmpty && toController.text.isEmpty) {
        toController.text = teacherActivity.endTime;
        controllers[ApiStrings.to]?.value = teacherActivity.endTime;
      }
      return () {};
    }, [teacherActivity]);

    return Form(
      key: formKey.value,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    final data = await showTimePickerDialog(context);

                    controllers[ApiStrings.isUpdated]?.value = true;

                    if (data == null) {
                      fromController.text = teacherActivity.startTime;
                      controllers[ApiStrings.from]?.value =
                          teacherActivity.startTime;
                    } else {
                      final isInitialFromHourAndToHour =
                          toController.text == '00:00' &&
                              fromController.text == '00:00';

                      final parsedFromHour = int.parse(data.split(":")[0]);
                      final parsedFromMinutes = int.parse(data.split(":")[1]);

                      final parsedToHour = int.parse(
                        toController.text.split(":")[0],
                      );
                      final parsedToMinutes = int.parse(
                        toController.text.split(":")[1],
                      );

                      if (isInitialFromHourAndToHour) {
                        fromController.text = data;
                        controllers[ApiStrings.from]?.value = data;
                        return;
                      }

                      if (parsedToHour < parsedFromHour ||
                          (parsedToHour == parsedFromHour &&
                              parsedToMinutes < parsedFromMinutes)) {
                        context.showBarMessage(
                            context.tr.pleaseEnterAValidFromToTime,
                            isError: true);

                        return;
                      }

                      fromController.text = data;
                      controllers[ApiStrings.from]?.value = data;
                    }
                  },
                  child: BaseTextField(
                    title: context.tr.from,
                    controller: fromController,
                    enabled: false,
                    hint: teacherActivity.startTime,
                  ),
                ),
              ),
              context.smallGap,
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    final data = await showTimePickerDialog(context);
                    controllers[ApiStrings.isUpdated]?.value = true;

                    if (data == null) {
                      toController.text = teacherActivity.endTime;
                      controllers[ApiStrings.to]?.value =
                          teacherActivity.endTime;
                    } else {
                      final parsedToHour = int.parse(data.split(":")[0]);
                      final parsedToMinutes = int.parse(data.split(":")[1]);

                      final parsedFromHour = int.parse(
                        fromController.text.split(":")[0] ?? "0",
                      );
                      final parsedFromMinutes = int.parse(
                        fromController.text.split(":")[1] ?? "0",
                      );

                      if (parsedToHour < parsedFromHour ||
                          (parsedToHour == parsedFromHour &&
                              parsedToMinutes < parsedFromMinutes)) {
                        context.showBarMessage(
                            context.tr.pleaseEnterAValidFromToTime,
                            isError: true);

                        return;
                      }

                      toController.text = data;
                      controllers[ApiStrings.to]?.value = data;
                    }
                  },
                  child: BaseTextField(
                    title: context.tr.to,
                    controller: toController,
                    enabled: false,
                    hint: teacherActivity.endTime,
                  ),
                ),
              ),
            ],
          ),
        ],
      ).paddingAll(AppSpaces.mediumPadding),
    );
  }
}
