import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class TeacherActivityViewCard extends StatelessWidget {
  final TeacherActivityModel teacherActivity;

  const TeacherActivityViewCard({super.key, required this.teacherActivity});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpaces.smallPadding),
          margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
          width: double.infinity,
          decoration: BoxDecoration(
              border: Border.all(color: ColorManager.grey),
              color: ColorManager.white,
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              boxShadow: ConstantsWidgets.boxShadowFromBottom),
          child: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    height: 50.h,
                    width: 55.w,
                    child: ClipRRect(
                      borderRadius:
                          BorderRadius.circular(AppRadius.baseContainerRadius),
                      child: BaseCachedImage(
                          teacherActivity.activity?.image?.url ?? '',
                          errorWidget: const BaseCachedImage(
                            AppConsts.activityPlaceholder,
                          )),
                    ),
                  ),
                  context.smallGap,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        teacherActivity.activity?.name ?? '',
                        style: context.blueHint,
                        maxLines: 1,
                      ),
                      context.xSmallGap,
                      Text(
                        teacherActivity.activity?.description ?? '',
                        style: context.smallHint.copyWith(fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ).sized(width: context.width * .7),
                ],
              ),
              context.mediumGap,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: TextWithWidget(
                      title: context.tr.from,
                      child: Text(
                        teacherActivity.startTime,
                        style: context.subHeadLine
                            .copyWith(color: ColorManager.primaryColor),
                      ),
                    ),
                  ),
                  Expanded(
                    child: TextWithWidget(
                      title: context.tr.to,
                      child: Text(
                        teacherActivity.endTime,
                        style: context.subHeadLine
                            .copyWith(color: ColorManager.primaryColor),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}

class TextWithWidget extends StatelessWidget {
  final String title;
  final Widget child;

  const TextWithWidget({
    super.key,
    required this.title,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          '$title : ',
          style: context.title,
        ),
        context.smallGap,
        Expanded(child: child)
      ],
    );
  }
}
