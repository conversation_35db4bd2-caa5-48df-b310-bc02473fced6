import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_view/view/widgets/teacher_activity_view_card.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/date_attendance_filter_widget.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../shared/consts/app_constants.dart';
import '../../../../../controllers/teacher_activities_controller.dart';
import '../../../../activiy/view/widgets/activity_card.dart';

class TeacherActivityViewList extends HookConsumerWidget {
  const TeacherActivityViewList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getTeacherActivityCtrl =
        ref.watch(getTeacherActivitiesDataProvider(context));
    final teacherActivityCtrl =
        ref.watch(teacherActivityProviderController(context));

    return getTeacherActivityCtrl.get(
      data: (teacherActivity) {
        if (teacherActivity.isEmpty) {
          return Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: Center(
              child: Text(
                context.tr.noActivities,
                style: textTheme(context).headlineMedium,
              ),
            ),
          );
        }
        return HookBuilder(builder: (context) {
          final selectedIndex = useState(0);

          final filterList = useState<List<TeacherActivityModel>>(
              teacherActivity
                  .where((element) => element.day == weekDays[0])
                  .toList());

          useEffect(() {
            filterList.value = teacherActivity
                .where(
                    (element) => element.day == weekDays[selectedIndex.value])
                .toList();

            Log.w(
                'SSSSS ${weekDays[selectedIndex.value]} List ${filterList.value}');

            return () {};
          }, [selectedIndex.value]);
          // Log.w(selectedIndex.value);
          // Log.w(weekDays[selectedIndex.value]);
          // Log.w(filterList.value);

          final isEmptyList = filterList.value.isEmpty;

          return Column(
            children: [
              DateAttendanceFilterWidget(
                selectedDay: weekDays[selectedIndex.value],
                onNext: () =>
                    teacherActivityCtrl.onNext(selectedIndex: selectedIndex),
                onPrevious: () =>
                    teacherActivityCtrl.onPrev(selectedIndex: selectedIndex),
              ),
              if (isEmptyList) ...[
                Column(
                  children: [
                    context.xxLargeGap,
                    Text(
                      context.tr.noActivities,
                      style: context.headLine,
                    ),
                  ],
                ),
              ] else ...[
                ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemBuilder: (context, index) => WidgetAnimator(
                        delay: Duration(
                            milliseconds: AppConsts.animatedDuration * index),
                        child: TeacherActivityViewCard(
                          teacherActivity: filterList.value[index],
                        )),
                    itemCount: filterList.value.length),
              ]
            ],
          );
        });
      },
    );
  }
}
