import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';

List<ActivityModel> responseToActivityModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final activities = data.map((e) => ActivityModel.fromJson(e)).toList();

  return activities;
}

class ActivityModel extends BaseModel {
  const ActivityModel({
    super.id,
    super.name,
    super.description,
    super.image,
    super.createdAt,
  });

  //? From Json
  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final image = attributes.containsKey(ApiStrings.image) &&
            attributes[ApiStrings.image][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.image][ApiStrings.data]
            [ApiStrings.attributes])
        : null;

    return ActivityModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name] ?? '',
      description: attributes[ApiStrings.description] ?? '',
      image: image,
      createdAt: attributes[ApiStrings.createdAt],
    );
  }

  //? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.description: description,
    };
  }
}
