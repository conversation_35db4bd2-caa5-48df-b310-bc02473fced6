import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/supplies/repo/supply_repo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/supply_model.dart';
import '../view/admin_supplies/supplies_screen.dart';

final supplyControllerProvider =
    Provider.family<SupplyController, BuildContext>((ref, context) {
  final supplyRepo = ref.watch(supplyRepoProvider);

  return SupplyController(supplyRepo: supplyRepo, context: context);
});

final supplyControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<SupplyController, BuildContext>(
        (ref, context) {
  final supplyRepo = ref.watch(supplyRepoProvider);

  return SupplyController(supplyRepo: supplyRepo, context: context);
});

final getSupplyDataProviderWithPagination =
    FutureProvider.family<List<SupplyModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;
    final supplyCtrl = ref.watch(supplyControllerProvider(context));

    return await supplyCtrl.getSuppliesPaginated(page: page);
  },
);

final getSupplyDataProvider =
    FutureProvider.family<List<SupplyModel>, BuildContext>(
        (ref, context) async {
  final supplyController = ref.watch(supplyControllerProvider(context));

  return await supplyController.getSuppliesData();
});

class SupplyController extends BaseVM {
  final SupplyRepo supplyRepo;
  final BuildContext context;

  SupplyController({required this.supplyRepo, required this.context});

  Future<List<SupplyModel>> getSuppliesData() async {
    return await baseFunction(context, () async {
      return await supplyRepo.getSuppliesData();
    });
  }

  Future<void> addSupply(
      {required TextEditingController supplyController}) async {
    return await baseFunction(context, () async {
      final supply = SupplyModel(name: supplyController.text);

      await supplyRepo.addSupply(supply: supply);

      NotificationService.sendNotification(
        title: "Supplies Update",
        body:
            "New supply named ${supply.name} has been added to the request list.",
        userTokenOrTopic: NurseryModelHelper.allTeacherTopic(),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Supplies Update",
        body: "New supplies have been added to the request list.",
        topic: NurseryModelHelper.allTeacherTopic(),
      ));

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const SuppliesScreen());
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  Future<List<SupplyModel>> getSuppliesPaginated({int page = 1}) async {
    return await baseFunction(context, () async {
      return await supplyRepo.getSuppliesPaginated(page: page);
    });
  }

  Future<void> editSupply(
      {required TextEditingController supplyController,
      required int id}) async {
    return await baseFunction(context, () async {
      final supply = SupplyModel(name: supplyController.text, id: id);

      await supplyRepo.editSupply(supply: supply);
      if (!context.mounted) return;
      context.back();
      context.toReplacement(const SuppliesScreen());
      context.showBarMessage(context.tr.editSuccessfully);
    });
  }

  Future<void> deleteSupply({required int id}) async {
    return await baseFunction(context, () async {
      await supplyRepo.deleteSupply(id: id);

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const SuppliesScreen());
      context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
    });
  }
}
