import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/supply_model.dart';
import '../repo/teacher_supply_repo.dart';

final teacherSupplyControllerProvider =
    Provider.family<TeacherSupplyController, BuildContext>((ref, context) {
  final teacherSupplyRepo = ref.watch(teacherSupplyRepoProvider);

  return TeacherSupplyController(
      teacherSupplyRepo: teacherSupplyRepo, context: context);
});

final teacherSupplyControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherSupplyController, BuildContext>(
        (ref, context) {
  final teacherSupplyRepo = ref.watch(teacherSupplyRepoProvider);

  return TeacherSupplyController(
      teacherSupplyRepo: teacherSupplyRepo, context: context);
});

class TeacherSupplyController extends BaseVM {
  final TeacherSupplyRepo teacherSupplyRepo;
  final BuildContext context;

  TeacherSupplyController(
      {required this.teacherSupplyRepo, required this.context});

  Future<void> addTeacherSupply(
      {required List<SupplyModel>? supplies,
      required StudentModel student}) async {
    return await baseFunction(context, () async {
      final teacherSupply =
          TeacherSupplyModel(student: student, supplies: supplies);

      await teacherSupplyRepo.addSupply(teacherSupply: teacherSupply);

      NotificationService.sendNotification(
        title: "Supply Request Sent",
        body: "New request for supplies has been submitted.",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Supply Request Sent",
        body: "New request for supplies has been submitted.",
        topic: NurseryModelHelper.parentByStudentTopic(student.id),
      ));
    });
  }
}
