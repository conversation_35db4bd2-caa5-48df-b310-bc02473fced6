import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

final teacherSupplyRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return TeacherSupplyRepo(networkApiServices);
});

class TeacherSupplyRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  TeacherSupplyRepo(this._networkApiServices);

  Future<void> addSupply({required TeacherSupplyModel teacherSupply}) async {
    return await baseFunction(() async {
      await _networkApiServices.postResponse(
          ApiEndpoints.editDeleteTeacherSupply,
          body: teacherSupply.toJson());

      for (var supply in teacherSupply.supplies ?? <SupplyModel>[]) {
        addNewHistory(
            historyModel: HistoryModel(
          historyType: HistoryType.supply,
          supply: supply,
          student: teacherSupply.student,
        ));
      }
    });
  }
}
