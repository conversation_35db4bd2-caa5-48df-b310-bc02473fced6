import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/setup_activites_fields.dart';
import 'package:connectify_app/src/screens/supplies/controller/supply_controller.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/supplies_screen.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../model/supply_model.dart';


class AddSupplyDialog extends HookConsumerWidget {
  final Widget navigateWidget;

  const AddSupplyDialog({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    void clearData() {
      ref.watch(mediaPickerControllerProvider).clearFiles();
    }

    return AddRectangleWidget(
        title: context.tr.createANewSupply,
        onTap: () {
          showAddEditSupplyDialog(
            context,
          ).then((value) => clearData());
        });
  }
}

Future<void> showAddEditSupplyDialog(
  context, {
  SupplyModel? supply,
}) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final supplyController = useTextEditingController(text: supply?.name);

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          //!-----------------------------------------------------

          final supplyCtrl =
              ref.watch(supplyControllerChangeNotifierProvider(context));

          //!-----------------------------------------------------

          final isEdit = supply != null;

          Future<void> addEditSupply() async {
            if (isEdit) {
              await supplyCtrl.editSupply(
                  supplyController: supplyController, id: supply.id!);
            } else {
              await supplyCtrl.addSupply(supplyController: supplyController);
            }
          }

          return AlertDialogWidget(
            header: context.tr.createANewSupply,
            isLoading: supplyCtrl.isLoading,
            isImage: false,
            child: Form(
              key: formKey.value,
              child: BaseTextField(
                title: context.tr.supplyName,
                controller: supplyController,
                textInputType: TextInputType.text,
              ),
            ),
            onConfirm: () async {
              if (!formKey.value.currentState!.validate()) return;

              await addEditSupply();
            },
          );
        },
      );
    },
  );
}
