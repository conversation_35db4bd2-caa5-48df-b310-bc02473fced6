import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/base_popupmenu/base_popupmenu.dart';
import '../../../../../shared/widgets/dialogs/base_delete_dialog.dart';
import '../../../controller/supply_controller.dart';
import 'add_supply_dialog.dart';

class SupplyCard extends ConsumerWidget {
  final SupplyModel supply;

  const SupplyCard({super.key, required this.supply});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final supplyCtrl =
        ref.watch(supplyControllerChangeNotifierProvider(context));
    return BaseContainer(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(child: Text(supply.name, style: context.title)),
          // const Spacer(),
          BasePopupmenu(
            editOnTap: () async {
              showAddEditSupplyDialog(context, supply: supply);
            },
            deleteOnTap: () => showDialog(
                context: context,
                builder: (context) => BaseDeleteDialog(
                    description: context.tr.areYouSureToDeleteThisSupply,
                    onConfirm: () async {
                      await supplyCtrl.deleteSupply(id: supply.id!);
                    })),
          )
        ],
      ),
    );
  }
}
