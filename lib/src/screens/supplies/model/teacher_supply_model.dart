import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../nursery/models/nursery_model_helper.dart';

class TeacherSupplyModel extends BaseModel {
  final List<SupplyModel>? supplies;
  final StudentModel? student;
  final TeacherModel? teacher;
  final NurseryModel? nursery;

  const TeacherSupplyModel({
    this.supplies,
    this.student,
    this.teacher,
    this.nursery,
  });

  // factory TeacherSupplyModel.fromJson(Map<String, dynamic> json) {
  //   final attributes = json[ApiStrings.attributes];
  //   return TeacherSupplyModel(
  //     id: json[ApiStrings.id],
  //     name: attributes[ApiStrings.name],
  //   );
  // }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.supplies: supplies?.map((e) => e.id).toList(),
      ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
    };
  }
}
