import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

class SupplyModel extends BaseModel {
  const SupplyModel({
    super.id,
    super.name,
  });

  factory SupplyModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return SupplyModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if(id !=null)
      ApiStrings.id: id,
      ApiStrings.name: name,
    };
  }
}
