import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/activities/controllers/teacher_activities_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/date_attendance_filter_widget.dart';
import 'package:connectify_app/src/screens/history/controller/history_controller.dart';
import 'package:connectify_app/src/screens/history/view/widgets/history_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/history_model.dart';

class HistoryScreen extends HookConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState(DateTime.now());
    final dayFilter = selectedDate.value.formatToDay;
    final selectedClass = useState<ClassModel?>(null);

    // Fetch history data
    final historyData = ref.watch(
        getAllHistoryData((context, selectedDate.value.formatDateToString)));

    // Fetch teacher activities
    final teacherActivities = ref.watch(
      getTeacherActivitiesByDateProviderByClassId(
        (
          context,
          selectedDate.value.formatDateToString,
          dayFilter,
          selectedClass.value?.id
        ),
      ),
    );

    final histories = historyData.when(
      data: (data) => data,
      loading: () => const <HistoryModel>[],
      error: (error, stack) => <HistoryModel>[],
    );

    final activities = teacherActivities.when(
      data: (data) => data,
      loading: () => const <TeacherActivityModel>[],
      error: (error, stack) => const <TeacherActivityModel>[],
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr.history),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Column(
          children: [
            // Date filter widget
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: DateAttendanceFilterWidget(
                selectedDay: selectedDate.value.formatToDay,
                date: selectedDate.value.formatDateToString,
                onNext: () {
                  selectedDate.value =
                      selectedDate.value.add(const Duration(days: 1));
                },
                onPrevious: () {
                  selectedDate.value =
                      selectedDate.value.subtract(const Duration(days: 1));
                },
              ),
            ),

            // Class filter dropdown
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: ClassDropDown(
                selectedClass: selectedClass,
                selectFirstClass: !const UserModel().isAdmin,
                classesData: const UserModel().isAdmin
                    ? []
                    : const UserModel().currentUser.classes ?? [],
              ),
            ),

            // Combined list
            Expanded(
              child: Builder(
                builder: (context) {
                  if (historyData.isLoading || teacherActivities.isLoading) {
                    return const Center(
                      child: LoadingWidget(),
                    );
                  }

                  // Filter histories by class if a class is selected
                  final filteredHistories = selectedClass.value != null
                      ? histories.where((history) {
                          // Filter by class ID if student has a class
                          if (history.student?.classModel?.id != null) {
                            return history.student!.classModel!.id ==
                                selectedClass.value!.id;
                          }

                          if (history.activity?.classModel?.id != null) {
                            return history.activity!.classModel!.id ==
                                selectedClass.value!.id;
                          }
                          return false;
                        }).toList()
                      : histories;

                  // Filter activities by class if a class is selected
                  final filteredActivities = selectedClass.value != null
                      ? activities.where((activity) {
                          return activity.classModel?.id ==
                              selectedClass.value!.id;
                        }).toList()
                      : activities;

                  // Combine and sort lists
                  final combinedList = [
                    ...filteredHistories.map((history) => {
                          'type': 'history',
                          'data': history,
                          'time': DateTime.parse(
                            '${selectedDate.value.formatDateToString} ${history.createdAt?.formatDateToTime}',
                          ),
                        }),
                    ...filteredActivities.map((activity) => {
                          'type': 'activity',
                          'data': activity,
                          'time': DateTime.parse(
                            '${selectedDate.value.formatDateToString} ${activity.startTime.split(':').map((e) => e.padLeft(2, '0')).join(':')}',
                          ).toUtc().toLocal(),
                        }),
                  ]..sort((a, b) => (a['time'] as DateTime)
                      .compareTo(b['time']! as DateTime));

                  if (combinedList.isEmpty) {
                    return Center(
                      child: Text(
                        context.tr.noHistoryForThisDate,
                        style: context.subHeadLine,
                      ),
                    );
                  }

                  return BaseList(
                    data: combinedList,
                    separatorGap: context.mediumGap,
                    itemBuilder: (context, index) {
                      final item = combinedList[index];
                      if (item['type'] == 'history') {
                        return HistoryCard(
                          history: item['data'] as HistoryModel,
                          date: selectedDate.value.formatDateToString,
                        );
                      } else if (item['type'] == 'activity') {
                        final activity = item['data'] as TeacherActivityModel?;

                        return HistoryCard(
                          history: HistoryModel(
                            createdAt: item['time'] as DateTime,
                            historyType: HistoryType.activity,
                            activity: activity,
                          ),
                          date: selectedDate.value.formatDateToString,
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
// import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/date_attendance_filter_widget.dart';
// import 'package:connectify_app/src/screens/history/controller/history_controller.dart';
// import 'package:connectify_app/src/screens/history/view/widgets/history_card.dart';
// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
// import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class HistoryScreen extends HookConsumerWidget {
//   const HistoryScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final selectedDate = useState(DateTime.now());
//     final dayFilter = selectedDate.value.formatToDay;
//
//     final historyData = ref.watch(
//         getAllHistoryData((context, selectedDate.value.formatDateToString)));
//
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(context.tr.history),
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//         child: Column(
//           children: [
//             // Date filter widget
//             Padding(
//               padding: const EdgeInsets.symmetric(vertical: 16),
//               child: DateAttendanceFilterWidget(
//                 selectedDay: selectedDate.value.formatToDay,
//                 date: selectedDate.value.formatDateToString,
//                 onNext: () {
//                   selectedDate.value =
//                       selectedDate.value.add(const Duration(days: 1));
//                 },
//                 onPrevious: () {
//                   selectedDate.value =
//                       selectedDate.value.subtract(const Duration(days: 1));
//                 },
//               ),
//             ),
//
//             // History list
//             Expanded(
//               child: historyData.get(
//                 data: (histories) {
//                   if (histories.isEmpty) {
//                     return Center(
//                       child: Text(
//                         context.tr.noHistoryForThisDate,
//                         style: context.subHeadLine,
//                       ),
//                     );
//                   }
//
//                   return BaseList(
//                     data: histories,
//                     separatorGap: context.mediumGap,
//                     itemBuilder: (context, index) {
//                       return HistoryCard(
//                         history: histories[index],
//                         date: selectedDate.value.formatDateToString,
//                       );
//                     },
//                   );
//                 },
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
