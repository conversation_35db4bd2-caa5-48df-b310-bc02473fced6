import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/history/model/history_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/consts/app_constants.dart';
import '../../../../shared/widgets/base_list/base_list.dart';

class HistoryCard extends StatelessWidget {
  final HistoryModel history;
  final String date;

  const HistoryCard({
    super.key,
    required this.history,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time created
            Row(
              children: [
                Row(
                  children: [
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      history.createdAt?.formatDateToTime ?? '',
                      style: context.greyLabelLarge.copyWith(fontSize: 17),
                    ),
                  ],
                ),

                const SizedBox(width: 8),

                // History type and relations
                Text(
                  '-  ${_getHistoryTypeTitle(context)}',
                  style: context.subTitle.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),

            const Divider(
              height: 16,
              thickness: 1,
              color: Colors.grey,
            ),

            const SizedBox(height: 8),

            if (history.student != null)
              _buildInfoRow(context, Icons.person,
                  '${context.tr.student}: ${history.student?.name ?? ''}'),

            // Show specific details based on history type
            ..._buildHistoryDetails(
              context,
            ),
          ],
        ),
      ),
    );
  }

  String _getHistoryTypeTitle(BuildContext context) {
    switch (history.historyType) {
      case HistoryType.activity:
        return context.tr.activity;
      case HistoryType.sleep:
        return context.tr.sleep;
      case HistoryType.food:
        return context.tr.food;
      case HistoryType.supply:
        return context.tr.supply;
      case HistoryType.toilet:
        return context.tr.toilet;
    }
  }

  List<Widget> _buildHistoryDetails(BuildContext context) {
    switch (history.historyType) {
      case HistoryType.activity:
        return [
          if (history.activity != null)
            Builder(builder: (context) {
              final dayNote = history.activity?.notes.lastWhereOrNull(
                (element) => element.date == date,
              );

              return Column(
                children: [
                  _buildInfoRow(context, Icons.directions_run,
                      '${context.tr.activity}: ${history.activity?.activity?.name ?? ''}'),
                  if (dayNote?.note != null)
                    _buildInfoRow(context, Icons.note_alt_sharp,
                        '${context.tr.note}: ${dayNote?.note ?? ''}'),
                  if (dayNote?.media != null && dayNote!.media.isNotEmpty)
                    BaseList.horizontal(
                      height: 100,
                      data: dayNote.media,
                      separatorGap: context.mediumGap,
                      itemBuilder: (media, index) {
                        return GestureDetector(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) => Dialog(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      AppRadius.baseContainerRadius),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                      AppRadius.baseContainerRadius),
                                  child: Image.network(
                                    media.url ?? '',
                                    errorBuilder:
                                        (context, error, stackTrace) =>
                                            const BaseCachedImage(
                                      AppConsts.activityPlaceholder,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                          child: BaseCachedImage(
                            media.url ?? '',
                            radius: 8,
                            width: 100,
                            height: 50,
                            fit: BoxFit.cover,
                          ),
                        );
                      },
                    ),
                ],
              );
            }),
        ];

      case HistoryType.sleep:
        return [
          if (history.sleep != null)
            _buildInfoRow(context, Icons.nightlight_round,
                '${context.tr.sleep}: (${context.tr.from}: ${history.sleep?.sleepStartTime ?? ''}) - (${context.tr.to}: ${history.sleep?.sleepEndTime ?? ''})'),
        ];

      case HistoryType.food:
        return [
          if (history.food != null)
            _buildInfoRow(context, Icons.fastfood,
                '${context.tr.food}: ${history.food?.getMealType() ?? ''} - ${context.tr.amount}: ${history.food?.getMealAmount() ?? ''}'),
        ];

      case HistoryType.supply:
        return [
          if (history.supply != null)
            _buildInfoRow(context, Icons.inventory,
                '${context.tr.supply}: ${history.supply?.name ?? ''}'),
        ];

      case HistoryType.toilet:
        return [
          if (history.toilet != null)
            _buildInfoRow(context, Icons.wc,
                '${context.tr.description}: ${history.toilet?.toiletType?.name ?? ''} ${history.toilet?.toiletWay?.name ?? ''}'),
        ];
    }
  }

  Widget _buildInfoRow(BuildContext context, IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[700]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }
}
// import 'package:collection/collection.dart';
// import 'package:connectify_app/src/screens/history/model/history_model.dart';
// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:connectify_app/src/shared/widgets/base_container.dart';
// import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../../shared/consts/app_constants.dart';
// import '../../../../shared/widgets/base_list/base_list.dart';
//
// class HistoryCard extends StatelessWidget {
//   final HistoryModel history;
//   final String date;
//
//   const HistoryCard({
//     super.key,
//     required this.history,
//     required this.date,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return BaseContainer(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Time created
//             Row(
//               children: [
//                 Row(
//                   children: [
//                     Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
//                     const SizedBox(width: 4),
//                     Text(
//                       history.createdAt?.formatDateToTime ?? '',
//                       style: context.greyLabelLarge.copyWith(fontSize: 17),
//                     ),
//                   ],
//                 ),
//
//                 const SizedBox(width: 8),
//
//                 // History type and relations
//                 Text(
//                   '-  ${_getHistoryTypeTitle(context)}',
//                   style: context.subTitle.copyWith(
//                     fontWeight: FontWeight.bold,
//                     fontSize: 18,
//                   ),
//                 ),
//               ],
//             ),
//
//             const Divider(
//               height: 16,
//               thickness: 1,
//               color: Colors.grey,
//             ),
//
//             const SizedBox(height: 8),
//
//             if (history.student != null)
//               _buildInfoRow(context, Icons.person,
//                   '${context.tr.student}: ${history.student?.name ?? ''}'),
//
//             // Show specific details based on history type
//             ..._buildHistoryDetails(
//               context,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   String _getHistoryTypeTitle(BuildContext context) {
//     switch (history.historyType) {
//       case HistoryType.activity:
//         return context.tr.activity;
//       case HistoryType.sleep:
//         return context.tr.sleep;
//       case HistoryType.food:
//         return context.tr.food;
//       case HistoryType.supply:
//         return context.tr.supply;
//       case HistoryType.toilet:
//         return context.tr.toilet;
//     }
//   }
//
//   List<Widget> _buildHistoryDetails(BuildContext context) {
//     switch (history.historyType) {
//       case HistoryType.activity:
//         return [
//           if (history.activity != null)
//             Builder(builder: (context) {
//               final dayNote = history.activity?.notes.lastWhereOrNull(
//                 (element) => element.date == date,
//               );
//
//               return Column(
//                 children: [
//                   _buildInfoRow(context, Icons.directions_run,
//                       '${context.tr.activity}: ${history.activity?.activity?.name ?? ''}'),
//                   if (dayNote?.note != null)
//                     _buildInfoRow(context, Icons.note_alt_sharp,
//                         '${context.tr.note}: ${dayNote?.note ?? ''}'),
//                   if (dayNote?.media != null && dayNote!.media.isNotEmpty)
//                     BaseList.horizontal(
//                       height: 100,
//                       data: dayNote.media,
//                       separatorGap: context.mediumGap,
//                       itemBuilder: (media, index) {
//                         return GestureDetector(
//                           onTap: () {
//                             showDialog(
//                               context: context,
//                               builder: (context) => Dialog(
//                                 shape: RoundedRectangleBorder(
//                                   borderRadius: BorderRadius.circular(
//                                       AppRadius.baseContainerRadius),
//                                 ),
//                                 child: ClipRRect(
//                                   borderRadius: BorderRadius.circular(
//                                       AppRadius.baseContainerRadius),
//                                   child: Image.network(
//                                     media.url ?? '',
//                                     errorBuilder:
//                                         (context, error, stackTrace) =>
//                                             const BaseCachedImage(
//                                       AppConsts.activityPlaceholder,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             );
//                           },
//                           child: BaseCachedImage(
//                             media.url ?? '',
//                             radius: 8,
//                             width: 100,
//                             height: 50,
//                             fit: BoxFit.cover,
//                           ),
//                         );
//                       },
//                     ),
//                 ],
//               );
//             }),
//         ];
//
//       case HistoryType.sleep:
//         return [
//           if (history.sleep != null)
//             _buildInfoRow(context, Icons.nightlight_round,
//                 '${context.tr.sleep}: (${context.tr.from}: ${history.sleep?.sleepStartTime ?? ''}) - (${context.tr.to}: ${history.sleep?.sleepEndTime ?? ''})'),
//         ];
//
//       case HistoryType.food:
//         return [
//           if (history.food != null)
//             _buildInfoRow(context, Icons.fastfood,
//                 '${context.tr.food}: ${history.food?.getMealType() ?? ''} - ${context.tr.amount}: ${history.food?.getMealAmount() ?? ''}'),
//         ];
//
//       case HistoryType.supply:
//         return [
//           if (history.supply != null)
//             _buildInfoRow(context, Icons.inventory,
//                 '${context.tr.supply}: ${history.supply?.name ?? ''}'),
//         ];
//
//       case HistoryType.toilet:
//         return [
//           if (history.toilet != null)
//             _buildInfoRow(context, Icons.wc,
//                 '${context.tr.description}: ${history.toilet?.toiletType?.name ?? ''} ${history.toilet?.toiletWay?.name ?? ''}'),
//         ];
//     }
//   }
//
//   Widget _buildInfoRow(BuildContext context, IconData icon, String text) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8.0),
//       child: Row(
//         children: [
//           Icon(icon, size: 18, color: Colors.grey[700]),
//           const SizedBox(width: 8),
//           Expanded(
//             child: Text(text),
//           ),
//         ],
//       ),
//     );
//   }
// }
