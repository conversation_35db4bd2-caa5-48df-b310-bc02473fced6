import 'dart:developer';

import 'package:connectify_app/src/screens/history/model/history_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

// void postNewNotification({required NotificationModel notificationModel}) {
//   if (kDebugMode) {
//     Log.w('NotificationModel: ${notificationModel.toJson()}');
//     return;
//   }
//   NotificationRepo(NetworkApiServices())
//       .sendNotification(notificationModel: notificationModel);
// }

addNewHistory({required HistoryModel historyModel}) {
  // if (kDebugMode) {
  //   Log.w('HistoryModel: ${historyModel.toJson()}');
  //   return;
  // }
  HistoryRepo(NetworkApiServices()).addHistory(history: historyModel);
}

// * =========================================================

final historyRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return HistoryRepo(networkApiServices);
});

//? ========================================================

class HistoryRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  HistoryRepo(this._networkApiServices);

//? get History Data ========================================================
  Future<List<HistoryModel>> getHistoryData({
    required String date,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices
          .getResponse('${ApiEndpoints.history}&filters[date][\$eq]=$date');

      // final historyData = compute(responseToHistoryModelList, response);
      final data = (response[ApiStrings.data] as List?) ?? [];

      log('faasfasf ${data}');

      final historyData = data.map((e) => HistoryModel.fromJson(e)).toList();
      log('faasfasf22222222 ${historyData.map(
        (e) => e.id,
      )}');

      return historyData;
    });
  }

  //? Add History ========================================================
  Future<void> addHistory({required HistoryModel history}) async {
    return await baseFunction(() async {
      await _networkApiServices.postResponse(ApiEndpoints.editDeleteHistory,
          body: history.toJson());
    });
  }
}
