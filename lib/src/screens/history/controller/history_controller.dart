import 'package:connectify_app/src/screens/history/repo/history_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/history_model.dart';

final historyControllerProvider =
    Provider.family<HistoryController, BuildContext>((ref, context) {
  final historyRepo = ref.watch(historyRepoProvider);

  return HistoryController(historyRepo: historyRepo, context: context);
});
final historyChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<HistoryController, BuildContext>(
        (ref, context) {
  final historyRepo = ref.watch(historyRepoProvider);

  return HistoryController(historyRepo: historyRepo, context: context);
});

final getAllHistoryData = FutureProvider.family<List<HistoryModel>,
    (BuildContext context, String date)>((ref, params) {
  final historyRepo = ref.watch(historyRepoProvider);

  final historyController =
      HistoryController(historyRepo: historyRepo, context: params.$1);

  return historyController.getHistoryData(date: params.$2);
});

class HistoryController extends BaseVM {
  final HistoryRepo historyRepo;
  final BuildContext context;

  HistoryController({required this.historyRepo, required this.context});

  Future<List<HistoryModel>> getHistoryData({
    required String date,
  }) async {
    return await baseFunction(context, () async {
      final historyData = await historyRepo.getHistoryData(
        date: date,
      );

      // sort by createdAt
      historyData.sort((a, b) {
        return a.createdAt?.compareTo(b.createdAt ?? DateTime.now()) ?? 0;
      });


      return historyData;
    });
  }

  //? Add History ========================================================
  Future<void> addHistory({
    required HistoryModel history,
  }) async {
    return await baseFunction(context, () async {
      await historyRepo.addHistory(history: history);
    });
  }
}
