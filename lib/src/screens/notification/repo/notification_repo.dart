import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../model/notification_model.dart';

final notificationRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return NotificationRepo(networkApiServices);
});

// * =============================================================
void postNewNotification({required NotificationModel notificationModel}) {
  if (kDebugMode) {
    Log.w('NotificationModel: ${notificationModel.toJson()}');
    return;
  }
  NotificationRepo(NetworkApiServices())
      .sendNotification(notificationModel: notificationModel);
}

class NotificationRepo with BaseRepository {
  final BaseApiServices networkApiServices;

  NotificationRepo(this.networkApiServices);

  // Future<List<NotificationModel>> getNotification() async {
  //   return await baseFunction(() async {
  //     final isTeacher = const UserModel().isTeacher;
  //     final currentUserId = const UserModel().currentUser.id;
  //     final teacherTopic = NurseryModelHelper.allTeacherTopic();
  //
  //     String filterQuery = isTeacher
  //         ? 'filters[\$or][0][topic]=$teacherTopic&filters[\$or][1][teacher][id][\$eq]=$currentUserId&filters[topic][\$notContains]=parents'
  //         : 'filters[topic][\$notContains]=parents';
  //
  //     final response = await networkApiServices.getResponse(
  //       '${ApiEndpoints.notification}&$filterQuery&pagination[page]=1&pagination[pageSize]=500',
  //     );
  //
  //     final notificationData =
  //         await compute(responseFromNotificationModel, response);
  //
  //     return notificationData;
  //   });
  // }

  // get last notification only
  Future<NotificationModel> getLastNotification() async {
    return await baseFunction(() async {
      final isTeacher = const UserModel().isTeacher;
      final currentUserId = const UserModel().currentUser.id;
      final teacherTopic = NurseryModelHelper.allTeacherTopic();

      String filterQuery = isTeacher
          ? 'filters[\$or][0][topic]=$teacherTopic&filters[\$or][1][teacher][id][\$eq]=$currentUserId&filters[topic][\$notContains]=parents'
          : 'filters[topic][\$notContains]=parents';

      final response = await networkApiServices.getResponse(
        '${ApiEndpoints.notification}&$filterQuery&pagination[page]=1&pagination[pageSize]=1&sort=createdAt:desc',
      );

      final notificationData =
          await compute(responseFromNotificationModel, response);

      return notificationData.first;
    });
  }

  Future<List<NotificationModel>> getNotificationsPaginated({
    int page = 1,
  }) async {
    return await baseFunction(() async {
      final isTeacher = const UserModel().isTeacher;
      final currentUserId = const UserModel().currentUser.id;
      final teacherTopic = NurseryModelHelper.allTeacherTopic();

      String filterQuery = isTeacher
          ? 'filters[\$or][0][topic]=$teacherTopic&filters[\$or][1][teacher][id][\$eq]=$currentUserId&filters[topic][\$notContains]=parents'
          : 'filters[topic][\$notContains]=parents';

      final response = await networkApiServices.getResponse(
        '${ApiEndpoints.notification}&${ApiEndpoints.pagination(page)}&$filterQuery',
      );

      final notificationData =
          await compute(responseFromNotificationModel, response);

      return notificationData;
    });
  }

  // Future<List<NotificationModel>> getNotificationsPaginated(
  //     {int page = 1}) async {
  //   return baseFunction(
  //     () async {
  //       final response = await networkApiServices.getResponse(
  //         '${ApiEndpoints.notification}&${ApiEndpoints.pagination(page)}',
  //       );
  //
  //       final notificationPages =
  //           await compute(responseFromNotificationModel, response);
  //
  //       return notificationPages;
  //     },
  //   );
  // }

  // *  method to send notification
  Future<void> sendNotification({
    required NotificationModel notificationModel,
  }) async {
    await baseFunction(() async {
      final response = await networkApiServices.postResponse(
          ApiEndpoints.sendNotification,
          body: notificationModel.toJson());

      return response;
    });
  }
}
