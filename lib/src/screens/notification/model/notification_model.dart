import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

dynamic responseFromNotificationModel(dynamic response) {
  final notificationData = (response[ApiStrings.data] as List)
      .map((e) => NotificationModel.fromJson(e))
      .toList();

  return notificationData;
}

class NotificationModel extends Equatable {
  final int? id;
  final String title;
  final String body;
  final TeacherModel? teacher;
  final String topic;

  const NotificationModel({
    this.id,
    this.title = '',
    this.body = '',
    this.teacher,
    this.topic = '',
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final teacher = attributes[ApiStrings.teacher][ApiStrings.data] != null
        ? TeacherModel.fromAttributesJson(
            attributes[ApiStrings.teacher][ApiStrings.data])
        : null;

    return NotificationModel(
      id: json[ApiStrings.id],
      title: attributes[ApiStrings.title],
      body: attributes[ApiStrings.body],
      teacher: teacher,
      topic: attributes[ApiStrings.topic] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'body': body,
      'teacher': teacher?.id,
      'topic': topic,
    };
  }

  @override
  List<Object?> get props => [
        title,
        body,
        teacher,
        topic,
      ];
}
