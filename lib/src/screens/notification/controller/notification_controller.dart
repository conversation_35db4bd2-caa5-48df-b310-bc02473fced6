import 'dart:developer';

import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/notification_model.dart';

final notificationControllerProvider =
    Provider.family<NotificationController, BuildContext>((ref, context) {
  final notificationRepo = ref.watch(notificationRepoProvider);

  return NotificationController(
      notificationRepo: notificationRepo, context: context);
});

final getNotificationData =
    FutureProvider.family<NotificationModel, BuildContext>((ref, context) {
  final notificationController =
      ref.watch(notificationControllerProvider(context));

  return notificationController.getLastNotification();
});

final getNotificationDataProviderWithPagination =
    FutureProvider.family<List<NotificationModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;
    final notificationCtrl = ref.watch(notificationControllerProvider(context));

    return await notificationCtrl.getNotificationsPaginated(page: page);
  },
);

class NotificationController extends BaseVM {
  final NotificationRepo notificationRepo;
  final BuildContext context;

  NotificationController({
    required this.notificationRepo,
    required this.context,
  });

  Future<NotificationModel> getLastNotification() async {
    return await baseFunction(context, () async {
      final notificationData = await notificationRepo.getLastNotification();

      // final filterNotificationData = notificationData
      //     .where((element) => const UserModel().isTeacher
      //         ? element.topic == NurseryModelHelper.allTeacherTopic() ||
      //             (element.teacher != null &&
      //                     element.teacher!.id ==
      //                         const UserModel().currentUser.id) &&
      //                 !element.topic.contains('parents')
      //         : !element.topic.contains('parents'))
      //     .toList();

      log('asfsgggaf ${notificationData.id}');
      return notificationData;
    });
  }

  Future<List<NotificationModel>> getNotificationsPaginated(
      {int page = 1}) async {
    return await baseFunction(context, () async {
      final notificationData =
          await notificationRepo.getNotificationsPaginated(page: page);
      // final filterNotificationData = notificationData
      //     .where((element) => const UserModel().isTeacher
      //         ? element.topic == NurseryModelHelper.allTeacherTopic() ||
      //             (element.teacher != null &&
      //                     element.teacher!.id ==
      //                         const UserModel().currentUser.id) &&
      //                 !element.topic.contains('parents')
      //         : !element.topic.contains('parents'))
      //     .toList();

      return notificationData;
    });
  }
}
