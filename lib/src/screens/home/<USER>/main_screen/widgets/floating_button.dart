import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../model/home_add_model.dart';

class FloatingButtonWidget extends HookConsumerWidget {
  const FloatingButtonWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: EdgeInsets.only(bottom: 10.h),
        height: 50.h,
        width: 50.w,
        decoration: const BoxDecoration(
          color: ColorManager.primaryColor,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.add,
          color: ColorManager.white,
          size: 30,
        ),
      ),
    ).onTap(() {
      _showAlertDialog(context, ref);
    });
  }

  Future<void> _showAlertDialog(BuildContext context, ref) async {
    return showModalBottomSheet(
      elevation: 0,
      showDragHandle: true,
      useSafeArea: true,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        final addList = HomeAddModel.homeList(context, ref);
        final addTeacherList = HomeAddModel.addTeacherList(context);
        return Column(
          children: [
            Row(
              children: [
                Text(
                  context.tr.add,
                  style: context.headLine,
                ),
                const Spacer(),
                IconButton(
                    onPressed: () => context.back(),
                    icon: const Icon(
                      Icons.close,
                      size: 30,
                    ))
              ],
            ),
            Expanded(
              child: AutoHeightGridView(
                  itemCount: const UserModel().isAdmin
                      ? addList.length
                      : addTeacherList.length,
                  crossAxisCount: 2,
                  mainAxisSpacing: 15.h,
                  crossAxisSpacing: 10,
                  builder: (BuildContext context, int index) {
                    return BottomNavBarAddWidget(
                      home: const UserModel().isAdmin
                          ? addList[index]
                          : addTeacherList[index],
                    );
                  }),
            ),
          ],
        )
            .paddingAll(AppSpaces.mediumPadding)
            .sized(height: context.height * 0.5);
      },
    );
  }
}

class BottomNavBarAddWidget extends StatelessWidget {
  final HomeAddModel home;

  const BottomNavBarAddWidget({super.key, required this.home});

  @override
  Widget build(BuildContext context) {
    final isPng = home.image.contains('png');
    return BaseContainer(
        onTap: home.onTap,
        margin: 0,
        boxShadow: ConstantsWidgets.boxShadow,
        child: Column(
          children: [
            SizedBox(
                height: 40.h,
                child: isPng
                    ? Image.asset(home.image)
                    : SvgPicture.asset(home.image)),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                home.title.contains(context.tr.sendSupplies)
                    ? home.title
                    : ' ${context.tr.add} ${home.title}',
                style: context.hint.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ));
  }
}
