import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/add_activity_dialog.dart';
import 'package:connectify_app/src/screens/class/view/add_class/add_class_dialog.dart';
import 'package:connectify_app/src/screens/events/view/add_events/add_event.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_dialog.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/sleep/view/widgets/add_sleep_dialog.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/widgets/add_supply_dialog.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/widgets/add_teacher_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../financial/view/financial_screen.dart';
import '../../student/view/student_screen/widgets/add_student.dart';
import '../../supplies/view/teacher_supplies/widgets/assign_supply_dialog.dart';
import '../../toilet/view/widgets/add_toilet_dialog.dart';

class HomeAddModel {
  final String image;
  final String title;
  final String subTitle;
  final Function() onTap;

  const HomeAddModel({
    required this.image,
    required this.onTap,
    required this.title,
    required this.subTitle,
  });

  static List<HomeAddModel> homeList(BuildContext context, WidgetRef ref) => [
        HomeAddModel(
            onTap: () =>
                showAddClassDialog(context, navigateWidget: const MainScreen()),
            image: Assets.svgClasses,
            title: context.tr.classes,
            subTitle: context.tr.classes),
        HomeAddModel(
            onTap: () => showAddTeacherDialog(context,
                navigateWidget: const MainScreen()),
            image: Assets.svgStaff,
            title: context.tr.staff,
            subTitle: context.tr.members),
        HomeAddModel(
            onTap: () => showAddStudentDialog(context,
                ref: ref, navigateWidget: const MainScreen()),
            image: Assets.svgStudents,
            title: context.tr.students,
            subTitle: context.tr.students),
        HomeAddModel(
            onTap: () => showAddEditActivityDialog(context,
                navigateWidget: const MainScreen()),
            image: Assets.svgActivities,
            title: context.tr.activities,
            subTitle: context.tr.activities),
        HomeAddModel(
            onTap: () => showAddEventDialog(context),
            image: Assets.svgEvents,
            title: context.tr.events,
            subTitle: context.tr.eventThisMonth),
        HomeAddModel(
            onTap: () {
              context.back();

              context.to(const FinancialScreen());
            },
            image: Assets.svgFinancial,
            title: context.tr.financial,
            subTitle: '\n'),
        HomeAddModel(
            onTap: () => showAddEditSupplyDialog(context),
            image: Assets.imagesSupplies,
            title: context.tr.supplies,
            subTitle: context.tr.sendSupplies),
      ];

  static List<HomeAddModel> addTeacherList(BuildContext context) {
    return [
      HomeAddModel(
          onTap: () => showAddFoodDialog(context),
          image: Assets.imagesFood,
          title: context.tr.food,
          subTitle: context.tr.food),
      HomeAddModel(
          onTap: () => showAddSleepDialog(context),
          image: Assets.imagesSleep,
          title: context.tr.sleep,
          subTitle: context.tr.sleep),
      HomeAddModel(
          onTap: () => showAddToiletDialog(context),
          image: Assets.imagesToilet,
          title: context.tr.toilet,
          subTitle: context.tr.toilet),
      HomeAddModel(
          onTap: () => showAssignSupplyDialog(context),
          image: Assets.imagesSupplies,
          title: context.tr.sendSupplies,
          subTitle: context.tr.sendSupplies),
    ];
  }
}
