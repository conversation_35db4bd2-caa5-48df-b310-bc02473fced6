import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final attendanceCount = ValueNotifier(0);
final attendanceValue = ValueNotifier<num>(0);

class AttendeesOfToday extends ConsumerWidget {
  const AttendeesOfToday({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseContainer(
      radius: AppRadius.sliderRadius,
      child: Column(children: [
        Row(
          children: [
            Text(
              context.tr.attendeesOfToday,
              style: context.boldTitle,
            ),
            const Spacer(),
            ValueListenableBuilder<int>(
              valueListenable: attendanceCount,
              builder: (context, attendance, child) {
                return ValueListenableBuilder<int>(
                  valueListenable: StudentController.allActiveStudentsLength,
                  builder: (context, students, child) {
                    return Text(
                      '$attendance ${context.tr.Of} $students',
                      style: context.subTitle.copyWith(color: Colors.grey),
                    );
                  },
                );
              },
            ),
          ],
        ),
        context.largeGap,
        ValueListenableBuilder<num>(
          valueListenable: attendanceValue,
          builder: (context, value, child) {
            return LinearProgressIndicator(
              borderRadius: BorderRadius.circular(AppRadius.baseRadius),
              backgroundColor: ColorManager.greyColor,
              value: value.isNaN || value.isInfinite ? 0 : value.toDouble(),
              minHeight: 10,
              valueColor:
                  const AlwaysStoppedAnimation<Color>(ColorManager.purple),
            );
          },
        ),
      ]),
    );
  }
}
