import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/shared_widgets.dart';

class CurrentActivityCard extends StatelessWidget {
  final ClassModel? classModel;
  final TeacherActivityModel teacherActivity;

  const CurrentActivityCard(
      {super.key, required this.classModel, required this.teacherActivity});

  @override
  Widget build(BuildContext context) {
    final classImage = SizedBox(
      height: 50.h,
      width: 55.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: BaseCachedImage(
          classModel?.image?.url ?? '',
          errorWidget: Image.network(
            'https://img.freepik.com/premium-vector/education-school-logo-design_586739-1327.jpg',
          ),
        ),
      ),
    );
    final activityImage = SizedBox(
      height: 50.h,
      width: 55.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: BaseCachedImage(teacherActivity.activity?.image?.url ?? '',
            errorWidget: const BaseCachedImage(
              AppConsts.activityPlaceholder,
            )),
      ),
    );
    return Row(
      children: [
        classImage,
        context.smallGap,
        Text(
          classModel?.name ?? '',
          style: context.title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const Spacer(),
        Column(
          children: [
            activityImage,
            Text(
              teacherActivity.activity?.name ?? '',
              style: context.labelLarge,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        )
      ],
    );
  }
}
