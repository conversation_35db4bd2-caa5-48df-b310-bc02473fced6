import 'package:connectify_app/src/screens/home/<USER>/home_model.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeCard extends StatelessWidget {
  final HomeModel home;
  final bool isPngImage;

  const HomeCard({super.key, required this.home, this.isPngImage = false});

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
        padding: AppSpaces.smallPadding,
        onTap: () => context.to(home.widget),
        boxShadow: ConstantsWidgets.boxShadow,
        child: Column(
          children: [
            SizedBox(
                height: 60.h,
                child: isPngImage
                    ? Image.asset(home.image)
                    : SvgPicture.asset(home.image)),
            context.xSmallGap,
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                home.title,
                style: context.hint.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
            // context.smallGap,
            // FittedBox(
            //   child: Text(
            //     home.subTitle,
            //     style: context.smallHint,
            //     maxLines: 1,
            //   ),
            // )
          ],
        ));
  }
}
