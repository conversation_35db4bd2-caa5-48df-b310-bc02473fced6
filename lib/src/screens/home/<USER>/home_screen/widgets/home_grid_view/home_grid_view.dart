import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/home_grid_view/home_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/consts/app_constants.dart';
import '../../../../../auth/models/user_model.dart';

class HomeGridView extends StatelessWidget {
  const HomeGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return AutoHeightGridView(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: HomeModel.homeList(context).length,
        crossAxisCount: 3,
        mainAxisSpacing: 15.h,
        crossAxisSpacing: 10,
        builder: (BuildContext context, int index) {
          final home = HomeModel.homeList(context)[index];
          final isPngImage = HomeModel.homeList(context)[index].image.contains('png');

          return WidgetAnimator(
              delay: Duration(milliseconds: AppConsts.animatedDuration * index),
              child: HomeCard(
                home: home,
                isPngImage: isPngImage,
              ));
        });
  }
}
