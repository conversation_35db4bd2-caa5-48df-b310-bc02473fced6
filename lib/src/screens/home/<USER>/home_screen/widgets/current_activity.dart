import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/activities/controllers/teacher_activities_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/indicator_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/shared_widgets.dart';
import 'current_activity_card.dart';

final currentActivities = ValueNotifier<List<TeacherActivityModel>>([]);

class CurrentActivity extends ConsumerWidget {
  const CurrentActivity({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(indicatorControllerProvider);
    final indicatorCtrl = ref.watch(indicatorController);

    return ValueListenableBuilder(
      valueListenable: currentActivities,
      builder: (context, filteredActivities, child) {
        if (filteredActivities.isEmpty) {
          return const SizedBox();
        }

        return BaseContainer(
            boxShadow: ConstantsWidgets.boxShadow,
            radius: AppRadius.sliderRadius,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //! Current Activity Text
                Text(
                  context.tr.currentActivity,
                  style: context.boldTitle,
                ),

                context.mediumGap,

                //! Current Activity List
                if (filteredActivities.isNotEmpty) ...[
                  SizedBox(
                    height: 75.h,
                    width: double.infinity,
                    child: PageView(
                      children: filteredActivities.map((e) {
                        return CurrentActivityCard(
                            classModel: e.classModel, teacherActivity: e);
                      }).toList(),
                      onPageChanged: (index) {
                        indicatorCtrl.changeIndex(index);
                      },
                    ),
                  ),

                  //! Indicator
                  Center(
                    child: DotsIndicator(
                      dotsCount: filteredActivities.length,
                      position: currentIndex,
                      mainAxisSize: MainAxisSize.min,
                      reversed: false,
                      decorator: DotsDecorator(
                        size: const Size.square(9.0),
                        activeSize: const Size(18.0, 9.0),
                        color: ColorManager.greyIndicator,
                        activeColor: ColorManager.purple,
                        activeShape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5.0)),
                      ),
                    ),
                  ),
                ]
              ],
            ));
      },
    );

    final teacherActivityCtrl =
        ref.watch(getTeacherActivitiesByTodayDataProvider(context));

    // return teacherActivityCtrl.get(
    //   data: (teacherActivities) {
    //     final filteredActivities = teacherActivities.where(
    //       (element) {
    //         if (element.date.isNotEmpty && !element.isWeekly) {
    //           final date = DateTime.parse(element.date);
    //           final now = DateTime.now();
    //           return date.day == now.day &&
    //               date.month == now.month &&
    //               date.year == now.year;
    //         }
    //
    //         return true;
    //       },
    //     ).toList();
    //
    //     if (filteredActivities.isEmpty) {
    //       return const SizedBox();
    //     }
    //
    //     return BaseContainer(
    //         boxShadow: ConstantsWidgets.boxShadow,
    //         radius: AppRadius.sliderRadius,
    //         child: Column(
    //           crossAxisAlignment: CrossAxisAlignment.start,
    //           children: [
    //             //! Current Activity Text
    //             Text(
    //               context.tr.currentActivity,
    //               style: context.boldTitle,
    //             ),
    //
    //             context.mediumGap,
    //
    //             //! Current Activity List
    //             if (filteredActivities.isNotEmpty) ...[
    //               SizedBox(
    //                 height: 75.h,
    //                 width: double.infinity,
    //                 child: PageView(
    //                   children: filteredActivities.map((e) {
    //                     return CurrentActivityCard(
    //                         classModel: e.classModel, teacherActivity: e);
    //                   }).toList(),
    //                   onPageChanged: (index) {
    //                     indicatorCtrl.changeIndex(index);
    //                   },
    //                 ),
    //               ),
    //
    //               //! Indicator
    //               Center(
    //                 child: DotsIndicator(
    //                   dotsCount: filteredActivities.length,
    //                   position: currentIndex,
    //                   mainAxisSize: MainAxisSize.min,
    //                   reversed: false,
    //                   decorator: DotsDecorator(
    //                     size: const Size.square(9.0),
    //                     activeSize: const Size(18.0, 9.0),
    //                     color: ColorManager.greyIndicator,
    //                     activeColor: ColorManager.purple,
    //                     activeShape: RoundedRectangleBorder(
    //                         borderRadius: BorderRadius.circular(5.0)),
    //                   ),
    //                 ),
    //               ),
    //             ]
    //           ],
    //         ));
    //   },
    // );
  }
}
