import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<FoodModel> responseToFoodModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final foodData = data.map((e) => FoodModel.fromJson(e)).toList();

  return foodData;
}

enum MealTypes { breakFast, snack, lunch }

enum MealAmount { all, more, some, none }

class FoodModel extends Equatable {
  final int? id;
  final MealTypes? mealType;
  final MealAmount? mealAmount;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;

  const FoodModel(
      {this.id,
      this.mealType,
      this.mealAmount,
      this.student,
      this.nursery,
      this.teacher});

  factory FoodModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return FoodModel(
      id: json[ApiStrings.id],
      mealAmount: getMealAmountFromApi(
          attributes[ApiStrings.mealAmount].toString().toLowerCase()),
      mealType: getMealTypeFromApi(
          attributes[ApiStrings.mealType].toString().toLowerCase()),
    );
  }

  static MealTypes getMealTypeFromApi(String value) {
    switch (value) {
      case 'breakfast':
        return MealTypes.breakFast;
      case 'snack':
        return MealTypes.snack;
      case 'lunch':
        return MealTypes.lunch;

      default:
        return MealTypes.breakFast;
    }
  }

  static MealAmount getMealAmountFromApi(String value) {
    switch (value) {
      case 'all':
        return MealAmount.all;
      case 'more':
        return MealAmount.more;
      case 'some':
        return MealAmount.some;
      case 'none':
        return MealAmount.none;

      default:
        return MealAmount.all;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.mealType: getMealType(),
      ApiStrings.mealAmount: getMealAmount(),
      ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId()
    };
  }

  String getMealType() {
    switch (mealType) {
      case MealTypes.breakFast:
        return "Breakfast";
      case MealTypes.snack:
        return "Snack";
      case MealTypes.lunch:
        return "Lunch";

      default:
        return 'Breakfast';
    }
  }

  static MealTypes getMealTypeValue({required int value}) {
    switch (value) {
      case 0:
        return MealTypes.breakFast;
      case 1:
        return MealTypes.snack;
      case 2:
        return MealTypes.lunch;

      default:
        return MealTypes.breakFast;
    }
  }

  String getMealAmount() {
    switch (mealAmount) {
      case MealAmount.all:
        return "All";
      case MealAmount.more:
        return "More";
      case MealAmount.some:
        return "Some";
      case MealAmount.none:
        return "None";

      default:
        return 'All';
    }
  }

  static MealAmount getMealAmountValue({required int value}) {
    switch (value) {
      case 0:
        return MealAmount.all;
      case 1:
        return MealAmount.more;
      case 2:
        return MealAmount.some;

      default:
        return MealAmount.none;
    }
  }

  @override
  List<Object?> get props =>
      [id, mealType, mealAmount, student, nursery, teacher];
}
