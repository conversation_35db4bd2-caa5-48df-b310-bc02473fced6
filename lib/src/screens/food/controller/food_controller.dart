import 'package:connectify_app/src/screens/food/repo/food_repo.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/food_model.dart';

final foodControllerProvider =
    Provider.family<FoodController, BuildContext>((ref, context) {
  final foodRepo = ref.watch(foodRepoProvider);

  return FoodController(foodRepo: foodRepo, context: context);
});
final foodChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<FoodController, BuildContext>((ref, context) {
  final foodRepo = ref.watch(foodRepoProvider);

  return FoodController(foodRepo: foodRepo, context: context);
});

final getAllFoodData =
    FutureProvider.family<List<FoodModel>, BuildContext>((ref, context) {
  final foodRepo = ref.watch(foodRepoProvider);

  final foodController = FoodController(foodRepo: foodRepo, context: context);

  return foodController.getFoodData();
});

class FoodController extends BaseVM {
  final FoodRepo foodRepo;
  final BuildContext context;

  FoodController({required this.foodRepo, required this.context});

  Future<List<FoodModel>> getFoodData() async {
    return await baseFunction(context, () async {
      final foodData = await foodRepo.getFoodData();

      return foodData;
    });
  }

  //? Add Food ========================================================
  Future<void> addFood(
      {required int mealTypeValue,
      required int mealAmountValue,
      required final StudentModel? student}) async {
    return await baseFunction(context, () async {
      final getMealTypeCurrentVal =
          FoodModel.getMealTypeValue(value: mealTypeValue);
      final getMealAmountCurrentVal =
          FoodModel.getMealAmountValue(value: mealAmountValue);

      final food = FoodModel(
          mealType: getMealTypeCurrentVal,
          mealAmount: getMealAmountCurrentVal,
          student: student);

      await foodRepo.addFood(food: food);

      NotificationService.sendNotification(
        title: "Meal Update",
        body:
            "${food.mealType?.name} (${food.mealAmount?.name}) has been added to ${student?.name}'s meal",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Meal Update",
        body:
            "${food.mealType?.name} (${food.mealAmount?.name}) has been added to ${student?.name}'s meal",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));
    });
  }
}
