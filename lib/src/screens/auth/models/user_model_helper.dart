part of 'user_model.dart';

enum UserTypeEnum { admin, teacher, child, parent }

UserTypeEnum _userTypeCheck(String? type) {
  switch (type) {
    case ApiStrings.admin:
      return UserTypeEnum.admin;

    case ApiStrings.teacher:
      return UserTypeEnum.teacher;

    case ApiStrings.parent:
      return UserTypeEnum.parent;

    default:
      return UserTypeEnum.teacher;
  }
}

//? Get User Data from local

extension UserModelExtensions on UserModel {
  UserModel get currentUser {
    final user = GetStorageService.getLocalData(
      key: LocalKeys.user,
    );

    if (user == null) return UserModel.empty();

    return UserModel.fromJson(user);
  }

  bool get isTeacher => currentUser.userType == UserTypeEnum.teacher;

  bool get isAdmin => currentUser.userType == UserTypeEnum.admin;

  bool get isConnectifyAdmin => currentUser.email == '<EMAIL>';

  bool get isParent => currentUser.userType == UserTypeEnum.parent;

  // String get selectedUserStudentFilter {
  //   if (isTeacher) {
  //     final classId = selectedTeacherClass.value?.id;
  //     // currentUser.classes?.firstOrNull?.id;
  //
  //     return '&filters[class][id]=$classId';
  //   }
  //
  //   return '';
  // }

  String selectedTeacherClassFilter() {
    if (isTeacher) {
      final classId = selectedTeacherClass.value?.id ??
          currentUser.classes?.firstOrNull?.id;

      log('asfasfsafsa ${selectedTeacherClass.value?.id}');

      return '&filters[class][id]=$classId';
    }

    return '';
  }

  String get selectedUserStudentFilterSign {
    if (isTeacher) {
      final classId = selectedTeacherClass.value?.id;
      // currentUser.classes?.firstOrNull?.id;

      return '?filters[class][id]=$classId';
    }

    return '';
  }
}
