import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/repos/auth_repo.dart';
import 'package:connectify_app/src/screens/auth/repos/users_repo.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:restart_app/restart_app.dart';
import 'package:xr_helper/xr_helper.dart';

final authControllerProvider =
    ChangeNotifierProvider.family<AuthController, BuildContext>((ref, context) {
  final remoteAuthRepo = ref.watch(authRepoProvider);
  final userRepo = ref.watch(userRepoProvider);

  return AuthController(context,
      remoteAuthRepo: remoteAuthRepo, userRepo: userRepo);
});

//getNurseryById
final getNurseryByIdFutureProvider =
    FutureProvider.family<NurseryModel?, (BuildContext, int?)>(
        (ref, nurseryId) async {
  final authCtrl = ref.watch(authControllerProvider(nurseryId.$1));

  return await authCtrl.getNurseryById(nurseryId: nurseryId.$2);
});

class AuthController extends BaseVM {
  final AuthRepo remoteAuthRepo;
  final UserRepo userRepo;
  final BuildContext context;

  AuthController(
    this.context, {
    required this.remoteAuthRepo,
    required this.userRepo,
  });

  //! Fcm Token ------------------------
  final _fcm = FirebaseMessaging.instance;

  Future<String?> get token => _fcm.getToken();

  //! Disabled OTP Sheet Timer ------------------------
  bool _disabledTimer = false;

  bool get disabledTimer => _disabledTimer;

  set disabledTimer(bool value) {
    _disabledTimer = value;
    notifyListeners();
  }

  //! Sign in with phone number ------------------------
  Future signInWithPhoneNumber({
    required String phone,
  }) async {
    await baseFunction(context, () async {
      await remoteAuthRepo.login(phone: phone);
    });
  }

  //! Verify OTP ------------------------
  Future<bool> verifyOTP({
    required String otp,
  }) async {
    return await baseFunction(context, () async {
      final verified = await remoteAuthRepo.verifyOTP(otp);

      return verified;
    });
  }

  //! Resend code ------------------------
  Future resendCode({
    required String phone,
  }) async {
    await baseFunction(context, () async {
      await remoteAuthRepo.login(phone: phone);

      notifyListeners();
    });
  }

  Future<UserModel> checkAndGetUser({required String phone}) async {
    return await baseFunction(context, () async {
      Log.w('check Ctrl First');
      final signedUser = await userRepo.signedUser(phone: phone);
      Log.w('check Ctrl $signedUser');

      final isUserExist = signedUser != null;

      if (!isUserExist) {
        context.showBarMessage(context.tr.userNotFound, isError: true);
        return UserModel.empty();
      } else {
        return signedUser;
      }
    });
  }

  //! Update Profile ------------------------
  Future updateProfile({
    required Map<String, TextEditingController> controllers,
    String? filePath,
  }) async {
    await baseFunction(context, () async {
      await Future(() async {
        final userModel = UserModel(
          id: const UserModel().currentUser.id,
          name: controllers[ApiStrings.name]!.text,
          phone: controllers[ApiStrings.phone]!.text,
          email: controllers[ApiStrings.email]!.text,
          password: controllers[ApiStrings.password]?.text,
        );

        await userRepo.updateUser(user: userModel, filePath: filePath);

        final user = await checkAndGetUser(phone: userModel.phone!);

        GetStorageService.setLocalData(
          key: LocalKeys.user,
          value: user.toJson(),
        );

        context.toReplacement(const MainScreen());

        context.showBarMessage(context.tr.editSuccessfully);

        if (const UserModel().isAdmin) {
          await userRepo.updateNursery(
            user: userModel,
            pickedImage: filePath ?? '',
          );

          final nurseryData =
              await userRepo.getNurseryByUserId(userId: userModel.id);

          GetStorageService.setLocalData(
            key: LocalKeys.nursery,
            value: nurseryData,
          );
        }
      });
    }, additionalFunction: (_) {});
  }

  Future<NurseryModel?> getNurseryById({required int? nurseryId}) async {
    return await userRepo.getNurseryById(id: nurseryId);
  }

  //updatePassword
  Future updatePassword({
    required Map<String, TextEditingController> controllers,
    bool isDelete = false,
  }) async {
    await baseFunction(context, () async {
      final userModel = UserModel(
        id: const UserModel().currentUser.id,
        password: controllers[ApiStrings.password]?.text,
      );

      await userRepo.updateUser(user: userModel, changePassword: true);
    }, additionalFunction: (_) {
      if (!isDelete) {
        context.toReplacement(const MainScreen());
      }
    });
  }

  //! Logout ------------------------
  Future logout() async {
    return await baseFunction(context, () async {
      NotificationService.unsubscribeFromTopic(
        NurseryModelHelper.adminTopic(),
      );
      NotificationService.unsubscribeFromTopic(
        NurseryModelHelper.allTeacherTopic(),
      );

      NotificationService.unsubscribeFromTopic(
        NurseryModelHelper.teacherByIdTopic(
          const UserModel().currentUser.id,
        ),
      );

      final currentLangCode =
          GetStorageService.getLocalData(key: LocalKeys.language);

      await GetStorageService.clearLocalData();

      await GetStorageService.setLocalData(
        key: LocalKeys.language,
        value: currentLangCode,
      );

      await GetStorageService.setLocalData(
        key: LocalKeys.haveSeenOnBoarding,
        value: true,
      );

      await remoteAuthRepo.signOut();
    },
        additionalFunction: (_) =>
            // kDebugMode ?
            //     context.toReplacement(const BaseApp(
            //       showSignIn: true,
            //     )):
            Restart.restartApp());
  }
}
