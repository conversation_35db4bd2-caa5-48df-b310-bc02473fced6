import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../nursery/models/nursery_model.dart';
import '../../nursery/models/payment_methods_model.dart';

final userRepoProvider = Provider<UserRepo>((ref) {
  final netWorkApiServices = ref.watch(networkServiceProvider);

  return UserRepo(netWorkApiServices);
});

class UserRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  UserRepo(this._networkApiServices);

  // Future<List<UserModel>> getUsers() async {
  //   return baseFunction(() async {
  //     final response =
  //         await _networkApiServices.getResponse(ApiEndpoints.usersPopulate);
  //     return compute(responseToUserModelList, response);
  //   });
  // }

  Future<List<UserModel>> _getUsersByPhone({
    required String phone,
  }) async {
    return baseFunction(() async {
      final response = await _networkApiServices
          .getResponse('${ApiEndpoints.usersPopulate}&filters[phone]=$phone');

      return compute(responseToUserModelList, response);
    });
  }

  Future<void> addUser({
    required UserModel user,
    required String pickedImage,
    int? studentId,
    int? classId,
  }) async {
    return baseFunction(() async {
      final addNursery = studentId == null;

      final userRes = await _networkApiServices.postResponse(
        ApiEndpoints.auth,
        body: user.toJson(studentId: studentId, classId: classId),
        filePaths: [pickedImage],
        fromAuth: true,
        isNormalMethod: studentId != null,
      );

      if (addNursery) {
        final nursery = await _networkApiServices.postResponse(
          ApiEndpoints.nursery,
          body: {
            ApiStrings.admin: userRes[ApiStrings.user][ApiStrings.id],
            ApiStrings.name: user.name,
          },
          filePaths: [pickedImage],
          fieldName: ApiStrings.logo,
        );

        final res = await _networkApiServices.putResponse(
          '${ApiEndpoints.users}/${userRes['user']['id']}',
          data: {ApiStrings.nursery: nursery['data']['id']},
          filePaths: [pickedImage],
          fromAuth: true,
        );

        Log.w('afafsfff $res');
      }

      //
      // await GetStorageService.setLocalData(
      //     key: LocalKeys.nursery, value: nursery);
    });
  }

// update nursery
  Future<void> updateNursery(
      {required UserModel user, required String pickedImage}) async {
    return baseFunction(() async {
      final currentNurseryId = NurseryModelHelper.currentNurseryId();

      await _networkApiServices.putResponse(
        '${ApiEndpoints.updateNursery}/$currentNurseryId',
        data: {
          ApiStrings.name: user.name,
        },
        filePaths: [pickedImage],
        fieldName: ApiStrings.logo,
      );
    });
  }

  // update nursery payment methods
  Future<void> updateNurseryPaymentMethods({
    required PaymentMethodsModel paymentMethods,
  }) async {
    return baseFunction(() async {
      final currentNurseryId = NurseryModelHelper.currentNurseryId();

      await _networkApiServices.putResponse(
        '${ApiEndpoints.updateNursery}/$currentNurseryId',
        data: {
          ApiStrings.paymentMethods: paymentMethods.toJson(),
        },
      );
    });
  }

  Future<void> editUser({
    required UserModel user,
    required String pickedImage,
    required int id,
    int? studentId,
    int? classId,
  }) async {
    return baseFunction(() async {
      final res = await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/$id',
        data: user.toJson(
          studentId: studentId,
          classId: classId,
          sendUsername: false,
        ),
        filePaths: [pickedImage],
        fromAuth: true,
        isNormalMethod: studentId != null,
      );

      if (pickedImage.isNotEmpty) {
        await _updateProfileImage(
            id: res[ApiStrings.user][ApiStrings.id].toString(),
            image: pickedImage);
      }
    });
  }

  Future<void> deleteUser({required int id}) async {
    return baseFunction(() async {
      await _networkApiServices.deleteResponse('${ApiEndpoints.users}/$id');
    });
  }

  Future<UserModel?> signedUser({required String phone}) async {
    return baseFunction(() async {
      final users = await _getUsersByPhone(phone: phone);

      return users
          .firstWhereOrNull((element) => element.phone?.trim() == phone);
    });
  }

  Future<Map<String, dynamic>?> getNurseryByUserId(
      {required int? userId}) async {
    return baseFunction(() async {
      final response = await _networkApiServices
          .getResponse(ApiEndpoints.filteredNurseryByAdminId(userId));

      if (response == null || response.isEmpty) return null;

      return response['data'][0];
    });
  }

  Future<NurseryModel?> getNurseryById({required int? id}) async {
    return baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.nursery}/$id',
      );

      final data = response['data'] != null && response['data'].isNotEmpty
          ? response['data'][0]
          : null;

      if (data == null) return null;

      return NurseryModel.fromAttributesJson(data);
    });
  }

  Future<void> _updateProfileImage(
      {required String? id, required String? image}) async {
    return baseFunction(() async {
      final uploadImageModel = UploadImageModel(
          refId: id.toString(),
          ref: 'plugin::users-permissions.user',
          field: 'image');
      await _networkApiServices.uploadFile(
          filePath: image!, uploadImageModel: uploadImageModel);
    });
  }

  Future<void> updateUser(
      {required UserModel user,
      String? filePath,
      bool changePassword = false}) async {
    return baseFunction(() async {
      await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/${user.id}',
        data: changePassword ? user.toLoginJson() : user.toJson(),
        fromAuth: true,
        filePaths: [filePath ?? ''],
        fieldName: 'image',
      );
    });
  }

  Future<bool> login({required UserModel user}) async {
    return baseFunction(() async {
      final response = await _networkApiServices.postResponse(
          ApiEndpoints.login,
          body: user.toLoginJson(),
          fromAuth: true);

      return response != null;
    });
  }

  Future<void> saveUserToLocal({required UserModel? user}) async {
    return baseFunction(() async {
      await GetStorageService.setLocalData(
          key: LocalKeys.user, value: user?.toJson());
    });
  }

  Future<UserModel?> getUserFromLocal() async {
    return baseFunction(() async {
      final user = await GetStorageService.getLocalData(key: LocalKeys.user);
      return user != null ? UserModel.fromJson(user) : null;
    });
  }
}
