import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/screens/auth/view/widgets/otp_bottom_sheet/otp_bottom_sheet.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../generated/assets.dart';
import '../../../../../../shared/widgets/icon_widget/fields_icon_widget.dart';

class AdminSignUpFields extends HookConsumerWidget {
  final bool isAdmin;
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<bool> phoneVerified;

  const AdminSignUpFields(
      {super.key,
      required this.controllers,
      required this.phoneVerified,
      this.isAdmin = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider(context));
    final isObscure = useState(true);

    void verify() async {
      final phone = '+2${controllers[ApiStrings.phone]?.text}';

      final phoneNotValid = phone.isEmpty || phone.length < 13;

      if (phoneNotValid) {
        context.showBarMessage(context.tr.enterValidPhoneNumber, isError: true);
        return;
      }

      await authController.signInWithPhoneNumber(
        phone: phone,
      );

      _showAuthBottomSheet(context, phone: phone);
    }

    return Column(
      children: [
        if (isAdmin) ...[
          SinglePickImageWidget(
            pickImageWidget: BaseTextField(
              enabled: false,
              isRequired: false,
              title: context.tr.nurseryLogo,
              hint: context.tr.uploadLogo,
              suffixIcon: const FieldsIconWidget(
                icon: Assets.iconsUpload,
              ),
            ),
          ),

          context.largeGap,

          //! Name Field
          BaseTextField(
            title: context.tr.nurseryName,
            validator: (value) {
              // cannot be only numbers
              final isNumeric = double.tryParse(value ?? '');

              if (isNumeric != null) {
                return context.tr.enterValidNurseryName;
              }

              return Validations.mustBeNotEmpty(value);
            },
            controller: controllers[ApiStrings.name],
          ),
        ],

        context.largeGap,

        //! Phone Field
        BaseTextField(
          title: context.tr.phoneNumber,
          enabled: true,
          // !isAdmin || !phoneVerified.value,
          textInputType: TextInputType.phone,
          controller: controllers[ApiStrings.phone],
          hint: '0113 xxxx xxxx',
          icon: const FieldsIconWidget(
            icon: Assets.iconsPhone,
          ),
          // suffixIcon:
          // !isAdmin
          //     ? null
          //     : phoneVerified.value
          //         ? const Icon(
          //             Icons.check_circle,
          //             color: ColorManager.buttonColor,
          //           )
          //         : TextButton(
          //             onPressed: verify,
          //             child: Text(
          //               context.tr.verify,
          //               style: context.labelLarge.copyWith(
          //                   color: ColorManager.primaryColor,
          //                   fontWeight: FontWeight.bold),
          //             ),
          //           ),
        ),

        context.largeGap,

        //! Password Field
        BaseTextField(
          title: context.tr.password,
          textInputType: TextInputType.visiblePassword,
          controller: controllers[ApiStrings.password],
          isObscure: isObscure.value,
          validator: (value) {
            return Validations.password(value);
          },
          suffixIcon: InkWell(
            onTap: () {
              isObscure.value = !isObscure.value;
            },
            child: const FieldsIconWidget(
              icon: Assets.iconsCloseEye,
            ),
          ),
        ),

        context.largeGap,

        //! Email Field
        BaseTextField(
          title: context.tr.email,
          controller: controllers[ApiStrings.email],
          textInputType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  void _showAuthBottomSheet(BuildContext context, {String? phone}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.baseRadius),
                topRight: Radius.circular(15))),
        builder: (ctx) =>
            OtpBottomSheet(phoneNumber: phone, phoneVerified: phoneVerified));
  }
}
