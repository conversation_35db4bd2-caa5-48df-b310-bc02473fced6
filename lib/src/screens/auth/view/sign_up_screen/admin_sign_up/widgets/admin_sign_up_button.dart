import 'package:connectify_app/src/screens/auth/controllers/users_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/widgets/loading/loading_widget.dart';

class AdminSignUpButton extends HookConsumerWidget {
  final Map<String, TextEditingController> controllers;
  final bool isAdmin;
  final (
    bool onChecked,
    bool phoneVerified,
    GlobalKey<FormState> formKey
  ) valueNotifiers;

  const AdminSignUpButton(
      {super.key,
      required this.controllers,
      required this.valueNotifiers,
      this.isAdmin = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userController = ref.watch(userChangeNotifierProvider(context));

    final isTermsChecked = valueNotifiers.$1;
    final isPhoneVerified = valueNotifiers.$2;
    final formKey = valueNotifiers.$3;

    final filePath = ref.watch(mediaPickerControllerProvider).filePath;

    void validateAndAddOrEditUser() {
      if (!formKey.currentState!.validate()) return;
      if (!isTermsChecked) {
        context.showBarMessage(context.tr.pleaseAcceptTerms, isError: true);
        return;
      }

      if (!isPhoneVerified) {
        context.showBarMessage(context.tr.pleaseVerifyPhone, isError: true);
        return;
      }

      userController.checkUserExistAndAddOrEdit(
        controllers: controllers,
        pickedImage: filePath,
        userType: isAdmin ? UserTypeEnum.admin : UserTypeEnum.teacher,
      );
    }

    return Button(
        isLoading: userController.isLoading,
        loadingWidget: const LoadingWidget(
          loadingType: LoadingType.button,
        ),
        // enabled: isTermsChecked && isPhoneVerified,
        label: context.tr.finishLetsStart,
        onPressed: validateAndAddOrEditUser);
  }
}
