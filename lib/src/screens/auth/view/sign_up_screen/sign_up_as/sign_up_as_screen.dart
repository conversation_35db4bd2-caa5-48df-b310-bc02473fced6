import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/view/sign_up_screen/admin_sign_up/sign_up_screen.dart';
import 'package:connectify_app/src/screens/auth/view/widgets/background_and_logo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class SignUpAsScreen extends StatelessWidget {
  const SignUpAsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundLogo(
        child: Column(
      children: [
        context.xlLargeGap,
        Text(
          context.tr.SignupAsa,
          style: context.authTitle,
        ),
        context.largeGap,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _ContainerImage(
              onTap: () => context.to(const SignUpScreen(isAdmin: true)),
              title: context.tr.administrator,
              imagePath: Assets.imagesAdmin,
            ),
            context.smallGap,
            Text(
              'or',
              style: context.title,
            ),
            context.smallGap,
            _ContainerImage(
              onTap: () => context.to(const SignUpScreen()),
              title: context.tr.teacher,
              imagePath: Assets.imagesTeacher,
            ),
          ],
        )
      ],
    ));
  }
}

class _ContainerImage extends StatelessWidget {
  final String imagePath;
  final String title;
  final Function() onTap;

  const _ContainerImage(
      {required this.imagePath, required this.title, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            height: 150,
            width: 150,
            decoration: BoxDecoration(
                color: ColorManager.containerColor,
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius)),
            child: Image.asset(
              imagePath,
            ),
          ),
        ),
        context.smallGap,
        Text(
          title,
          style: context.title,
        )
      ],
    );
  }
}
