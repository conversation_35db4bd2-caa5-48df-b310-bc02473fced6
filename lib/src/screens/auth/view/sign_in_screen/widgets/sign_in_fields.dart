import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/view/reset_password/reset_password.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../generated/assets.dart';
import '../../../../../shared/widgets/icon_widget/fields_icon_widget.dart';
import '../../../controllers/auth_controller.dart';
import '../../widgets/otp_bottom_sheet/otp_bottom_sheet.dart';

class SignInFields extends HookConsumerWidget {
  final Map<String, TextEditingController> controllers;

  const SignInFields({
    super.key,
    required this.controllers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider(context));
    final isObscure = useState(true);
    final phoneVerified = useState<bool>(false);

    void showAuthBottomSheet(BuildContext context,
        {String? phone, Function? onSuccess}) {
      showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppRadius.baseRadius),
                  topRight: Radius.circular(15))),
          builder: (ctx) => OtpBottomSheet(
              onSuccess: onSuccess,
              phoneNumber: '+2${controllers[ApiStrings.phone]?.text}',
              phoneVerified: phoneVerified));
    }

    Future<void> verify() async {
      final phone = '+2${controllers[ApiStrings.phone]?.text}';
      Log.i(phone);
      final phoneNotValid = phone.length < 13;

      if (phoneNotValid) {
        context.showBarMessage(context.tr.enterValidPhoneNumber, isError: true);
        if (phone == '+2' || phone.isEmpty) {
          context.showBarMessage(context.tr.enterPhoneNumberFirst,
              isError: true);
        }
        return;
      }

      final user = await authController.checkAndGetUser(
          phone: controllers[ApiStrings.phone]?.text ?? '');

      await authController.signInWithPhoneNumber(
        phone: phone,
      );

      if (!context.mounted || user == UserModel.empty()) return;

      if (kDebugMode) {
        context.to(ResetPasswordScreen(
          user: user,
        ));
      } else {
        showAuthBottomSheet(context, phone: phone, onSuccess: () {
          context.to(ResetPasswordScreen(
            user: user,
          ));
        });
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //! Phone Field
        BaseTextField(
          controller: controllers[ApiStrings.phone],
          title: context.tr.phoneNumber,
          hint: '0334 xxxx xxxx',
          textInputType: TextInputType.phone,
          suffixIcon: const FieldsIconWidget(
            icon: Assets.iconsPhone,
          ),
        ),

        context.largeGap,

        //! Password Field
        BaseTextField(
          title: context.tr.password,
          textInputType: TextInputType.visiblePassword,
          controller: controllers[ApiStrings.password],
          isObscure: isObscure.value,
          validator: (value) {
            return Validations.password(value);
          },
          suffixIcon: InkWell(
            onTap: () {
              isObscure.value = !isObscure.value;
            },
            child: const FieldsIconWidget(
              icon: Assets.iconsCloseEye,
            ),
          ),
        ),

        forgetPasswordButton(context, isLoading: authController.isLoading,
            onTap: () async {
          await verify();
        }),
        context.xlLargeGap,
      ],
    );
  }

  Widget forgetPasswordButton(BuildContext context,
      {required bool isLoading, required Function() onTap}) {
    if (isLoading) {
      return const SizedBox(height: 60, width: 60, child: LoadingWidget())
          .paddingOnly(left: 10);
    } else {
      return TextButton(
          onPressed: onTap, child: Text(context.tr.forgetPassword));
    }
  }
}
