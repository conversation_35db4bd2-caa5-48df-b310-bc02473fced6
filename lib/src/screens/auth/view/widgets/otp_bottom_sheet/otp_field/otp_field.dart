import 'dart:async';

import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:xr_helper/xr_helper.dart';

StreamController<ErrorAnimationType>? _errorController;

// final _pinTheme = PinTheme(
//     shape: PinCodeFieldShape.circle,
//     borderRadius: BorderRadius.circular(5),
//     fieldHeight: 50,
//     fieldWidth: 40,
//     activeFillColor: Colors.white,
//     inactiveFillColor: Colors.white,
//     selectedFillColor: Colors.white,
//     inactiveColor: ColorManager.secondaryColor,
//     activeColor: ColorManager.secondaryColor);

class OtpField extends ConsumerWidget {
  final ValueNotifier<bool> phoneVerified;
  final Function? onSuccess;

  const OtpField({super.key, required this.phoneVerified, this.onSuccess});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider(context));

    void onCompleted(verificationCode) async {
      phoneVerified.value = await authController.verifyOTP(
        otp: verificationCode,
      );

      if (!context.mounted) return;

      if (phoneVerified.value) {
        if (onSuccess != null) {
          onSuccess!();
        } else {
          context.back();
          context.showBarMessage(context.tr.verificationSuccessful);
        }
      } else {
        context.showBarMessage(context.tr.verificationCodeIsWrong,
            isError: true);
      }
    }

    final _pinTheme = PinTheme(
      shape: PinCodeFieldShape.box,
      borderRadius: BorderRadius.circular(5),
      fieldHeight: 60,
      fieldWidth: 50,
      activeFillColor: ColorManager.grey,
      // Colors.grey[200]!,
      inactiveFillColor: ColorManager.grey,
      selectedFillColor: ColorManager.grey,
      inactiveColor: ColorManager.secondaryColor,
      activeColor: ColorManager.primaryColor,
      activeBoxShadow: [
        const BoxShadow(
          offset: Offset(0, 1),
          color: Colors.black12,
          blurRadius: 10,
        )
      ],
      borderWidth: 2,
    );

    return authController.isLoading
        ? const LoadingWidget()
        : Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 30),
            decoration: BoxDecoration(boxShadow: [
              // very light grey shadow
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 10,
                blurRadius: 20,
                offset: const Offset(0, 1),
              ),
            ]),
            child: PinCodeTextField(
              appContext: context,
              animationType: AnimationType.fade,
              length: 6,
              validator: (v) {
                if (v!.length < 5) {
                  return context.tr.completeVerification;
                } else {
                  return null;
                }
              },
              pinTheme: _pinTheme,
              cursorColor: Colors.black,
              animationDuration: const Duration(milliseconds: 300),
              enableActiveFill: true,
              backgroundColor: Colors.transparent,
              errorAnimationController: _errorController,
              keyboardType: TextInputType.number,
              // boxShadows: const [
              //   BoxShadow(
              //     offset: Offset(0, 1),
              //     color: Colors.black12,
              //     blurRadius: 10,
              //   )
              // ],
              onCompleted: onCompleted,
              onChanged: (String value) {},
            ),
          );
  }
}
