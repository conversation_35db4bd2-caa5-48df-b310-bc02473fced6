import 'package:connectify_app/src/screens/auth/view/welcome_screen/welcome_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class LetsStartButton extends StatelessWidget {
  const LetsStartButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Button(
      label: context.tr.letsStart,
      isWhiteText: false,
      textColor: ColorManager.primaryColor,
      color: ColorManager.secondaryColor,
      onPressed: () => context.toReplacement(const WelcomeScreen()),
    ).sized(
      width: context.width / 2,
    );
  }
}
