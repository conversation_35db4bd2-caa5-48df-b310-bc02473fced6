import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<ToiletModel> responseToToiletModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final toiletData = data.map((e) => ToiletModel.fromJson(e)).toList();

  return toiletData;
}

enum ToiletType { urine, stool }

enum ToiletWay { diaper, clothes, toilet }

class ToiletModel extends Equatable {
  final int? id;
  final ToiletType? toiletType;
  final ToiletWay? toiletWay;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;

  const ToiletModel(
      {this.id,
      this.toiletType,
      this.toiletWay,
      this.student,
      this.nursery,
      this.teacher});

  factory ToiletModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return ToiletModel(
      id: json[ApiStrings.id],
      toiletType: getToiletTypeFromApi(attributes[ApiStrings.toiletType] ?? ''),
      toiletWay: getToiletWayFromApi(attributes[ApiStrings.toiletWay] ?? ''),
    );
  }

  static ToiletType getToiletTypeFromApi(String value) {
    switch (value) {
      case "Urine":
        return ToiletType.urine;
      case "Stool":
        return ToiletType.stool;

      default:
        return ToiletType.urine;
    }
  }

  static ToiletWay getToiletWayFromApi(String value) {
    switch (value) {
      case "Diaper":
        return ToiletWay.diaper;
      case "Clothes":
        return ToiletWay.clothes;
      case "Toilet":
        return ToiletWay.toilet;

      default:
        return ToiletWay.diaper;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.toiletType: getToiletType(),
      ApiStrings.toiletWay: getToiletWay(),
      ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId()
    };
  }

  String getToiletType() {
    switch (toiletType) {
      case ToiletType.urine:
        return "Urine";
      case ToiletType.stool:
        return "Stool";

      default:
        return 'Urine';
    }
  }

  static ToiletType getToiletTypeValue({required int value}) {
    switch (value) {
      case 0:
        return ToiletType.urine;
      case 1:
        return ToiletType.stool;

      default:
        return ToiletType.urine;
    }
  }

  String getToiletWay() {
    switch (toiletWay) {
      case ToiletWay.diaper:
        return "Diaper";
      case ToiletWay.clothes:
        return "Clothes";
      case ToiletWay.toilet:
        return "Toilet";

      default:
        return 'Diaper';
    }
  }

  static ToiletWay getToiletWayValue({required int value}) {
    switch (value) {
      case 0:
        return ToiletWay.diaper;
      case 1:
        return ToiletWay.clothes;
      case 2:
        return ToiletWay.toilet;

      default:
        return ToiletWay.toilet;
    }
  }

  @override
  List<Object?> get props =>
      [id, toiletType, toiletWay, student, nursery, teacher];
}
