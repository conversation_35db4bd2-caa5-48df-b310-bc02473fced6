import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/toilet/repo/toilet_repo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/toilet_model.dart';

final toiletControllerProvider =
    Provider.family<ToiletController, BuildContext>((ref, context) {
  final toiletRepo = ref.watch(toiletRepoProvider);

  return ToiletController(toiletRepo: toiletRepo, context: context);
});
final toiletChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<ToiletController, BuildContext>(
        (ref, context) {
  final toiletRepo = ref.watch(toiletRepoProvider);

  return ToiletController(toiletRepo: toiletRepo, context: context);
});

final getAllToiletData =
    FutureProvider.family<List<ToiletModel>, BuildContext>((ref, context) {
  final toiletRepo = ref.watch(toiletRepoProvider);

  final toiletController =
      ToiletController(toiletRepo: toiletRepo, context: context);

  return toiletController.getToiletData();
});

class ToiletController extends BaseVM {
  final ToiletRepo toiletRepo;
  final BuildContext context;

  ToiletController({required this.toiletRepo, required this.context});

  Future<List<ToiletModel>> getToiletData() async {
    return await baseFunction(context, () async {
      final toiletData = await toiletRepo.getToiletData();

      return toiletData;
    });
  }

  //? Add Toilet ========================================================
  Future<void> addToilet(
      {required int toiletTypeValue,
      required int toiletWayValue,
      required final StudentModel? student}) async {
    return await baseFunction(context, () async {
      final getToiletTypeCurrentVal =
          ToiletModel.getToiletTypeValue(value: toiletTypeValue);
      final getToiletWayCurrentVal =
          ToiletModel.getToiletWayValue(value: toiletWayValue);

      final toilet = ToiletModel(
          toiletType: getToiletTypeCurrentVal,
          toiletWay: getToiletWayCurrentVal,
          student: student);

      await toiletRepo.addToilet(toilet: toilet);

      NotificationService.sendNotification(
        title: "Toilet Update",
        body:
            "${student?.name ?? 'Student'} has used the toilet (${toilet.toiletType?.name} - ${toilet.toiletWay?.name}).",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Toilet Update",
        body:
            "${student?.name ?? 'Student'} has used the toilet (${toilet.toiletType?.name} - ${toilet.toiletWay?.name}).",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }
}
