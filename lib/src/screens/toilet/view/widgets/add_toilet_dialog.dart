import 'dart:developer';

import 'package:connectify_app/src/screens/food/controller/food_controller.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/toilet/controller/toilet_controller.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/radio_list_tile_widget/radio_list_tile_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import 'add_toilet_widgets.dart';

class AddFoodDialog extends StatelessWidget {
  const AddFoodDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
  }
}

Future<void> showAddToiletDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final toiletChangeNotifierCtrl =
              ref.watch(toiletChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.student: useState<StudentModel?>(null),
            ApiStrings.toiletType: useState<int>(0),
            ApiStrings.toiletWay: useState<int>(0),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addToilet() async {
            if (!formKey.value.currentState!.validate()) return;
            await toiletChangeNotifierCtrl.addToilet(
                toiletTypeValue: valueNotifiers[ApiStrings.toiletType]?.value as int,
                toiletWayValue: valueNotifiers[ApiStrings.toiletWay]?.value as int,
                student:
                    valueNotifiers[ApiStrings.student]?.value as StudentModel?);
          }

          return AlertDialogWidget(
              header: context.tr.toilet,
              isLoading: toiletChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddToiletWidgets(
                  valueNotifiers: valueNotifiers,
                ),
              ),
              onConfirm: () async => await addToilet());
        },
      );
    },
  );
}
