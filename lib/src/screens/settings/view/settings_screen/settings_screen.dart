import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/settings/view/settings_screen/widgets/profile_dialog/profile_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/app_settings/controller/settings_controller.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/language_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider(context));
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);
    final settingsController = ref.watch(settingsControllerProvider);

    return Center(
      child: Column(
        children: [
          context.largeGap,

          //! Profile List Tile ========================================
          ListTile(
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return const ProfileDialog();
                },
              );
            },
            title: Text(
              context.tr.profile,
              style: context.title.copyWith(fontWeight: FontWeight.bold),
            ),
            leading: const Icon(
              Icons.person,
              color: ColorManager.primaryColor,
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: ColorManager.grey,
            ),
          ),

          context.largeGap,

          //! Change Language List Tile ========================================
          ListTile(
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return const LanguageDialog();
                },
              );
            },
            title: Text(
              context.tr.changeLanguage,
              style: context.title.copyWith(fontWeight: FontWeight.bold),
            ),
            leading: const Icon(
              Icons.language,
              color: ColorManager.primaryColor,
            ),
            trailing: Text(
              settingsController.locale.languageCode == 'en'
                  ? context.tr.english
                  : context.tr.arabic,
              style: context.subTitle.copyWith(
                color: ColorManager.primaryColor,
              ),
            ),
          ),

          context.largeGap,

          //! Change Password List Tile ========================================
          ListTile(
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return const ProfileDialog(
                    changePassword: true,
                  );
                },
              );
            },
            title: Text(
              context.tr.changePassword,
              style: context.title.copyWith(fontWeight: FontWeight.bold),
            ),
            leading: const Icon(
              Icons.lock,
              color: ColorManager.primaryColor,
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: ColorManager.grey,
            ),
          ),

          context.largeGap,

          //! Delete Account ========================================
          ListTile(
            onTap: () {
              showDialog(
                  context: context,
                  builder: (context) => BaseDeleteDialog(
                      description: context.tr.areYouSureToDeleteYourAccount,
                      onConfirm: () async {
                        await authController.updatePassword(controllers: {
                          'password': TextEditingController(text: '000000'),
                        });
                        await authController.logout();
                      }));
            },
            title: Text(
              context.tr.deleteAccount,
              style: context.title.copyWith(fontWeight: FontWeight.bold),
            ),
            leading: const Icon(
              Icons.delete,
              color: ColorManager.errorColor,
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: ColorManager.grey,
            ),
          ),

          const Spacer(),

          Text(context.tr.haveAnyQuestionsContactUs,
              style: context.title, textAlign: TextAlign.center),

          context.largeGap,

          //! Contact US ========================================
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              CircleAvatar(
                backgroundColor: ColorManager.lightBlue,
                radius: 30.r,
                child: IconButton(
                  icon: const Icon(
                    Icons.web,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    launchUrl(Uri.parse('https://connectifyapp.org'));
                  },
                ),
              ),
              CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                radius: 30.r,
                child: IconButton(
                  icon: const Icon(
                    Icons.facebook,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    launchUrl(Uri.parse(
                        'https://www.facebook.com/ConnectifyApps?mibextid=ZbWKwL'));
                  },
                ),
              ),
            ],
          ),

          const Spacer(
            flex: 2,
          ),

          //! Logout Button ========================================
          Button(
            label: context.tr.logout,
            color: ColorManager.primaryColor,
            onPressed: () async {
              await authController.logout();
              bottomNavCtrl.changeIndex(0);
            },
          ).sized(
            height: 40.h,
            width: context.width * .9,
          ),

          context.largeGap,
        ],
      ),
    );
  }
}
