import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class DashBoardExpansionTileWidget extends StatelessWidget {
  final List<Widget> children;
  final String title;

  const DashBoardExpansionTileWidget({
    super.key,
    required this.children,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          child: ExpansionTile(
            expandedAlignment: Alignment.topLeft,
            shape: InputBorder.none,
            backgroundColor: ColorManager.primaryColor,
            collapsedBackgroundColor: ColorManager.primaryColor,
            initiallyExpanded: true,
            title: Text(
              title,
              style: context.title.copyWith(color: Colors.white),
            ),
            trailing: const Icon(
              Icons.more_vert,
              color: ColorManager.white,
            ),
            children: children,
          ),
        )
      ],
    );
  }
}
