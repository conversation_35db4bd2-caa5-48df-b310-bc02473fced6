// import 'package:connectify_app/src/screens/student/models/student_model.dart';
// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:connectify_app/src/shared/widgets/drop_downs/student_drop_down.dart';
// import 'package:flutter/material.dart';
//
// class TitleAndDropDownField extends StatelessWidget {
//   final ValueNotifier<StudentModel?> selectedStudent;
//
//   const TitleAndDropDownField({super.key, required this.selectedStudent});
//
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         Expanded(
//           child: StudentDropDown(
//             selectedStudent: selectedStudent,
//             label: context.tr.studentAndClass,
//             withLoading: false,
//           ),
//         ),
//         // context.largeGap,
//         // Expanded(
//         //   child: Column(
//         //     crossAxisAlignment: CrossAxisAlignment.start,
//         //     children: [
//         //       Text(
//         //         context.tr.selectPeriod,
//         //         style: context.hint,
//         //       ),
//         //       context.smallGap,
//         //       BaseDropDown(
//         //           onChanged: (value) {},
//         //           data: [],
//         //           label: context.tr.currentMonth,
//         //           selectedValue: ''),
//         //     ],
//         //   ),
//         // ),
//       ],
//     );
//   }
// }
