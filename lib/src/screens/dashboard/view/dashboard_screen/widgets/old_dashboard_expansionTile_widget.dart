// import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class DashBoardExpansionTileWidget extends StatelessWidget {
//   final List<Widget> children;
//   final int index;
//   final bool isFromClass;
//
//   const DashBoardExpansionTileWidget(
//       {super.key, required this.children, required this.index,  this.isFromClass = false});
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         ClipRRect(
//           borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
//           child: ExpansionTile(
//             expandedAlignment: Alignment.topLeft,
//             shape: InputBorder.none,
//             backgroundColor: ColorManager.primaryColor,
//             collapsedBackgroundColor: ColorManager.primaryColor,
//             initiallyExpanded: true,
//             title: isFromClass ? Text(
//               classSelectedTitle(index, context),
//               style: context.title.copyWith(color: Colors.white),
//             ) : Text(
//               selectedTitle(index, context),
//               style: context.title.copyWith(color: Colors.white),
//             ),
//             trailing: const Icon(
//               Icons.more_vert,
//               color: ColorManager.white,
//             ),
//             children: children,
//           ),
//         )
//       ],
//     );
//   }
//
//   String classSelectedTitle(int index, BuildContext context) {
//     switch (index) {
//       case 0:
//         return context.tr.attendanceChart;
//
//       case 1:
//         return context.tr.activitiesCompleted;
//     }
//     return '';
//   }
//   String selectedTitle(int index, BuildContext context) {
//     switch (index) {
//       case 0:
//         return context.tr.attendanceChart;
//       case 1:
//         return context.tr.incomeChart;
//       case 2:
//         return context.tr.billsChart;
//       case 3:
//         return context.tr.activitiesCompleted;
//     }
//     return '';
//   }
// }
