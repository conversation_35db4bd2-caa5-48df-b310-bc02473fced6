import 'package:connectify_app/src/screens/financial/controllers/invoices_controller.dart';
import 'package:connectify_app/src/screens/financial/models/invoices_model.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/dashboard_expansionTile_widget.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:xr_helper/xr_helper.dart';

class InvoicesCharts extends HookConsumerWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const InvoicesCharts({super.key, required this.valueNotifiers});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMonth = valueNotifiers[ApiStrings.selectedInvoiceMonth]
        as ValueNotifier<String>;

    final params = (context, selectedMonth.value);

    final getInvoicesProvider =
        ref.watch(getInvoicesDataByMonthProvider(params));

    final invoices = valueNotifiers[ApiStrings.invoices]
        as ValueNotifier<List<InvoicesModel>>;
    final isLoading =
        valueNotifiers[ApiStrings.isLoadingInvoice] as ValueNotifier<bool>;

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        invoices.value = getInvoicesProvider.when(
          data: (data) {
            isLoading.value = false;
            return data
                .where((element) =>
                    element.createdAt.formatDateTimeToMonth ==
                    selectedMonth.value)
                .toList();
          },
          loading: () {
            isLoading.value = true;
            return <InvoicesModel>[];
          },
          error: (error, stack) {
            isLoading.value = false;
            return <InvoicesModel>[];
          },
        );
      });

      return () {};
    }, [getInvoicesProvider, selectedMonth.value]);

    return DashBoardExpansionTileWidget(
      title: context.tr.invoicesChart,
      children: [
        BaseContainer(
          radius: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              context.mediumGap,

              if (isLoading.value)
                const Padding(
                  padding: EdgeInsets.only(top: AppSpaces.mediumPadding),
                  child: LoadingWidget(
                    loadingType: LoadingType.linear,
                  ),
                )
              else ...[
                BaseDropDown(
                  onChanged: (value) {
                    selectedMonth.value = value;
                  },
                  data: AppConsts.months,
                  label: context.tr.month,
                  icon: const Icon(Icons.calendar_today),
                  selectedValue: selectedMonth.value,
                ),
              ],

              context.mediumGap,

              //! Chart Widget (Stacked Column Chart)
              SfCartesianChart(
                primaryXAxis: CategoryAxis(),
                series: [
                  StackedColumnSeries<InvoicesModel, String>(
                    width: 0.4,
                    dataSource: invoices.value,
                    xValueMapper: (InvoicesModel invoice, _) => invoice.name,
                    yValueMapper: (InvoicesModel invoice, _) => invoice.amount,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
