import 'package:connectify_app/src/screens/financial/controllers/bills_controller.dart';
import 'package:connectify_app/src/screens/financial/models/bill_model.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/dashboard_expansionTile_widget.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:xr_helper/xr_helper.dart';

class BillsCharts extends HookConsumerWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const BillsCharts({super.key, required this.valueNotifiers});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMonth =
        valueNotifiers[ApiStrings.selectedBillMonth] as ValueNotifier<String>;

    final params = (context, selectedMonth.value);

    final getBillsProvider = ref.watch(getBillsDataByMonthProvider(params));

    final bills =
        valueNotifiers[ApiStrings.bills] as ValueNotifier<List<BillModel>>;
    final isLoading =
        valueNotifiers[ApiStrings.isLoadingBill] as ValueNotifier<bool>;

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        bills.value = getBillsProvider.when(
          data: (data) {
            isLoading.value = false;
            return data
                .where((element) =>
                    element.createdAt.formatDateTimeToMonth ==
                    selectedMonth.value)
                .toList();
          },
          loading: () {
            isLoading.value = true;
            return <BillModel>[];
          },
          error: (error, stack) {
            isLoading.value = false;
            return <BillModel>[];
          },
        );
      });
      return () {};
    }, [getBillsProvider, selectedMonth.value]);

    return DashBoardExpansionTileWidget(
      title: context.tr.billsChart,
      children: [
        BaseContainer(
          radius: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              context.mediumGap,

              if (isLoading.value)
                const Padding(
                  padding: EdgeInsets.only(top: AppSpaces.mediumPadding),
                  child: LoadingWidget(
                    loadingType: LoadingType.linear,
                  ),
                )
              else ...[
                BaseDropDown(
                  onChanged: (value) {
                    selectedMonth.value = value;
                  },
                  data: AppConsts.months,
                  label: context.tr.month,
                  icon: const Icon(Icons.calendar_today),
                  selectedValue: selectedMonth.value,
                ),
              ],

              context.mediumGap,

              //! Chart Widget (Stacked Column Chart)
              SfCartesianChart(
                primaryXAxis: CategoryAxis(),
                series: [
                  StackedColumnSeries<BillModel, String>(
                    width: 0.4,
                    dataSource: bills.value,
                    xValueMapper: (BillModel bill, _) => bill.name,
                    yValueMapper: (BillModel bill, _) => bill.amount,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
