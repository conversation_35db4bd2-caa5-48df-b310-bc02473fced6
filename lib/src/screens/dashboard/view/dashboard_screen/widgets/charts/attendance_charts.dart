import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/dashboard/view/dashboard_screen/widgets/dashboard_expansionTile_widget.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/student_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class AttendanceCharts extends HookConsumerWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const AttendanceCharts({
    super.key,
    required this.valueNotifiers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedStudent = valueNotifiers[ApiStrings.selectedAttendanceStudent]
        as ValueNotifier<StudentModel?>;

    final selectedMonth = valueNotifiers[ApiStrings.selectedAttendanceMonth]
        as ValueNotifier<String>;

    final params = (context, selectedMonth.value, selectedStudent.value?.id);

    final getAttendanceProvider =
        ref.watch(getAttendanceDataByDateProvider(params));

    final attendances = getAttendanceProvider.when(
      data: (data) {
        return data;
      },
      loading: () {
        return <AttendanceModel>[];
      },
      error: (error, stack) {
        return <AttendanceModel>[];
      },
    );

    final allAttendancesDates = attendances
        .map((attendance) => attendance.attendanceDate)
        .toList()
        .toSet();

    final attendedCount = attendances
        .where((attendance) => attendance.attendanceDate.isNotEmpty)
        // .where((element) => element.student?.id == selectedStudent.value?.id)
        .length;

    final absentCount = allAttendancesDates.length - attendedCount;

    final attendanceRate = attendedCount /
        (allAttendancesDates.isEmpty ? 1 : allAttendancesDates.length);

    return DashBoardExpansionTileWidget(
      title: context.tr.attendanceChart,
      children: [
        BaseContainer(
          radius: 0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              context.mediumGap,
              StudentDropDown(
                selectedStudent: selectedStudent,
                label: context.tr.studentAndClass,
                studentsData: valueNotifiers[ApiStrings.students]?.value
                    as List<StudentModel>,
              ),
              context.mediumGap,
              BaseDropDown(
                onChanged: (value) {
                  selectedMonth.value = value;
                },
                data: AppConsts.months,
                label: context.tr.month,
                icon: const Icon(Icons.calendar_today),
                selectedValue: selectedMonth.value,
              ),
              context.xlLargeGap,
              SizedBox(
                height: 100,
                width: 100,
                child: CircularProgressIndicator(
                  strokeWidth: 35,
                  backgroundColor: ColorManager.purple,
                  value: attendanceRate,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                      ColorManager.lightBlue),
                ),
              ),
              context.xlLargeGap,
              bottomSectionWidget(context, attendedCount, absentCount),
            ],
          ),
        ),
      ],
    );
  }

  Widget bottomSectionWidget(
      BuildContext context, int attendedCount, int absentCount) {
    return Row(
      children: [
        Row(
          children: [
            CircleAvatar(
              backgroundColor: ColorManager.purple,
              radius: 7.r,
            ),
            context.smallGap,
            Text('${context.tr.absent} ($absentCount)'),
          ],
        ),
        context.largeGap,
        Row(
          children: [
            CircleAvatar(
              backgroundColor: ColorManager.lightBlue,
              radius: 7.r,
            ),
            context.smallGap,
            Text('${context.tr.attended} ($attendedCount)'),
          ],
        ),
      ],
    );
  }
}
