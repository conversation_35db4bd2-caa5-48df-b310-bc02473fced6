import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

// * =========================================================

final sleepRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return SleepRepo(networkApiServices);
});

//? ========================================================

class SleepRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  SleepRepo(this._networkApiServices);

//? get Sleep Data ========================================================
  Future<List<SleepModel>> getSleepData() async {
    return await baseFunction(() async {
      final response = _networkApiServices.getResponse(ApiEndpoints.sleep);

      final sleepData = await compute(responseToSleepModelList, response);

      return sleepData;
    });
  }

  //? Add Sleep ========================================================
  Future<void> addSleep({required SleepModel sleep}) async {
    return await baseFunction(() async {
      final sleepData = await _networkApiServices
          .postResponse(ApiEndpoints.editDeleteSleeps, body: sleep.toJson());

      addNewHistory(
          historyModel: HistoryModel(
        historyType: HistoryType.sleep,
        sleep: SleepModel(
          id: sleepData['data']['id'],
        ),
        student: sleep.student,
      ));
    });
  }
}
