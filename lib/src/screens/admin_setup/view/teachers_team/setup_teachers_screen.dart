import 'package:connectify_app/src/screens/admin_setup/view/add_students/setup_students_screen.dart';
import 'package:connectify_app/src/screens/teacher/controllers/teacher_controller.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/widgets/teachers_grid_view.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SetupTeacherScreen extends HookConsumerWidget {
  const SetupTeacherScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherController = ref.watch(getTeacherDataProvider(context));

    return SetupAppBar(
      nextWidget: const SetupStudentScreen(),
      nextIndex: 2,
      child: Column(
        children: [
          context.largeGap,
          Text(
            context.tr.addNurseryTeam,
            style: context.subTitle,
          ),
          context.mediumGap,
          teacherController.get(
              data: (teachers) => TeachersGridView(
                    isSignUp: true,
                    teachers: teachers,
                    navigateWidget: const SetupTeacherScreen(),
                  )),
        ],
      ).scroll(),
    );
  }
}
