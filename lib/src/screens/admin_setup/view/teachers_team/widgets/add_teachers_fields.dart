import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:connectify_app/src/shared/widgets/select_contact/select_icon_icon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/data/remote/api_strings.dart';

class AddTeachersFields extends ConsumerWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<List<ClassModel>?> selectedClass;
  final List<int?>? classId;
  final bool viewOnlyClasses;

  const AddTeachersFields({
    super.key,
    required this.classId,
    required this.controllers,
    required this.selectedClass,
    this.viewOnlyClasses = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (viewOnlyClasses) {
      return MultiClassDropDown(
          selectedClasses: selectedClass, classIds: classId);
    }

    return Column(
      children: [
        //! Name
        BaseTextField(
          controller: controllers[ApiStrings.name],
          title: context.tr.teacherName,
          textInputType: TextInputType.text,
          validator: (value) {
            if (value!.isEmpty || value.length < 3) {
              return "Please enter a valid name";
            }

            return null;
          },
        ),

        context.largeGap,

        BaseTextField(
          suffixIcon: SelectContactIcon(
            onSelected: (contact) {
              controllers[ApiStrings.phone]!.text = normalizePhoneNumber(
                contact.phoneNumbers?.firstOrNull,
              );
            },
          ),
          controller: controllers[ApiStrings.phone],
          title: context.tr.phoneNumber,
          textInputType: TextInputType.phone,
        ),

        //! Class Drop Down
        context.mediumGap,

        MultiClassDropDown(selectedClasses: selectedClass, classIds: classId),

        context.largeGap,

        //! Description
        BaseTextField(
          controller: controllers[ApiStrings.jobTitle],
          title: context.tr.description,
          textInputType: TextInputType.text,
          isRequired: false,
          maxLines: 4,
        ),
      ],
    );
  }
}
