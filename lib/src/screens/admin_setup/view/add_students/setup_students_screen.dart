import 'package:connectify_app/src/screens/admin_setup/view/Activities/setup_activities_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../student/controllers/student_controller.dart';
import '../../../student/view/student_screen/widgets/students_grid_view.dart';

class SetupStudentScreen extends ConsumerWidget {
  const SetupStudentScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentController = ref.watch(getAllStudentsProvider(context));

    return SetupAppBar(
        nextWidget: const SetupActivitiesScreen(),
        nextIndex: 3,
        child: Column(
          children: [
            context.largeGap,
            Text(
              context.tr.addStudents,
              style: context.subTitle,
            ),
            context.smallGap,
            studentController.get(
              data: (student) => StudentGridView(
                fromClassDetails: false,
                isSignUp: true,
                students: student,
                navigateWidget: const SetupStudentScreen(),
              ),
            ),
          ],
        ).scroll());
  }
}
