import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class CongratulationsScreen extends StatelessWidget {
  const CongratulationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(Assets.imagesCongrats),
            context.smallGap,
            context.smallGap,
            Text(
              context.tr.congratulations,
              style: context.whiteHeadLine,
            ),
            context.smallGap,
            context.smallGap,
            Text(
              'Kinder Daycare and Nursery',
              style: context.whiteHint,
            ),
            context.xlLargeGap,
            Button(
                textColor: ColorManager.primaryColor,
                haveElevation: true,
                isWhiteText: false,
                color: ColorManager.white,
                label: context.tr.letsDoAGreatJob,
                onPressed: () {
                  GetStorageService.setLocalData(
                      key: LocalKeys.loggedIn, value: true);
                  context.to(const MainScreen());
                }).paddingSymmetric(horizontal: AppSpaces.largePadding)
          ],
        ),
      ),
    );
  }
}
