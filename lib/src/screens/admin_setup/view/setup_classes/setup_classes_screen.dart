import 'package:connectify_app/src/screens/admin_setup/view/teachers_team/setup_teachers_screen.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/view/add_class/add_class_dialog.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../class/view/classes_screen/widgets/classes_list.dart';

class SetupClassesScreen extends HookConsumerWidget {
  const SetupClassesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // !-----------------------------------------------------

    final classController = ref.watch(getClassDataProvider(context));

    return SetupAppBar(
      nextWidget: const SetupTeacherScreen(),
      nextIndex: 1,
      child: Column(
        children: [
          context.largeGap,
          //! Text Setup Your Classes
          Text(
            context.tr.setupYourClasses,
            style: context.subTitle,
          ),

          context.largeGap,

          //! Add Rectangle Widget
          const AddClassDialog(
            navigateWidget: SetupClassesScreen(),
          ),

          context.mediumGap,

          //! Classes List
          classController.get(data: (classes) {
            if (classController.isRefreshing) {
              return const LoadingWidget();
            }
            return ClassesList(
              isSignUp: true,
              classes: classes,
            );
          }),
        ],
      ).scroll(),
    );
  }
}
