import 'package:connectify_app/src/screens/admin_setup/view/congratulation_screen.dart';
import 'package:connectify_app/src/screens/admin_setup/view/setup_classes/setup_classes_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../Activities/view/activiy/view/widgets/activities_list.dart';
import 'widgets/add_activity_dialog.dart';

class SetupActivitiesScreen extends HookConsumerWidget {
  const SetupActivitiesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SetupAppBar(
      nextWidget: const CongratulationsScreen(),
      child: Column(
        children: [
          context.largeGap,
          Text(
            context.tr.nurseryActivities,
            style: context.subTitle,
          ),

          context.largeGap,

          //! Add Activity
          const AddActivityDialog(
            navigateWidget: SetupActivitiesScreen(),
          ),

          context.mediumGap,

          //! Activities List
          const ActivitiesList(
            navigateWidget: SetupActivitiesScreen(),
            isSignUp: true,

          ),
        ],
      ).scroll(),
    );
  }
}
