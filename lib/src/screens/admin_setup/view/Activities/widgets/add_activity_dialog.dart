import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/Activities/controllers/activities_controller.dart';
import 'package:connectify_app/src/screens/Activities/models/activity_model.dart';
import 'package:connectify_app/src/screens/admin_setup/view/Activities/widgets/setup_activites_fields.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class AddActivityDialog extends HookConsumerWidget {
  final bool fromClassDetails;
  final Widget navigateWidget;

  const AddActivityDialog(
      {super.key, this.fromClassDetails = false, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    void clearData() {
      ref.watch(mediaPickerControllerProvider).clearFiles();
    }

    return AddRectangleWidget(
        isClasses: fromClassDetails,
        onTap: () {
          showAddEditActivityDialog(context, navigateWidget: navigateWidget)
              .then((value) => clearData());
        });
  }
}

Future<void> showAddEditActivityDialog(context,
    {ActivityModel? activity,
    required Widget navigateWidget,
    bool fromClassDetails = false}) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final controllers = {
            ApiStrings.name: useTextEditingController(text: activity?.name),
            ApiStrings.description:
                useTextEditingController(text: activity?.description),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          //!-----------------------------------------------------

          final activityController =
              ref.watch(activityChangeNotifierProvider(context));
          final filePath = ref.watch(mediaPickerControllerProvider).filePath;

          //!-----------------------------------------------------

          final isEdit = activity != null;

          Future<void> addEditActivity() async {
            if (isEdit) {
              await activityController.editActivity(
                  controllers: controllers,
                  id: activity.id!,
                  pickedImage: filePath);

              if (!context.mounted) return;
              ref.watch(mediaPickerControllerProvider).clearFiles();

              context.toReplacement(navigateWidget);
              context.showBarMessage(context.tr.editSuccessfully);
            } else {
              await activityController.addActivity(
                  controllers: controllers, pickedImage: filePath);

              if (!context.mounted) return;
              ref.watch(mediaPickerControllerProvider).clearFiles();

              context.toReplacement(navigateWidget);
              context.showBarMessage(context.tr.addedSuccessfully);
            }
          }

          return AlertDialogWidget(
            iconPath: Assets.imagesActivities,
            header: isEdit ? context.tr.editActivity : context.tr.addActivity,
            networkImage: activity?.image?.url ?? '',
            isLoading: activityController.isLoading,
            child: Form(
              key: formKey.value,
              child: SetupActivitiesFields(
                controllers: controllers,
              ),
            ),
            onConfirm: () async {
              if (!formKey.value.currentState!.validate()) return;

              await addEditActivity();
            },
          );
        },
      );
    },
  );
}
