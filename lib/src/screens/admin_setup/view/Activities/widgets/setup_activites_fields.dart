import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/data/remote/api_strings.dart';

class SetupActivitiesFields extends StatelessWidget {
  final Map<String, TextEditingController> controllers;

  const SetupActivitiesFields({
    super.key,
    required this.controllers,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //! Name
        BaseTextField(
          controller: controllers[ApiStrings.name],
          title: context.tr.activityName,
          textInputType: TextInputType.text,
        ),

        context.largeGap,

        //! Description
        BaseTextField(
          controller: controllers[ApiStrings.description],
          title: context.tr.activityDescription,
          textInputType: TextInputType.text,
          isRequired: false,
          maxLines: 4,
        ),
      ],
    );
  }
}
