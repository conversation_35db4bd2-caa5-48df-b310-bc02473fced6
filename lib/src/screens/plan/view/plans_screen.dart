import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/base_date_filter_widget.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/plan/controller/plan_controller.dart';
import 'package:connectify_app/src/screens/plan/view/add_plan_screen.dart';
import 'package:connectify_app/src/screens/plan/view/widgets/plan_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/widgets/shared_widgets.dart';
import '../../auth/models/user_model.dart';
import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class PlansScreen extends HookConsumerWidget {
  const PlansScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState<DateTime>(DateTime.now());

    final params = (context, selectedDate.value.formatToMonthName);

    final plansData = ref.watch(getPlansDataByMonthProvider(params));

    final selectedClass = useState<ClassModel?>(null);

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Scaffold(
        appBar: MainAppBar(
          isBackButton: true,
          title: context.tr.plans,
          iconPath: '',
        ),
        body: Column(
          children: [
            if (const UserModel().isAdmin)
              Padding(
                padding: const EdgeInsets.all(AppSpaces.largePadding),
                child: AddRectangleWidget(
                    title: context.tr.addPlan,
                    onTap: () {
                      context.to(const AddPlanScreen());
                    }),
              ),

            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpaces.largePadding,
                vertical: AppSpaces.mediumPadding,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ClassDropDown(
                      selectedClass: selectedClass,
                      classesData: const UserModel().isTeacher
                          ? (const UserModel().currentUser.classes ?? [])
                          : [],
                    ),
                  ),
                  context.smallGap,
                  if (selectedClass.value != null)
                    TextButton(
                      onPressed: () {
                        selectedClass.value = null;
                      },
                      child: Text(context.tr.clear,
                          style: context.labelMedium.copyWith(
                            color: Colors.red,
                          )),
                    ),
                ],
              ),
            ),

            BaseDateFilterWidget(
              date: selectedDate.value,
              onNext: () {
                selectedDate.value = DateTime(
                  selectedDate.value.year,
                  selectedDate.value.month + 1,
                );
              },
              onPrevious: () {
                selectedDate.value = DateTime(
                  selectedDate.value.year,
                  selectedDate.value.month - 1,
                );
              },
            ),

            // Plans list
            Expanded(
              child: plansData.get(
                data: (plans) {
                  if (plans.isEmpty) {
                    return Center(
                      child: Text(
                        context.tr.noPlansForThisMonth,
                        style: context.subHeadLine,
                      ),
                    );
                  }

                  final filteredTeacherClassesPlans = const UserModel()
                          .isTeacher
                      ? plans.where((plan) {
                          return const UserModel().currentUser.classes?.any(
                                  (classModel) =>
                                      classModel.id == plan.classModel?.id) ??
                              false;
                        }).toList()
                      : plans;

                  final filteredPlans =
                      filteredTeacherClassesPlans.where((plan) {
                    if (selectedClass.value == null) return true;
                    return plan.classModel?.id == selectedClass.value!.id;
                  }).toList();

                  return BaseList(
                    padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                    data: filteredPlans,
                    itemBuilder: (plan, index) => PlanCard(
                      plan: plan,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
