import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

/// A widget for picking and displaying an image for a section.

class PlanFields extends HookConsumerWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<ClassModel?> selectedClass;
  final ValueNotifier<List<Map<String, TextEditingController>>>
      sectionControllers;
  final ValueNotifier<List<BaseMediaModel?>> sectionImages;

  const PlanFields({
    super.key,
    required this.controllers,
    required this.selectedClass,
    required this.sectionControllers,
    required this.sectionImages,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final focusNode = useFocusNode();
    final networkApiServices = ref.watch(networkServiceProvider);
    final mediaController = ref.watch(mediaPickerControllerProvider);
    final isLoading = useState<Map<int, bool>>({});

    // Use a map to store section images by index
    final pickedImageMap = useState<Map<int, BaseMediaModel?>>({});

    // Initialize the pickedImageMap with existing section images
    useEffect(() {
      for (int i = 0; i < sectionImages.value.length; i++) {
        if (sectionImages.value[i] != null) {
          pickedImageMap.value[i] = sectionImages.value[i];
        }
      }
      return null;
    }, []);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Class dropdown
          ClassDropDown(
            selectedClass: selectedClass,
            selectFirstClass: false,
          ),

          context.fieldsGap,

          // Sections header
          Text(
            context.tr.sections,
            style: context.labelLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          context.smallGap,

          // Sections list
          StatefulBuilder(builder: (context, setState) {
            return ValueListenableBuilder<
                List<Map<String, TextEditingController>>>(
              valueListenable: sectionControllers,
              builder: (context, controllers, child) {
                return Column(
                  children: [
                    ...controllers.asMap().entries.map((entry) {
                      final index = entry.key;
                      final sectionController = entry.value;
                      final isLastItem = index == controllers.length - 1;

                      // Ensure sectionImages has enough slots for all sections
                      while (sectionImages.value.length <= index) {
                        sectionImages.value = [...sectionImages.value, null];
                      }

                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section header
                            Row(
                              children: [
                                Text(
                                  '${context.tr.section} ${index + 1}',
                                  style: context.labelMedium.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const Spacer(),
                                IconButton(
                                  icon: const Icon(Icons.remove_circle,
                                      color: Colors.red),
                                  onPressed: () async {
                                    // Remove the section and its associated image
                                    final newSectionControllers = List<
                                            Map<String,
                                                TextEditingController>>.from(
                                        sectionControllers.value)
                                      ..removeAt(index);

                                    // Delete the image from the server if it exists
                                    if (sectionImages.value.length > index &&
                                        sectionImages.value[index]?.id !=
                                            null) {
                                      await networkApiServices.deleteImage(
                                          sectionImages.value[index]!.id
                                              .toString());
                                    }

                                    // Create new sectionImages list without the deleted section image
                                    final newSectionImages =
                                        List<BaseMediaModel?>.from(
                                            sectionImages.value);
                                    if (newSectionImages.length > index) {
                                      newSectionImages.removeAt(index);
                                    }

                                    // Remove the image from pickedImageMap
                                    pickedImageMap.value =
                                        Map<int, BaseMediaModel?>.from(
                                            pickedImageMap.value);
                                    pickedImageMap.value.remove(index);

                                    // Reindex the remaining images in the map
                                    final newPickedImageMap =
                                        <int, BaseMediaModel?>{};
                                    pickedImageMap.value.forEach((key, value) {
                                      if (key > index) {
                                        newPickedImageMap[key - 1] = value;
                                      } else if (key < index) {
                                        newPickedImageMap[key] = value;
                                      }
                                    });
                                    pickedImageMap.value = newPickedImageMap;

                                    // Update the controllers and images
                                    sectionControllers.value =
                                        newSectionControllers;
                                    sectionImages.value = newSectionImages;

                                    setState(() {});
                                  },
                                ),
                              ],
                            ),

                            context.smallGap,

                            // Section title
                            BaseTextField(
                              focusNode: isLastItem ? focusNode : null,
                              controller: sectionController['title']!,
                              isRequired: false,
                              label: context.tr.sectionTitle,
                              hint: context.tr.enterSectionTitle,
                              onChanged: (value) {
                                // No need to update a separate list since controllers hold the values
                              },
                            ),

                            context.fieldsGap,

                            // Section description
                            BaseTextField(
                              controller: sectionController['description']!,
                              isRequired: false,
                              label: context.tr.sectionDescription,
                              hint: context.tr.enterSectionDescription,
                              textInputType: TextInputType.multiline,
                              maxLines: 3,
                              onChanged: (value) {
                                // No need to update a separate list since controllers hold the values
                              },
                            ),

                            context.fieldsGap,

                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  context.tr.sectionImage,
                                  style: context.labelMedium.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                context.smallGap,
                                GestureDetector(
                                  onTap: () async {
                                    // Set loading state for this specific section
                                    isLoading.value = {
                                      ...isLoading.value,
                                      index: true
                                    };
                                    setState(() {});

                                    final result =
                                        await mediaController.pickFile();

                                    if (result != null) {
                                      final files = result.files;

                                      if (files.isNotEmpty) {
                                        final uploadedMedia =
                                            await networkApiServices
                                                .uploadFiles(
                                          filePaths: [files.first.path],
                                        );

                                        if (uploadedMedia != null &&
                                            uploadedMedia.isNotEmpty) {
                                          final uploadedFile = BaseMediaModel(
                                            id: uploadedMedia.first['id'],
                                            url: uploadedMedia.first['url'],
                                          );

                                          // Update the image for this specific section in both maps
                                          pickedImageMap.value = {
                                            ...pickedImageMap.value,
                                            index: uploadedFile,
                                          };

                                          final newImages =
                                              List<BaseMediaModel?>.from(
                                                  sectionImages.value);
                                          while (newImages.length <= index) {
                                            newImages.add(null);
                                          }
                                          newImages[index] = uploadedFile;
                                          sectionImages.value = newImages;
                                        }
                                      }
                                    }

                                    // Clear loading state for this section
                                    isLoading.value = {
                                      ...isLoading.value,
                                      index: false
                                    };
                                    setState(() {});
                                  },
                                  child: Container(
                                    height: 150,
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: Colors.grey.shade300),
                                      borderRadius: BorderRadius.circular(
                                          AppRadius.baseContainerRadius),
                                    ),
                                    child: isLoading.value[index] == true
                                        ? const Center(
                                            child: CircularProgressIndicator())
                                        : (pickedImageMap.value[index]?.url !=
                                                    null ||
                                                (sectionImages.value.length >
                                                        index &&
                                                    sectionImages.value[index]
                                                            ?.url !=
                                                        null))
                                            ? ClipRRect(
                                                borderRadius: BorderRadius
                                                    .circular(AppRadius
                                                        .baseContainerRadius),
                                                child: Image.network(
                                                  pickedImageMap
                                                          .value[index]?.url ??
                                                      sectionImages
                                                          .value[index]?.url ??
                                                      '',
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error,
                                                          stackTrace) =>
                                                      const BaseCachedImage(
                                                    AppConsts
                                                        .activityPlaceholder,
                                                  ),
                                                ),
                                              )
                                            : Center(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const Icon(
                                                        Icons
                                                            .add_photo_alternate_outlined,
                                                        size: 40),
                                                    const SizedBox(height: 8),
                                                    Text(context.tr.addImage),
                                                  ],
                                                ),
                                              ),
                                  ),
                                ),
                                if (pickedImageMap.value[index] != null ||
                                    (sectionImages.value.length > index &&
                                        sectionImages.value[index] != null))
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: TextButton.icon(
                                      onPressed: () async {
                                        // Delete the image from server if it exists
                                        final imageToDelete =
                                            pickedImageMap.value[index] ??
                                                sectionImages.value[index];
                                        if (imageToDelete?.id != null) {
                                          await networkApiServices.deleteImage(
                                              imageToDelete!.id.toString());
                                        }

                                        // Remove the image from the maps
                                        pickedImageMap.value = {
                                          ...pickedImageMap.value
                                        }..remove(index);

                                        final newImages =
                                            List<BaseMediaModel?>.from(
                                                sectionImages.value);
                                        if (newImages.length > index) {
                                          newImages[index] = null;
                                          sectionImages.value = newImages;
                                        }

                                        setState(() {});
                                      },
                                      icon: const Icon(Icons.delete,
                                          color: Colors.red),
                                      label: Text(
                                        context.tr.removeImage,
                                        style:
                                            const TextStyle(color: Colors.red),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }),
                    TextButton(
                      onPressed: () {
                        final newIndex = sectionControllers.value.length;

                        sectionControllers.value = [
                          ...sectionControllers.value,
                          {
                            'title': TextEditingController(text: ''),
                            'description': TextEditingController(text: ''),
                          },
                        ];

                        // Add a null placeholder for the new section's image
                        sectionImages.value = [...sectionImages.value, null];

                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          focusNode.requestFocus();
                        });

                        setState(() {});
                      },
                      child: Row(
                        children: [
                          const Icon(Icons.add).decorated(
                            border: Border.all(
                              color: ColorManager.primaryColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(context.tr.addNewSection),
                        ],
                      ),
                    ),
                  ],
                );
              },
            );
          }),
        ],
      ),
    );
  }
}
