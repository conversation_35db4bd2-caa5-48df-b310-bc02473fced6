import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/screens/plan/repo/plan_repo.dart';
import 'package:connectify_app/src/screens/plan/view/plans_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final planControllerProvider =
    Provider.family<PlanController, BuildContext>((ref, context) {
  final planRepo = ref.watch(planRepoProvider);

  return PlanController(planRepo: planRepo, context: context);
});

final planChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<PlanController, BuildContext>((ref, context) {
  final planRepo = ref.watch(planRepoProvider);

  return PlanController(planRepo: planRepo, context: context);
});

final getPlansDataProvider =
    FutureProvider.family<List<PlanModel>, BuildContext>((ref, context) async {
  final planCtrl = ref.watch(planControllerProvider(context));

  return await planCtrl.getPlans();
});

final getPlansDataByMonthProvider =
    FutureProvider.family<List<PlanModel>, (BuildContext, String monthName)>(
        (ref, params) async {
  final context = params.$1;
  final monthName = params.$2;
  final planCtrl = ref.watch(planControllerProvider(context));

  return await planCtrl.getPlansByMonth(monthName: monthName);
});

final getPlansDataProviderWithPagination =
    FutureProvider.family<List<PlanModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;
    final planCtrl = ref.watch(planControllerProvider(context));

    return await planCtrl.getPlansPaginated(page: page);
  },
);

class PlanController extends BaseVM {
  final PlanRepo planRepo;
  final BuildContext context;

  PlanController({
    required this.planRepo,
    required this.context,
  });

  Future<List<PlanModel>> getPlans() async {
    return await baseFunction(context, () async {
      final planData = await planRepo.getPlans();

      return planData;
    });
  }

  Future<List<PlanModel>> getPlansByMonth({required String monthName}) async {
    return await baseFunction(context, () async {
      final planData = await planRepo.getPlansByMonth(monthName: monthName);

      return planData;
    });
  }

  Future<List<PlanModel>> getPlansPaginated({int page = 1}) async {
    return await baseFunction(context, () async {
      final planData = await planRepo.getPlansPaginated(page: page);

      return planData;
    });
  }

  //? Add Plan ========================================================
  Future<void> addPlan({
    required List<PlanSection> sections,
    required ClassModel? classModel,
    required String pickedImage,
  }) async {
    return await baseFunction(context, () async {
      final plan = PlanModel(
        sections: sections,
        classModel: classModel,
      );

      await planRepo.addPlan(plan: plan, pickedImage: pickedImage);

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const PlansScreen());
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //? Edit Plan ========================================================
  Future<void> editPlan({
    required int id,
    required List<PlanSection> sections,
    required ClassModel? classModel,
    required String pickedImage,
  }) async {
    return await baseFunction(context, () async {
      final plan = PlanModel(
        sections: sections,
        classModel: classModel,
      );

      await planRepo.editPlan(id: id, plan: plan, pickedImage: pickedImage);

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const PlansScreen());
      context.showBarMessage(context.tr.editSuccessfully);
    });
  }

  //? Delete Plan ========================================================
  Future<void> deletePlan({required int id}) async {
    return await baseFunction(context, () async {
      await planRepo.deletePlan(id: id);

      if (!context.mounted) return;
      context.showBarMessage(context.tr.deletedSuccessfully);
      context.toReplacement(const PlansScreen());
    });
  }
}
