import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/screens/announcement/repo/announcement_repo.dart';
import 'package:connectify_app/src/screens/announcement/view/announcements_screen.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final announcementControllerProvider =
    Provider.family<AnnouncementController, BuildContext>((ref, context) {
  final announcementRepo = ref.watch(announcementRepoProvider);

  return AnnouncementController(
      announcementRepo: announcementRepo, context: context);
});

final announcementChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<AnnouncementController, BuildContext>(
        (ref, context) {
  final announcementRepo = ref.watch(announcementRepoProvider);

  return AnnouncementController(
      announcementRepo: announcementRepo, context: context);
});

final getAnnouncementsDataProvider =
    FutureProvider.family<List<AnnouncementModel>, BuildContext>(
        (ref, context) async {
  final announcementCtrl = ref.watch(announcementControllerProvider(context));

  return await announcementCtrl.getAnnouncements();
});

final getAnnouncementsDataProviderWithPagination =
    FutureProvider.family<List<AnnouncementModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;
    final announcementCtrl = ref.watch(announcementControllerProvider(context));

    return await announcementCtrl.getAnnouncementsPaginated(page: page);
  },
);

class AnnouncementController extends BaseVM {
  final AnnouncementRepo announcementRepo;
  final BuildContext context;

  AnnouncementController({
    required this.announcementRepo,
    required this.context,
  });

  Future<List<AnnouncementModel>> getAnnouncements() async {
    return await baseFunction(context, () async {
      final announcementData = await announcementRepo.getAnnouncements();

      return announcementData;
    });
  }

  Future<List<AnnouncementModel>> getAnnouncementsPaginated(
      {int page = 1}) async {
    return await baseFunction(context, () async {
      final announcementData =
          await announcementRepo.getAnnouncementsPaginated(page: page);

      return announcementData;
    });
  }

  //? Add Announcement ========================================================
  Future<void> addAnnouncement({
    required String title,
    required String description,
    required AnnouncementTarget target,
  }) async {
    return await baseFunction(context, () async {
      final announcement = AnnouncementModel(
        title: title,
        description: description,
        target: target,
      );

      await announcementRepo.addAnnouncement(announcement: announcement);

      // Send notification based on target
      String topic = '';

      if (target == AnnouncementTarget.all) {
        if (const UserModel().isConnectifyAdmin) {
          NotificationService.sendNotification(
            title: title,
            body: description,
            userTokenOrTopic: NurseryModelHelper.allAdminsTopic(),
            isTopic: true,
          );
        }
        NotificationService.sendNotification(
          title: title,
          body: description,
          userTokenOrTopic: const UserModel().isConnectifyAdmin
              ? 'teachers'
              : NurseryModelHelper.allTeacherTopic(),
          isTopic: true,
        );
        NotificationService.sendNotification(
          title: title,
          body: description,
          userTokenOrTopic: const UserModel().isConnectifyAdmin
              ? 'parents'
              : NurseryModelHelper.allParentTopic(),
          isTopic: true,
        );

        postNewNotification(
          notificationModel: NotificationModel(
            title: title,
            body: description,
            topic: NurseryModelHelper.allAdminsTopic(),
          ),
        );
        postNewNotification(
          notificationModel: NotificationModel(
            title: title,
            body: description,
            topic: const UserModel().isConnectifyAdmin
                ? 'teachers'
                : NurseryModelHelper.allTeacherTopic(),
          ),
        );
        postNewNotification(
          notificationModel: NotificationModel(
            title: title,
            body: description,
            topic: const UserModel().isConnectifyAdmin
                ? 'parents'
                : NurseryModelHelper.allParentTopic(),
          ),
        );
      } else {
        switch (target) {
          case AnnouncementTarget.admins:
            topic = NurseryModelHelper.allAdminsTopic();
            break;
          case AnnouncementTarget.teachers:
            topic = const UserModel().isConnectifyAdmin
                ? 'teachers'
                : NurseryModelHelper.allTeacherTopic();
            break;
          case AnnouncementTarget.parents:
            topic = const UserModel().isConnectifyAdmin
                ? 'parents'
                : NurseryModelHelper.allParentTopic();
            break;
          case AnnouncementTarget.all:
            break;
        }

        NotificationService.sendNotification(
          title: title,
          body: description,
          userTokenOrTopic: topic,
          isTopic: true,
        );
      }

      postNewNotification(
          notificationModel: NotificationModel(
        title: title,
        body: description,
        topic: topic,
      ));

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const AnnouncementsScreen());
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //? Edit Announcement ========================================================
  Future<void> editAnnouncement({
    required int id,
    required String title,
    required String description,
    required AnnouncementTarget target,
  }) async {
    return await baseFunction(context, () async {
      final announcement = AnnouncementModel(
        title: title,
        description: description,
        target: target,
      );

      await announcementRepo.editAnnouncement(
        id: id,
        announcement: announcement,
      );

      if (!context.mounted) return;
      context.back();
      context.toReplacement(const AnnouncementsScreen());
      context.showBarMessage(context.tr.editSuccessfully);
    });
  }

  //? Delete Announcement ========================================================
  Future<void> deleteAnnouncement({required int id}) async {
    return await baseFunction(context, () async {
      await announcementRepo.deleteAnnouncement(id: id);

      if (!context.mounted) return;
      context.showBarMessage(context.tr.deletedSuccessfully);
    });
  }
}
