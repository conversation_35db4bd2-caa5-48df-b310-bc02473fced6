import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class AnnouncementFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<AnnouncementTarget> selectedTarget;

  const AnnouncementFields({
    super.key,
    required this.controllers,
    required this.selectedTarget,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title field
        BaseTextField(
          title: context.tr.title,
          controller: controllers[ApiStrings.title],
          textInputType: TextInputType.text,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.thisFieldIsRequired;
            }
            return null;
          },
        ),

        context.fieldsGap,

        // Description field
        BaseTextField(
          title: context.tr.description,
          controller: controllers[ApiStrings.description],
          textInputType: TextInputType.multiline,
          maxLines: 5,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return context.tr.thisFieldIsRequired;
            }
            return null;
          },
        ),

        context.fieldsGap,

        // Target dropdown
        Text(
          context.tr.target,
          style: context.labelLarge,
        ),
        context.smallGap,
        ValueListenableBuilder<AnnouncementTarget>(
          valueListenable: selectedTarget,
          builder: (context, target, child) {
            return BaseSearchDropDown(
              label: context.tr.selectTarget,
              data: const UserModel().isConnectifyAdmin
                  ? AnnouncementTarget.values.toList()
                  : AnnouncementTarget.values
                      .where(
                        (element) => element != AnnouncementTarget.admins,
                      )
                      .toList(),
              itemModelAsName: (target) => context.isEng
                  ? (target as AnnouncementTarget).displayName
                  : (target as AnnouncementTarget).displayNameAr,
              selectedValue: target,
              isEng: context.isEng,
              onChanged: (value) {
                selectedTarget.value = value as AnnouncementTarget;
              },
            );
          },
        ),
      ],
    );
  }
}
