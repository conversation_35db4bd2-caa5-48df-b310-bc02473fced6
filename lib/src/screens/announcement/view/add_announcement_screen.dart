import 'package:connectify_app/src/screens/announcement/controller/announcement_controller.dart';
import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/screens/announcement/view/widgets/announcement_fields.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class AddAnnouncementScreen extends HookConsumerWidget {
  final AnnouncementModel? announcement;

  const AddAnnouncementScreen({super.key, this.announcement});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final announcementCtrl =
        ref.watch(announcementChangeNotifierControllerProvider(context));

    final formKey = useState(GlobalKey<FormState>());

    final controllers = {
      ApiStrings.title: useTextEditingController(text: announcement?.title),
      ApiStrings.description:
          useTextEditingController(text: announcement?.description),
    };

    final selectedTarget = useState<AnnouncementTarget>(
        announcement?.target ?? AnnouncementTarget.all);

    Future<void> handleSubmit() async {
      if (formKey.value.currentState?.validate() ?? false) {
        // if (announcement != null) {
        //   // Edit existing announcement
        //   await announcementCtrl.editAnnouncement(
        //     id: announcement!.id!,
        //     title: controllers[ApiStrings.title]!.text,
        //     description: controllers[ApiStrings.description]!.text,
        //     target: selectedTarget.value,
        //   );
        // } else {
        //   // Add new announcement
        await announcementCtrl.addAnnouncement(
          title: controllers[ApiStrings.title]!.text,
          description: controllers[ApiStrings.description]!.text,
          target: selectedTarget.value,
        );
        // }
      }
    }

    return Scaffold(
      appBar: MainAppBar(
        isBackButton: true,
        title: context.tr.addAnnouncement,
        iconPath: '',
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Form(
          key: formKey.value,
          child: Column(
            children: [
              Expanded(
                child: AnnouncementFields(
                  controllers: controllers,
                  selectedTarget: selectedTarget,
                ),
              ),
              context.largeGap,
              Button(
                isLoading: announcementCtrl.isLoading,
                loadingWidget: const LoadingWidget(),
                onPressed: handleSubmit,
                label: context.tr.send,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
