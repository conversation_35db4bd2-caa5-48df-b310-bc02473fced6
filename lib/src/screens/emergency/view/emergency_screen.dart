import 'package:connectify_app/src/screens/emergency/view/widgets/emergency_list.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class EmergencyScreen extends StatelessWidget {
  const EmergencyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.emergency,
        isBackButton: true,
      ),
      body: const EmergencyList(),
    );
  }
}
