import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'emergency_card_widget.dart';

class EmergencyList extends ConsumerWidget {
  const EmergencyList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.watch(getActiveStudents(context));
    return studentCtrl.get(
      data: (student) {
        final filteredStudents =
            student.where((element) => element.id != 0).toList();
        return ListView.separated(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            itemBuilder: (context, index) => EmergencyCardWidget(
                  student: filteredStudents[index],
                ),
            separatorBuilder: (context, index) => context.smallGap,
            itemCount: filteredStudents.length);
      },
    );
  }
}
