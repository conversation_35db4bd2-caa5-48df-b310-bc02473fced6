import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:xr_helper/xr_helper.dart';

class NurseryModelHelper {
  //? Get Nursery ID
  static NurseryModel? currentNursery() {
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    if (nursery == null) return null;

    if (nursery.containsKey('attributes')) {
      return NurseryModel.fromAttributesJson(nursery);
    }

    if (nursery.containsKey('data')) {
      return NurseryModel.fromAttributes<PERSON>son(nursery['data']);
    }

    // final nurseryModel =
    //     nursery['data'] is List ? nursery['data'][0] : nursery['data'];

    return NurseryModel.fromJson(nursery);
  }

  //? Get Nursery ID
  static currentNurseryId() {
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    if (nursery == null) return null;

    int? nurseryId;

    nurseryId = nursery['id'];

    return nurseryId;
  }

  static String allAdminsTopic() {
    return 'admins';
  }

  static String allTeacherTopic() {
    final nurseryData = GetStorageService.getLocalData(key: LocalKeys.nursery);

    return 'teachers${nurseryData?[ApiStrings.id]}';
  }

  static String teacherByIdTopic(int? teacherId) {
    return 'teachers$teacherId';
  }

  static String teacherByClassTopic(int? classId) {
    return 'teachers$classId';
  }

  static String adminTopic() {
    final nurseryData = GetStorageService.getLocalData(key: LocalKeys.nursery);

    return 'admin${nurseryData?[ApiStrings.id]}';
  }

  static String allParentTopic() {
    final nurseryData = GetStorageService.getLocalData(key: LocalKeys.nursery);

    return 'parents${nurseryData?[ApiStrings.id]}';
  }

  static String parentByStudentTopic(int? studentId) {
    // final nurseryData = GetStorageService.getLocalData(key: LocalKeys.nursery);

    return 'parents$studentId';
    // return 'parents${nurseryData?[ApiStrings.id]}$studentId';
  }

  static String allUsersTopic() {
    final nurseryData = GetStorageService.getLocalData(key: LocalKeys.nursery);

    return 'all${nurseryData?[ApiStrings.id]}';
  }
}
