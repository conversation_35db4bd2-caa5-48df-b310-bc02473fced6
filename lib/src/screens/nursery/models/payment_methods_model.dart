import 'package:equatable/equatable.dart';

import '../../../shared/data/remote/api_strings.dart';

class PaymentMethodsModel extends Equatable {
  final String? instapay;
  final String? vodafoneCash;
  final String? etisalatCash;
  final String? weCash;
  final String? orangeCash;

  const PaymentMethodsModel({
    this.instapay,
    this.vodafoneCash,
    this.etisalatCash,
    this.weCash,
    this.orangeCash,
  });

  factory PaymentMethodsModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const PaymentMethodsModel();
    
    return PaymentMethodsModel(
      instapay: json[ApiStrings.instapay],
      vodafoneCash: json[ApiStrings.vodafoneCash],
      etisalatCash: json[ApiStrings.etisalatCash],
      weCash: json[ApiStrings.weCash],
      orangeCash: json[ApiStrings.orangeCash],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.instapay: instapay,
      ApiStrings.vodafoneCash: vodafoneCash,
      ApiStrings.etisalatCash: etisalatCash,
      ApiStrings.weCash: weCash,
      ApiStrings.orangeCash: orangeCash,
    };
  }

  PaymentMethodsModel copyWith({
    String? instapay,
    String? vodafoneCash,
    String? etisalatCash,
    String? weCash,
    String? orangeCash,
  }) {
    return PaymentMethodsModel(
      instapay: instapay ?? this.instapay,
      vodafoneCash: vodafoneCash ?? this.vodafoneCash,
      etisalatCash: etisalatCash ?? this.etisalatCash,
      weCash: weCash ?? this.weCash,
      orangeCash: orangeCash ?? this.orangeCash,
    );
  }

  factory PaymentMethodsModel.empty() => const PaymentMethodsModel();

  @override
  List<Object?> get props => [
        instapay,
        vodafoneCash,
        etisalatCash,
        weCash,
        orangeCash,
      ];
}
