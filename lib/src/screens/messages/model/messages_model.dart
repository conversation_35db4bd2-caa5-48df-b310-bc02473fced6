import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<MessageModel> responseToMessageModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final messageData = data.map((e) => MessageModel.fromJson(e)).toList();

  return messageData;
}

class MessageModel extends Equatable {
  final int? id;
  final String? title;
  final String? description;
  final StudentModel? student;
  final TeacherModel? teacher;
  final UserModel? admin;
  final DateTime? createdAt;

  const MessageModel({
    this.id,
    this.title,
    this.description,
    this.student,
    this.teacher,
    this.admin,
    this.createdAt,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return MessageModel(
      id: json[ApiStrings.id],
      title: attributes[ApiStrings.title],
      description: attributes[ApiStrings.description],
      student: attributes[ApiStrings.student][ApiStrings.data] != null
          ? StudentModel.fromJson(
              attributes[ApiStrings.student][ApiStrings.data])
          : null,
      teacher: attributes[ApiStrings.teacher][ApiStrings.data] != null
          ? TeacherModel.fromJson(
              attributes[ApiStrings.teacher][ApiStrings.data])
          : null,
      admin: attributes[ApiStrings.admin][ApiStrings.data] != null
          ? UserModel.fromJson(attributes[ApiStrings.admin][ApiStrings.data])
          : null,
      createdAt: DateTime.parse(attributes[ApiStrings.createdAt]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.title: title,
      ApiStrings.description: description,
      ApiStrings.student: student?.id,
      if (const UserModel().currentUser.isTeacher)
        ApiStrings.teacher: const UserModel().currentUser.id
      else
        ApiStrings.admin: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId()
    };
  }

  @override
  List<Object?> get props => [id, title, description, student, teacher, admin];
}
