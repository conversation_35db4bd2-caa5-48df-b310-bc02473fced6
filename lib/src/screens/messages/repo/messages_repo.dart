import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final messageRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return MessageRepo(networkApiServices);
});

//? ========================================================

class MessageRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  MessageRepo(this._networkApiServices);

//? get Message Data ========================================================
  Future<List<MessageModel>> getMessageData() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.messages);

      final messageData = await compute(responseToMessageModelList, response);

      return messageData;
    });
  }

//? Add Message ========================================================

  Future<void> addMessage({required MessageModel message}) async {
    return await baseFunction(() async {
      await _networkApiServices.postResponse(
        ApiEndpoints.addMessage,
        body: message.toJson(),
      );
    });
  }
}
