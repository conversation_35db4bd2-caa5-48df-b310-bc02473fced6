import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/student_drop_down.dart';
import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

class AddMessageFields extends StatelessWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<StudentModel?> selectedStudent;
  final int? studentId;

  const AddMessageFields({
    super.key,
    required this.controllers,
    required this.selectedStudent,
    required this.studentId,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // if (showStudentDropDown) ...[
        StudentDropDown(selectedStudent: selectedStudent, studentId: studentId),
        context.largeGap,
        // ],

        //! Name
        BaseTextField(
          controller: controllers[ApiStrings.title],
          title: context.tr.title,
          textInputType: TextInputType.text,
        ),

        context.largeGap,

        //! Description
        BaseTextField(
          controller: controllers[ApiStrings.description],
          title: context.tr.message,
          textInputType: TextInputType.text,
          maxLines: 6,
        ),
      ],
    );
  }
}
