import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/messages/controller/messages_controller.dart';
import 'package:connectify_app/src/screens/messages/view/view/widgets/add_message/add_message_fields.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class AddMessageDialog extends HookConsumerWidget {
  final Widget navigateWidget;

  const AddMessageDialog({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return AddRectangleWidget(onTap: () {
      showAddMessageDialog(
        context,
      ).then((value) {
        mediaController.clearFiles();
      });
    });
  }
}

Future<void> showAddMessageDialog(BuildContext context,
    {int? studentId}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final controllers = {
              ApiStrings.title: useTextEditingController(),
              ApiStrings.description: useTextEditingController(),
            };
            final formKey = useState(GlobalKey<FormState>());

            final selectedStudent = useState<StudentModel?>(null);

            final messagesController =
                ref.watch(messageChangeNotifierControllerProvider(context));

            final bottomNavBarController =
                ref.watch(bottomNavControllerProvider);

            void addMessage() async {
              if (formKey.value.currentState!.validate()) {
                await messagesController.addMessage(
                  title: controllers[ApiStrings.title]!.text,
                  description: controllers[ApiStrings.description]!.text,
                  student: selectedStudent.value,
                );

                bottomNavBarController.changeIndex(3);

                context.toReplacement(const MainScreen());

                context.showBarMessage(context.tr.messageSentSuccessfully);
              }
            }

            return AlertDialogWidget(
                isImage: false,
                header: context.tr.sendANewMessage,
                isLoading: messagesController.isLoading,
                child: Form(
                  key: formKey.value,
                  child: AddMessageFields(
                    studentId: studentId,
                    selectedStudent: selectedStudent,
                    controllers: controllers,
                  ),
                ),
                onConfirm: () async {
                  addMessage();
                  // await addAndEditClass();
                });
          },
        );
      });
}
