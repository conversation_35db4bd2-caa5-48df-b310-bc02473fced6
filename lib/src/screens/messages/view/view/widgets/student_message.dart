import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/screens/messages/view/view/widgets/add_message/add_message.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/consts/app_constants.dart';
import '../../../../../shared/widgets/base_container.dart';
import '../../../../../shared/widgets/shared_widgets.dart';
import '../../../../student/models/student_model.dart';

class StudentMessages extends ConsumerWidget {
  final StudentModel student;
  final List<MessageModel> messages;

  const StudentMessages({
    super.key,
    required this.student,
    required this.messages,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final messagesList = messages.map((message) {
      return BaseContainer(
        boxShadow: ConstantsWidgets.boxShadow,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  message.title ?? '',
                  style: context.subTitle.copyWith(fontWeight: FontWeight.bold),
                )),
                Text(
                  message.createdAt.formatDateToTimeAndString ?? '',
                  style: context.labelMedium,
                ),
              ],
            ),
            context.mediumGap,
            Text(
              message.description ?? '',
              style: context.subTitle,
            )
          ],
        ),
      ).paddingSymmetric(
        vertical: AppSpaces.smallPadding,
      );
    }).toList();
    return Scaffold(
      appBar: MainAppBar(
        title: student.name,
        isBackButton: true,
      ),
      body: ListView(
        children: [
          BaseContainer(
            padding: AppSpaces.smallPadding,
            boxShadow: ConstantsWidgets.boxShadow,
            child: ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: Row(
                children: [
                  Container(
                    height: 30.h,
                    width: 35.w,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: BaseCachedImage(student.image?.url ?? '',
                          fit: BoxFit.cover,
                          errorWidget: const BaseCachedImage(
                            AppConsts.studentPlaceholder,
                          )),
                    ),
                  ),
                  context.mediumGap,
                  Text(
                    student.name ?? '',
                    style: context.blueHint.copyWith(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
          context.smallGap,
          ...List.generate(messagesList.length, (index) => messagesList[index]),
        ],
      ).paddingAll(AppSpaces.mediumPadding),
      bottomNavigationBar: Button(
          label: context.tr.sendANewMessageTo(student.name),
          onPressed: () {
            showAddMessageDialog(
              studentId: student.id,
              context,
            );
          }).paddingAll(AppSpaces.mediumPadding),
    );
  }
}
