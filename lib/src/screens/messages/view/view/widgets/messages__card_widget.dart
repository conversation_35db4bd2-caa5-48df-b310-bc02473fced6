import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/screens/messages/view/view/widgets/student_message.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/consts/app_constants.dart';
import '../../../../../shared/widgets/base_container.dart';
import '../../../../../shared/widgets/shared_widgets.dart';

class MessagesCardWidget extends StatelessWidget {
  final StudentModel student;
  final List<MessageModel> messages;

  const MessagesCardWidget({
    super.key,
    required this.student,
    required this.messages,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      onTap: () => context.to(StudentMessages(
        student: student,
        messages: messages,
      )),
      padding: AppSpaces.smallPadding,
      boxShadow: ConstantsWidgets.boxShadow,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: Row(
          children: [
            Container(
              height: 30.h,
              width: 35.w,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: BaseCachedImage(student.image?.url ?? '',
                    fit: BoxFit.cover,
                    errorWidget: const BaseCachedImage(
                      AppConsts.studentPlaceholder,
                    )),
              ),
            ),
            context.mediumGap,
            Text(
              student.name ?? '',
              style: context.blueHint.copyWith(fontSize: 16),
            ),
            const Spacer(),
            Text(
              '(${messages.length})',
              style: context.blueHint.copyWith(
                  fontWeight: FontWeight.w400,
                  color: ColorManager.primaryColor),
            ),
            context.smallGap,
            const Icon(
              Icons.arrow_forward_ios_outlined,
              size: 15,
            )
          ],
        ),
      ),
    );
  }
}
