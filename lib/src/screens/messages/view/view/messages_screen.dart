import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/add_message/add_message.dart';
import 'widgets/messages_list.dart';

class MessagesScreen extends StatelessWidget {
  const MessagesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Button(
          label: context.tr.sendANewMessage,
          onPressed: () => showAddMessageDialog(
                context,
              )).paddingAll(AppSpaces.mediumPadding),
      body: const MessagesList(),
    );
  }
}
