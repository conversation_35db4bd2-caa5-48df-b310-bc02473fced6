import 'package:connectify_app/src/screens/messages/repo/messages_repo.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/messages_model.dart';

final messageControllerProvider =
    Provider.family<MessageController, BuildContext>((ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);

  return MessageController(messageRepo: messageRepo, context: context);
});
final messageChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<MessageController, BuildContext>(
        (ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);

  return MessageController(messageRepo: messageRepo, context: context);
});

final getAllMessageData =
    FutureProvider.family<List<MessageModel>, BuildContext>((ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);

  final messageController =
      MessageController(messageRepo: messageRepo, context: context);

  return messageController.getMessageData();
});

class MessageController extends BaseVM {
  final MessageRepo messageRepo;
  final BuildContext context;

  MessageController({required this.messageRepo, required this.context});

  Future<List<MessageModel>> getMessageData() async {
    return await baseFunction(context, () async {
      final messageData = await messageRepo.getMessageData();

      return messageData;
    });
  }

  //? Add Message ========================================================
  Future<void> addMessage(
      {required String title,
      required String description,
      required StudentModel? student}) async {
    return await baseFunction(context, () async {
      final message = MessageModel(
        student: student,
        title: title,
        description: description,
      );

      await messageRepo.addMessage(message: message);

      NotificationService.sendNotification(
        title: "New Message",
        body:
            "You have a new message for ${student?.name}.\n(Body : $description)",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "New Message",
        body:
            "You have a new message for ${student?.name}.\n(Body : $description)",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }
}
