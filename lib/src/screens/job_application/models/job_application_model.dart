import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';

class JobApplicationModel {
  final int? id;
  final String name;
  final DateTime? birthDate;
  final String city;
  final String jobTitle;
  final String education;
  final double expectedSalary;
  final BaseMediaModel? cv;
  final BaseMediaModel? image;
  final DateTime? createdAt;

  const JobApplicationModel({
    this.id,
    required this.name,
    this.birthDate,
    required this.city,
    required this.jobTitle,
    required this.education,
    required this.expectedSalary,
    this.cv,
    this.image,
    this.createdAt,
  });

  factory JobApplicationModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.image] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.image])
        : null;

    final cv = json[ApiStrings.cv] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.cv])
        : null;

    return JobApplicationModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.name] ?? '',
      birthDate: json[ApiStrings.birthDate] != null
          ? DateTime.tryParse(json[ApiStrings.birthDate])
          : null,
      city: json[ApiStrings.city] ?? '',
      jobTitle: json[ApiStrings.jobTitle] ?? '',
      education: json[ApiStrings.education] ?? '',
      expectedSalary: (json[ApiStrings.expectedSalary] ?? 0).toDouble(),
      cv: cv,
      image: image,
      createdAt: json[ApiStrings.createdAt] != null
          ? DateTime.tryParse(json[ApiStrings.createdAt])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.name: name,
      if (birthDate != null) ApiStrings.birthDate: birthDate!.toIso8601String(),
      ApiStrings.city: city,
      ApiStrings.jobTitle: jobTitle,
      ApiStrings.education: education,
      ApiStrings.expectedSalary: expectedSalary,
    };
  }

  JobApplicationModel copyWith({
    int? id,
    String? name,
    DateTime? birthDate,
    String? city,
    String? jobTitle,
    String? education,
    double? expectedSalary,
    BaseMediaModel? cv,
    BaseMediaModel? image,
    DateTime? createdAt,
  }) {
    return JobApplicationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      birthDate: birthDate ?? this.birthDate,
      city: city ?? this.city,
      jobTitle: jobTitle ?? this.jobTitle,
      education: education ?? this.education,
      expectedSalary: expectedSalary ?? this.expectedSalary,
      cv: cv ?? this.cv,
      image: image ?? this.image,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JobApplicationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'JobApplicationModel(id: $id, name: $name, city: $city, jobTitle: $jobTitle)';
  }
}

// Helper function to convert response to JobApplicationModel list
List<JobApplicationModel> responseToJobApplicationModelList(
    Map<String, dynamic> response) {
  final List<dynamic> data = response[ApiStrings.data] ?? [];
  return data
      .map((json) => JobApplicationModel.fromJson(json[ApiStrings.attributes]))
      .toList();
}
