import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/job_application/controllers/job_application_controller.dart';
import 'package:connectify_app/src/screens/job_application/view/job_applications_screen/widgets/city_filter_dropdown.dart';
import 'package:connectify_app/src/screens/job_application/view/job_applications_screen/widgets/job_applications_grid_view.dart';
import 'package:connectify_app/src/screens/job_application/view/job_applications_screen/widgets/job_title_filter_dropdown.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class JobApplicationsScreen extends HookConsumerWidget {
  const JobApplicationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final jobApplicationController = ref.watch(getJobApplicationDataProvider(context));
    final jobApplicationCtrl = ref.watch(jobApplicationProviderController(context));
    
    final selectedCity = useState<String?>(null);
    final selectedJobTitle = useState<String?>(null);

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          appBar: MainAppBar(title: context.tr.jobApplications, isBackButton: true),
          body: ListView(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            children: [
              jobApplicationController.get(
                data: (jobApplications) {
                  // Get unique cities and job titles for filters
                  final cities = jobApplicationCtrl.getUniqueCities(jobApplications);
                  final jobTitles = jobApplicationCtrl.getUniqueJobTitles(jobApplications);
                  
                  // Filter job applications based on selected filters
                  final filteredJobApplications = jobApplicationCtrl.filterJobApplications(
                    jobApplications: jobApplications,
                    selectedCity: selectedCity.value,
                    selectedJobTitle: selectedJobTitle.value,
                  );

                  return Column(
                    children: [
                      // Filters Section
                      if (cities.isNotEmpty || jobTitles.isNotEmpty) ...[
                        Row(
                          children: [
                            // City Filter
                            if (cities.isNotEmpty)
                              Expanded(
                                child: CityFilterDropdown(
                                  cities: cities,
                                  selectedCity: selectedCity,
                                ),
                              ),
                            if (cities.isNotEmpty && jobTitles.isNotEmpty)
                              context.mediumGap,
                            // Job Title Filter
                            if (jobTitles.isNotEmpty)
                              Expanded(
                                child: JobTitleFilterDropdown(
                                  jobTitles: jobTitles,
                                  selectedJobTitle: selectedJobTitle,
                                ),
                              ),
                          ],
                        ),
                        context.largeGap,
                      ],
                      
                      // Job Applications Grid
                      JobApplicationsGridView(
                        jobApplications: filteredJobApplications,
                        navigateWidget: const JobApplicationsScreen(),
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
