import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class JobTitleFilterDropdown extends HookConsumerWidget {
  final List<String> jobTitles;
  final ValueNotifier<String?> selectedJobTitle;

  const JobTitleFilterDropdown({
    super.key,
    required this.jobTitles,
    required this.selectedJobTitle,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseSearchDropDown(
      label: context.tr.jobTitle,
      data: jobTitles,
      itemModelAsName: (jobTitle) => jobTitle,
      selectedValue: selectedJobTitle.value,
      isEng: context.isEng,
      onChanged: (value) {
        selectedJobTitle.value = value;
      },
    );
  }
}
