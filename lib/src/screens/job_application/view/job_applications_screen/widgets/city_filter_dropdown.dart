import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class CityFilterDropdown extends HookConsumerWidget {
  final List<String> cities;
  final ValueNotifier<String?> selectedCity;

  const CityFilterDropdown({
    super.key,
    required this.cities,
    required this.selectedCity,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseSearchDropDown(
      label: context.tr.city,
      data: cities,
      itemModelAsName: (city) => city,
      selectedValue: selectedCity.value,
      isEng: context.isEng,
      onChanged: (value) {
        selectedCity.value = value;
      },
    );
  }
}
